require('dotenv').config();
const imageService = require('./src/services/image.service');

async function testImageGeneration() {
  console.log('🖼️ TESTING IMAGE GENERATION');
  console.log('============================');
  
  const testCases = [
    {
      description: "Professional solar panel installation on residential roof",
      keyword: "solar panel installation"
    },
    {
      description: "Modern solar energy system with battery storage",
      keyword: "solar energy system"
    },
    {
      description: "Solar technician installing panels on commercial building",
      keyword: "commercial solar installation"
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n🧪 Test ${i + 1}: ${testCase.description}`);
    console.log(`🎯 Keyword: ${testCase.keyword}`);
    
    try {
      const result = await imageService.generateImageWithGemini(
        testCase.description,
        testCase.keyword
      );
      
      console.log('✅ Image generation successful!');
      console.log('📸 Image URL:', result.imageUrl);
      console.log('🏷️ Alt text:', result.alt);
      console.log('📝 Title:', result.title);
      console.log('🎨 Enhanced prompt:', result.enhancedPrompt.substring(0, 100) + '...');
      console.log('🕒 Generated at:', result.generatedAt);
      console.log('🆔 Image ID:', result.imageId);
      
    } catch (error) {
      console.error('❌ Image generation failed:', error.message);
    }
  }
  
  console.log('\n🎉 IMAGE GENERATION TEST COMPLETED!');
}

testImageGeneration().catch(console.error);
