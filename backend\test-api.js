// Simple test script to verify API endpoints
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testHealthCheck() {
  try {
    const response = await axios.get('http://localhost:5000/health');
    console.log('✅ Health check:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return false;
  }
}

async function testBlogRoutes() {
  try {
    const response = await axios.get(`${API_BASE}/blog/test`);
    console.log('✅ Blog routes test:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Blog routes test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testRegenerateBlock() {
  try {
    const testData = {
      draftId: 'test_draft',
      blockId: 'test_block',
      regenerationType: 'ai',
      customPrompt: 'Test prompt'
    };

    const response = await axios.post(`${API_BASE}/blog/regenerate-block`, testData);
    console.log('✅ Regenerate block test:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Regenerate block test failed:', error.response?.data || error.message);
    return false;
  }
}

async function testGenerateImage() {
  try {
    const testData = {
      draftId: 'test_draft',
      blockId: 'test_block',
      description: 'Test image description'
    };

    const response = await axios.post(`${API_BASE}/blog/generate-image`, testData);
    console.log('✅ Generate image test:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Generate image test failed:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Testing API endpoints...\n');

  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ Server not responding, stopping tests');
    return;
  }

  console.log('\n📝 Testing blog routes...');
  const blogRoutesOk = await testBlogRoutes();
  if (!blogRoutesOk) {
    console.log('❌ Blog routes not working, stopping tests');
    return;
  }

  console.log('\n📝 Testing blog endpoints...');
  await testRegenerateBlock();
  await testGenerateImage();

  console.log('\n✅ Tests completed');
}

runTests();
