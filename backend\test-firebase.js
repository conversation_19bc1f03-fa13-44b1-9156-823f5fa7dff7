require('dotenv').config();
const { initializeFirebase, getDb } = require('./src/config/firebase.config');
const draftService = require('./src/services/draft.service');

async function testFirebaseIntegration() {
  console.log('🔥 TESTING FIREBASE INTEGRATION\n');
  console.log('=' .repeat(50));
  
  try {
    // Initialize Firebase
    console.log('📝 Step 1: Initializing Firebase...');
    const db = initializeFirebase();
    
    if (db) {
      console.log('✅ Firebase initialized successfully');
      console.log('🔥 Database connection established');
    } else {
      console.log('⚠️  Firebase not initialized - using fallback storage');
    }
    
    // Test draft creation
    console.log('\n📝 Step 2: Testing draft creation...');
    const testDraft = {
      userId: 'test_user_firebase',
      companyData: {
        companyName: 'Test Company',
        industry: 'Solar Energy'
      },
      selectedKeyword: 'solar panels',
      status: 'draft'
    };
    
    const createdDraft = await draftService.createDraft(testDraft);
    console.log('✅ Draft created successfully');
    console.log(`📋 Draft ID: ${createdDraft.id}`);
    
    // Test draft retrieval
    console.log('\n📝 Step 3: Testing draft retrieval...');
    const retrievedDraft = await draftService.getDraft(createdDraft.id);
    console.log('✅ Draft retrieved successfully');
    console.log(`📋 Retrieved draft for: ${retrievedDraft.companyData.companyName}`);
    
    // Test draft update
    console.log('\n📝 Step 4: Testing draft update...');
    const updateData = {
      status: 'meta_generation',
      selectedKeyword: 'solar panel installation',
      competitorAnalysis: {
        competitors: ['SunPower', 'Tesla Solar'],
        commonH2Topics: ['Installation Process', 'Cost Analysis']
      }
    };
    
    const updatedDraft = await draftService.updateDraft(createdDraft.id, updateData);
    console.log('✅ Draft updated successfully');
    console.log(`📋 New status: ${updatedDraft.status}`);
    console.log(`📋 Updated keyword: ${updatedDraft.selectedKeyword}`);
    
    // Test draft listing
    console.log('\n📝 Step 5: Testing draft listing...');
    const draftList = await draftService.listDrafts('test_user_firebase', { limit: 10 });
    console.log('✅ Draft listing successful');
    console.log(`📋 Found ${draftList.length} drafts for user`);
    
    // Test draft deletion
    console.log('\n📝 Step 6: Testing draft deletion...');
    const deleteResult = await draftService.deleteDraft(createdDraft.id);
    console.log('✅ Draft deleted successfully');
    
    // Verify deletion
    try {
      await draftService.getDraft(createdDraft.id);
      console.log('❌ Draft should have been deleted');
    } catch (error) {
      console.log('✅ Draft deletion verified - draft not found');
    }
    
    console.log('\n🎉 FIREBASE INTEGRATION TEST COMPLETED!');
    console.log('=' .repeat(50));
    console.log('✅ All Firebase operations working correctly');
    console.log('✅ Drafts can be stored and retrieved from Firebase');
    console.log('✅ Fallback to in-memory storage works when Firebase unavailable');
    console.log('✅ Your blog generation workflow now has persistent storage!');
    
  } catch (error) {
    console.error('❌ Firebase integration test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check Firebase credentials in .env file');
    console.log('2. Verify Firebase project exists and is accessible');
    console.log('3. Ensure Firestore is enabled in Firebase console');
    console.log('4. Check network connectivity');
    console.log('\n💡 The system will fall back to in-memory storage if Firebase is unavailable');
  }
}

// Run the test
testFirebaseIntegration();
