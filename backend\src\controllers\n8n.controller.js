// N8N Integration Controller - Handles webhooks and data sync from N8N workflows
const googleSheetsService = require('../services/googleSheets.service');
const draftService = require('../services/draft.service');

class N8nController {
  // Sync company data from N8N workflow
  async syncCompanies(req, res) {
    try {
      const { companies } = req.body;
      
      if (!companies || !Array.isArray(companies)) {
        return res.status(400).json({ error: 'Companies array is required' });
      }

      // Store companies in your database/cache
      // For now, we'll just validate and respond
      const validCompanies = companies.filter(company => 
        company.companyName && company.servicesOffered
      );

      console.log(`N8N: Synced ${validCompanies.length} companies`);
      
      res.json({
        success: true,
        synced: validCompanies.length,
        message: 'Companies synced successfully'
      });

    } catch (error) {
      console.error('Error syncing companies from N8N:', error);
      res.status(500).json({ error: 'Failed to sync companies' });
    }
  }

  // Sync keywords data from N8N workflow
  async syncKeywords(req, res) {
    try {
      const { keywords, companyId } = req.body;
      
      if (!keywords || !Array.isArray(keywords)) {
        return res.status(400).json({ error: 'Keywords array is required' });
      }

      // Process and categorize keywords
      const manualKeywords = keywords.filter(k => k.source === 'manual');
      const autoKeywords = keywords.filter(k => k.source === 'ai' || k.source === 'auto');

      console.log(`N8N: Synced ${keywords.length} keywords for company ${companyId}`);
      
      res.json({
        success: true,
        manual: manualKeywords.length,
        auto: autoKeywords.length,
        total: keywords.length,
        message: 'Keywords synced successfully'
      });

    } catch (error) {
      console.error('Error syncing keywords from N8N:', error);
      res.status(500).json({ error: 'Failed to sync keywords' });
    }
  }

  // Generate content via N8N workflow
  async generateContent(req, res) {
    try {
      const { draftId, keyword, companyData, contentType } = req.body;
      
      if (!draftId || !keyword) {
        return res.status(400).json({ error: 'Draft ID and keyword are required' });
      }

      // This would trigger an N8N workflow for content generation
      // For now, return a webhook URL that N8N can call back
      const webhookUrl = `${req.protocol}://${req.get('host')}/api/n8n/content-callback`;
      
      res.json({
        success: true,
        webhookUrl,
        draftId,
        message: 'Content generation initiated. N8N will callback with results.'
      });

    } catch (error) {
      console.error('Error initiating content generation:', error);
      res.status(500).json({ error: 'Failed to initiate content generation' });
    }
  }

  // Callback from N8N with generated content
  async contentCallback(req, res) {
    try {
      const { draftId, content, blocks, images, citations } = req.body;
      
      if (!draftId || !content) {
        return res.status(400).json({ error: 'Draft ID and content are required' });
      }

      // Update draft with N8N generated content
      await draftService.updateDraft(draftId, {
        content,
        blocks,
        images,
        citations,
        status: 'content_ready',
        generatedAt: new Date().toISOString()
      });

      console.log(`N8N: Content generated for draft ${draftId}`);
      
      res.json({
        success: true,
        message: 'Content updated successfully'
      });

    } catch (error) {
      console.error('Error processing N8N content callback:', error);
      res.status(500).json({ error: 'Failed to process content callback' });
    }
  }

  // Health check for N8N workflows
  async healthCheck(req, res) {
    try {
      res.json({
        success: true,
        timestamp: new Date().toISOString(),
        service: 'ArticleScribe N8N Integration',
        status: 'healthy'
      });
    } catch (error) {
      res.status(500).json({ error: 'Health check failed' });
    }
  }

  // Get current data status for N8N dashboard
  async getDataStatus(req, res) {
    try {
      // Get counts of companies, keywords, drafts
      const companies = await googleSheetsService.getCompanyData();
      
      res.json({
        success: true,
        data: {
          companies: companies.length,
          lastSync: new Date().toISOString(),
          status: 'active'
        }
      });

    } catch (error) {
      console.error('Error getting data status:', error);
      res.status(500).json({ error: 'Failed to get data status' });
    }
  }
}

module.exports = new N8nController();
