const { GoogleGenerativeAI } = require('@google/generative-ai');

class GoogleVisionService {
  constructor() {
    // Use GOOGLE_API_KEY for Google services, GEMINI_API_KEY for Gemini
    const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.visionModel = this.genAI.getGenerativeModel({
      model: 'gemini-1.5-pro-vision',
      generationConfig: {
        temperature: 0.8,
        topK: 32,
        topP: 0.95,
        maxOutputTokens: 4096,
      }
    });
  }

  async generateImageDescription(keyword, h1Title, companyData, imageType = 'feature') {
    try {
      const prompt = `
        Create a detailed, professional image description for a ${imageType} image for a blog post.
        
        Blog Context:
        - H1 Title: ${h1Title}
        - Primary Keyword: ${keyword}
        - Company: ${companyData.companyName}
        - Industry: Solar Energy
        - Image Type: ${imageType}
        
        Requirements:
        1. The image should visually support the H1 title and keyword
        2. Professional, high-quality appearance
        3. Relevant to solar industry and ${companyData.companyName}
        4. Modern, clean aesthetic
        5. Include specific visual elements that relate to the topic
        
        Return JSON format:
        {
          "description": "Detailed description of what the image should show",
          "alt": "SEO-optimized alt text",
          "title": "Image title",
          "style": "Photography style (e.g., professional, modern, clean)",
          "elements": ["element1", "element2", "element3"],
          "prompt": "Enhanced prompt for image generation"
        }
      `;

      const result = await this.visionModel.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Vision service parsing error:', parseError);
      }

      // Fallback
      return {
        description: `Professional ${imageType} image showing ${keyword} related to ${companyData.companyName} solar services`,
        alt: `${keyword} - ${companyData.companyName}`,
        title: `Professional ${keyword} Guide`,
        style: "professional, modern, clean",
        elements: [keyword, "solar panels", "professional setting"],
        prompt: `Professional ${imageType} image showing ${keyword}, modern solar installation, clean aesthetic, high quality photography`
      };

    } catch (error) {
      console.error('Error generating image description:', error);
      throw error;
    }
  }

  async enhanceImagePrompt(basicDescription, keyword, context) {
    try {
      const prompt = `
        Enhance this image description for professional image generation:
        
        Basic Description: ${basicDescription}
        Keyword Context: ${keyword}
        Additional Context: ${context}
        
        Create an enhanced, detailed prompt that will generate a high-quality, professional image.
        Focus on:
        - Professional photography style
        - High resolution and quality
        - Relevant visual elements
        - Modern, clean aesthetic
        - Industry-appropriate imagery
        
        Return only the enhanced prompt text, no JSON.
      `;

      const result = await this.visionModel.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();

    } catch (error) {
      console.error('Error enhancing image prompt:', error);
      return basicDescription; // Fallback to basic description
    }
  }

  async generateMultipleImageDescriptions(keyword, h1Title, companyData, imageTypes = ['feature', 'section', 'section']) {
    try {
      const descriptions = [];
      
      for (let i = 0; i < imageTypes.length; i++) {
        const imageType = imageTypes[i];
        const description = await this.generateImageDescription(keyword, h1Title, companyData, imageType);
        descriptions.push({
          ...description,
          type: imageType,
          index: i
        });
      }
      
      return descriptions;
    } catch (error) {
      console.error('Error generating multiple image descriptions:', error);
      throw error;
    }
  }
}

module.exports = new GoogleVisionService();
