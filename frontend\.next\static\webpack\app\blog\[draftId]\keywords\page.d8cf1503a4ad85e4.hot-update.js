"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[draftId]/keywords/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:5000/api\";\nclass ApiClient {\n    async request(endpoint, options) {\n        const url = \"\".concat(API_BASE_URL).concat(endpoint);\n        const headers = {};\n        // Only add Content-Type for non-FormData requests\n        if (!((options === null || options === void 0 ? void 0 : options.body) instanceof FormData)) {\n            headers[\"Content-Type\"] = \"application/json\";\n        }\n        const response = await fetch(url, {\n            headers: {\n                ...headers,\n                ...options === null || options === void 0 ? void 0 : options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(\"API request failed: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    // Companies\n    async getCompanies() {\n        return this.request(\"/company\");\n    }\n    // Blog workflow\n    async startBlog(companyId, userId) {\n        return this.request(\"/blog/start\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                companyId,\n                userId\n            })\n        });\n    }\n    async selectKeywordAnalyze(draftId, selectedKeyword, alternativeKeywords) {\n        return this.request(\"/blog/select-keyword-analyze\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                selectedKeyword,\n                alternativeKeywords\n            })\n        });\n    }\n    async generateMetaScores(draftId) {\n        return this.request(\"/blog/generate-meta-scores\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async selectMeta(draftId, selectedMetaIndex) {\n        return this.request(\"/blog/select-meta\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                selectedMetaIndex\n            })\n        });\n    }\n    async regenerateMeta(draftId, optionIndex) {\n        return this.request(\"/blog/regenerate-meta\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                optionIndex\n            })\n        });\n    }\n    async generateStructuredContent(draftId) {\n        return this.request(\"/blog/generate-structured-content\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async regenerateBlock(draftId, blockId, regenerationType, customPrompt, newContent) {\n        return this.request(\"/blog/regenerate-block\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                blockId,\n                regenerationType,\n                customPrompt,\n                newContent\n            })\n        });\n    }\n    async generateImage(draftId, blockId, description) {\n        return this.request(\"/blog/generate-image\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                blockId,\n                description\n            })\n        });\n    }\n    async generateAIImage(prompt, keyword) {\n        return this.request(\"/blog/generate-ai-image\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                prompt,\n                keyword\n            })\n        });\n    }\n    async uploadImage(draftId, blockId, file) {\n        const formData = new FormData();\n        formData.append(\"draftId\", draftId);\n        formData.append(\"blockId\", blockId);\n        formData.append(\"file\", file);\n        return this.request(\"/blog/upload-image\", {\n            method: \"POST\",\n            body: formData,\n            headers: {}\n        });\n    }\n    async generateLinks(draftId) {\n        return this.request(\"/blog/generate-links\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async deployWordPress(draftId, wordpressConfig) {\n        return this.request(\"/blog/deploy-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                wordpressConfig\n            })\n        });\n    }\n    // Draft management\n    async getDraft(draftId) {\n        return this.request(\"/blog/draft/\".concat(draftId));\n    }\n    async listDrafts(userId) {\n        const params = userId ? \"?userId=\".concat(userId) : \"\";\n        return this.request(\"/blog/drafts\".concat(params));\n    }\n    // WordPress\n    async testWordPress() {\n        return this.request(\"/blog/test-wordpress\", {\n            method: \"POST\"\n        });\n    }\n    async previewWordPress(draftId) {\n        return this.request(\"/blog/preview-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    // Draft management - additional methods\n    async deleteDraft(draftId) {\n        return this.request(\"/blog/draft/\".concat(draftId), {\n            method: \"DELETE\"\n        });\n    }\n    async saveDraft(draftId, updates) {\n        return this.request(\"/blog/save-draft\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                updates\n            })\n        });\n    }\n    // WordPress deployment\n    async deployToWordPress(draftId, options) {\n        return this.request(\"/blog/deploy-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                postStatus: options.postStatus\n            })\n        });\n    }\n}\nconst api = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});