"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[draftId]/editor/page",{

/***/ "(app-pages-browser)/./app/blog/[draftId]/editor/page.tsx":
/*!********************************************!*\
  !*** ./app/blog/[draftId]/editor/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_stepper_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/stepper-header */ \"(app-pages-browser)/./components/stepper-header.tsx\");\n/* harmony import */ var _components_content_block__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/content-block */ \"(app-pages-browser)/./components/content-block.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    const [draft, setDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [blocks, setBlocks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatingLinks, setGeneratingLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingBlock, setEditingBlock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editContent, setEditContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const draftId = params.draftId;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            loadDraftAndContent();\n        }\n    }[\"EditorPage.useEffect\"], [\n        draftId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // Calculate total word count\n            const total = blocks.reduce({\n                \"EditorPage.useEffect.total\": (sum, block)=>sum + (block.wordCount || 0)\n            }[\"EditorPage.useEffect.total\"], 0);\n            setWordCount(total);\n        }\n    }[\"EditorPage.useEffect\"], [\n        blocks\n    ]);\n    const loadDraftAndContent = async ()=>{\n        try {\n            // Load draft data first\n            const draftResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.getDraft(draftId);\n            if (draftResponse.success && draftResponse.data) {\n                const draftData = draftResponse.data;\n                setDraft(draftData);\n                // Check if structured content already exists\n                if (draftData.structuredContent && draftData.structuredContent.length > 0) {\n                    setBlocks(draftData.structuredContent);\n                } else {\n                    // Generate new structured content\n                    await generateContent();\n                }\n            }\n        } catch (error) {\n            toast({\n                title: \"Error loading draft\",\n                description: \"Failed to load draft data. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const generateContent = async ()=>{\n        try {\n            // Call backend API to generate structured content\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateStructuredContent(draftId);\n            if (response.success && response.structuredContent) {\n                setBlocks(response.structuredContent);\n                return;\n            }\n            // Fallback: Mock blog content blocks if API fails\n            const mockBlocks = [\n                {\n                    id: \"intro-1\",\n                    type: \"introduction\",\n                    content: \"In today's fast-paced business environment, small business owners are constantly looking for ways to streamline operations, reduce manual tasks, and focus on what matters most - growing their business. AI automation tools have emerged as game-changers, offering sophisticated solutions that were once only available to large enterprises.\\n\\nThis comprehensive guide will walk you through the best AI automation tools specifically designed for small businesses, helping you understand how to implement them effectively and maximize your return on investment. Whether you're looking to automate customer service, marketing campaigns, or internal processes, we've got you covered.\",\n                    editable: true,\n                    wordCount: 124\n                },\n                {\n                    id: \"feature-img-1\",\n                    type: \"image\",\n                    imageType: \"feature\",\n                    alt: \"AI automation tools dashboard showing various business processes being automated\",\n                    editable: false\n                },\n                {\n                    id: \"section-1\",\n                    type: \"section\",\n                    h2: \"What Are AI Automation Tools and Why Do Small Businesses Need Them?\",\n                    content: \"AI automation tools are software solutions that use artificial intelligence to perform tasks that typically require human intervention. These tools can analyze data, make decisions, and execute actions based on predefined rules or learned patterns.\\n\\nFor small businesses, AI automation offers several key benefits:\\n\\n• **Cost Reduction**: Automate repetitive tasks to reduce labor costs\\n• **Improved Accuracy**: Minimize human errors in data processing and analysis\\n• **24/7 Operations**: Keep critical processes running around the clock\\n• **Scalability**: Handle increased workload without proportional staff increases\\n• **Competitive Advantage**: Access enterprise-level capabilities at affordable prices\\n\\nThe key is choosing the right tools that align with your specific business needs and budget constraints.\",\n                    editable: true,\n                    wordCount: 156\n                },\n                {\n                    id: \"section-2\",\n                    type: \"section\",\n                    h2: \"Top 5 AI Automation Tools for Small Business Operations\",\n                    content: \"After extensive research and testing, we've identified the top AI automation tools that deliver exceptional value for small businesses:\\n\\n**1. Zapier with AI Features**\\nZapier's AI-powered automation connects over 5,000 apps, making it easy to create complex workflows without coding. Recent AI enhancements include smart suggestions and natural language processing for trigger creation.\\n\\n**2. HubSpot's AI-Powered CRM**\\nHubSpot offers AI-driven lead scoring, predictive analytics, and automated email sequences. Their free tier makes it accessible for startups and growing businesses.\\n\\n**3. Chatfuel for Customer Service**\\nThis AI chatbot platform handles customer inquiries 24/7, reducing response times and freeing up your team for more complex tasks.\\n\\n**4. Calendly with Smart Scheduling**\\nAI-powered scheduling that learns from your preferences and automatically optimizes meeting times based on participant availability and preferences.\\n\\n**5. QuickBooks AI for Financial Management**\\nAutomated expense categorization, invoice processing, and financial forecasting powered by machine learning algorithms.\",\n                    editable: true,\n                    wordCount: 189\n                },\n                {\n                    id: \"inblog-img-1\",\n                    type: \"image\",\n                    imageType: \"in-blog\",\n                    alt: \"Comparison chart showing different AI automation tools and their features\",\n                    editable: false\n                },\n                {\n                    id: \"section-3\",\n                    type: \"section\",\n                    h2: \"Implementation Strategy: How to Successfully Deploy AI Automation\",\n                    content: \"Successfully implementing AI automation in your small business requires a strategic approach:\\n\\n**Phase 1: Assessment and Planning (Week 1-2)**\\n• Identify repetitive, time-consuming tasks\\n• Map current workflows and processes\\n• Set clear goals and success metrics\\n• Determine budget and resource allocation\\n\\n**Phase 2: Tool Selection and Setup (Week 3-4)**\\n• Research and compare automation tools\\n• Start with free trials or basic plans\\n• Configure initial automations for low-risk processes\\n• Train team members on new tools\\n\\n**Phase 3: Testing and Optimization (Week 5-8)**\\n• Monitor automation performance closely\\n• Gather feedback from team members\\n• Make adjustments and improvements\\n• Gradually expand to more complex processes\\n\\n**Phase 4: Scale and Advanced Features (Month 3+)**\\n• Implement more sophisticated automations\\n• Integrate multiple tools for comprehensive workflows\\n• Analyze ROI and adjust strategy as needed\\n• Explore advanced AI features and capabilities\",\n                    editable: true,\n                    wordCount: 178\n                },\n                {\n                    id: \"section-4\",\n                    type: \"section\",\n                    h2: \"Measuring ROI: Key Metrics for AI Automation Success\",\n                    content: \"To ensure your AI automation investment pays off, track these essential metrics:\\n\\n**Time Savings Metrics:**\\n• Hours saved per week on automated tasks\\n• Reduction in manual data entry time\\n• Faster response times to customer inquiries\\n\\n**Cost Efficiency Metrics:**\\n• Reduction in operational costs\\n• Decreased need for additional staff\\n• Lower error rates and associated costs\\n\\n**Business Growth Metrics:**\\n• Increased lead generation and conversion rates\\n• Improved customer satisfaction scores\\n• Enhanced team productivity and focus on strategic tasks\\n\\n**Quality Improvements:**\\n• Reduced error rates in data processing\\n• More consistent customer experiences\\n• Better compliance with business processes\\n\\nMost small businesses see a positive ROI within 3-6 months of implementing AI automation tools, with some reporting up to 300% return on investment within the first year.\",\n                    editable: true,\n                    wordCount: 145\n                },\n                {\n                    id: \"inblog-img-2\",\n                    type: \"image\",\n                    imageType: \"in-blog\",\n                    alt: \"ROI dashboard showing positive returns from AI automation implementation\",\n                    editable: false\n                },\n                {\n                    id: \"conclusion-1\",\n                    type: \"conclusion\",\n                    content: \"AI automation tools are no longer a luxury reserved for large corporations – they're essential for small businesses looking to compete and thrive in today's digital landscape. By starting with simple automations and gradually expanding your capabilities, you can transform your operations, reduce costs, and focus on what you do best: serving your customers and growing your business.\\n\\nRemember, the key to successful AI automation is starting small, measuring results, and continuously optimizing your processes. Choose tools that integrate well with your existing systems, provide excellent support, and offer room for growth as your business expands.\\n\\nReady to get started? Begin by identifying one repetitive task in your business and explore how AI automation can help you reclaim those valuable hours for more strategic activities.\",\n                    editable: true,\n                    wordCount: 142\n                },\n                {\n                    id: \"references-1\",\n                    type: \"references\",\n                    content: '1. McKinsey Global Institute. (2023). \"The Age of AI: Artificial Intelligence and the Future of Work.\"\\n2. Small Business Administration. (2023). \"Technology Adoption in Small Businesses: 2023 Report.\"\\n3. Zapier. (2023). \"State of Business Automation Report 2023.\"\\n4. HubSpot Research. (2023). \"AI in Small Business: Adoption and Impact Study.\"\\n5. Deloitte. (2023). \"AI and Automation in Small and Medium Enterprises.\"',\n                    editable: true,\n                    wordCount: 67\n                }\n            ];\n            setBlocks(mockBlocks);\n        } catch (error) {\n            toast({\n                title: \"Error generating content\",\n                description: \"Failed to generate blog content. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        setSaving(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.saveDraft(draftId, {\n                structuredContent: blocks,\n                status: 'content_generation'\n            });\n            toast({\n                title: \"Draft saved\",\n                description: \"Your blog draft has been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Save failed\",\n                description: \"Failed to save draft. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleGenerateLinks = async ()=>{\n        setGeneratingLinks(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateLinks(draftId);\n            toast({\n                title: \"Links generated\",\n                description: \"Internal and external links have been generated.\"\n            });\n            router.push(\"/blog/\".concat(draftId, \"/review\"));\n        } catch (error) {\n            toast({\n                title: \"Link generation failed\",\n                description: \"Failed to generate links. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setGeneratingLinks(false);\n        }\n    };\n    const handleEditBlock = (block)=>{\n        setEditingBlock(block);\n        setEditContent(block.content || \"\");\n        setCustomPrompt(\"\");\n    };\n    const handleRegenerateBlock = async (blockId, type)=>{\n        console.log(\"=== FRONTEND: Regenerating block \".concat(blockId, \" with type \").concat(type, \" ===\"));\n        console.log('Draft ID:', draftId);\n        console.log('Custom prompt:', type === \"ai\" ? customPrompt : undefined);\n        console.log('Edit content:', type === \"manual\" ? editContent : undefined);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.regenerateBlock(draftId, blockId, type, type === \"ai\" ? customPrompt : undefined, type === \"manual\" ? editContent : undefined);\n            console.log('=== FRONTEND: Regeneration response ===', response);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                setEditingBlock(null);\n                toast({\n                    title: \"Block updated\",\n                    description: \"Content block has been successfully updated.\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error regenerating block:', error);\n            toast({\n                title: \"Update failed\",\n                description: \"Failed to update content block. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteBlock = (blockId)=>{\n        setBlocks(blocks.filter((block)=>block.id !== blockId));\n        toast({\n            title: \"Block deleted\",\n            description: \"Content block has been removed.\"\n        });\n    };\n    const handleImageGenerate = async (blockId, description)=>{\n        console.log(\"Generating image for block \".concat(blockId, \" with description: \").concat(description));\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateImage(draftId, blockId, description);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                toast({\n                    title: \"Image generated successfully!\",\n                    description: \"AI has generated an image description and placeholder for your content.\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error generating image:', error);\n            toast({\n                title: \"Image generation failed\",\n                description: \"Failed to generate image. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImageUpload = async (blockId, file)=>{\n        console.log(\"Uploading image for block \".concat(blockId, \":\"), file.name);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.uploadImage(draftId, blockId, file);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                toast({\n                    title: \"Image uploaded\",\n                    description: \"Image has been uploaded successfully!\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n            toast({\n                title: \"Upload failed\",\n                description: \"Failed to upload image. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                    currentStep: 3,\n                    draftId: draftId\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-5xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                className: \"h-16 w-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-32 w-full\"\n                                }, i, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                currentStep: 3,\n                draftId: draftId\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Content Editor\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        wordCount,\n                                        \" words\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSaveDraft,\n                                    disabled: saving,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        saving ? \"Saving...\" : \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Preview\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleGenerateLinks,\n                                    disabled: generatingLinks,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        generatingLinks ? \"Generating...\" : \"Generate Links\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/blog/\".concat(draftId, \"/deploy\")),\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Deploy to WordPress\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-5xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: blocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_content_block__WEBPACK_IMPORTED_MODULE_12__.ContentBlock, {\n                                block: block,\n                                onEdit: ()=>handleEditBlock(block),\n                                onRegenerate: ()=>handleRegenerateBlock(block.id, \"ai\"),\n                                onDelete: block.editable ? ()=>handleDeleteBlock(block.id) : undefined,\n                                onImageGenerate: handleImageGenerate,\n                                onImageUpload: handleImageUpload\n                            }, block.id, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: !!editingBlock,\n                        onOpenChange: ()=>setEditingBlock(null),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                            className: \"max-w-4xl max-h-[80vh] overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                            children: \"Edit Content Block\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                            children: \"Modify the content manually or use AI to regenerate with a custom prompt\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Content\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                    value: editContent,\n                                                    onChange: (e)=>setEditContent(e.target.value),\n                                                    rows: 8,\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"AI Regeneration Prompt (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    value: customPrompt,\n                                                    onChange: (e)=>setCustomPrompt(e.target.value),\n                                                    placeholder: \"e.g., Make it more engaging, Add statistics, Simplify for beginners...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleRegenerateBlock((editingBlock === null || editingBlock === void 0 ? void 0 : editingBlock.id) || \"\", \"manual\"),\n                                                    className: \"flex-1\",\n                                                    children: \"Save Manual Changes\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleRegenerateBlock((editingBlock === null || editingBlock === void 0 ? void 0 : editingBlock.id) || \"\", \"ai\"),\n                                                    disabled: !customPrompt,\n                                                    variant: \"outline\",\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Regenerate with AI\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"itvx8VbRR5EQhXBtC+m1KKHomDw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/[draftId]/editor/page.tsx\n"));

/***/ })

});