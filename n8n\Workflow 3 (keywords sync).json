{"name": "Workflow 3 (keywords sync)", "nodes": [{"parameters": {"documentId": {"__rl": true, "value": "1Apb72AR5glFViGd3O9DIa29ICMzrk35K-Io_czPSQSA", "mode": "list", "cachedResultName": "WattMonk Blog Data"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Automated Keywords"}, "options": {}}, "id": "read-keywords-001", "name": "Read Generated Keywords", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [200, 0], "credentials": {"googleSheetsOAuth2Api": {"id": "TXBfVPVDwrhp999t", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1Apb72AR5glFViGd3O9DIa29ICMzrk35K-Io_czPSQSA", "mode": "list", "cachedResultName": "WattMonk Blog Data"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Manual Keywords"}, "options": {}}, "id": "read-manual-keywords-001", "name": "Read Manual Keywords", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [200, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "TXBfVPVDwrhp999t", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Combine and transform keywords for ArticleScribe\nconst automatedItems = $('Read Generated Keywords').all();\nconst manualItems = $('Read Manual Keywords').all();\n\nconsole.log(`Processing ${automatedItems.length} automated and ${manualItems.length} manual keywords`);\n\nconst allKeywords = [];\n\n// Process automated keywords (from your Workflow 1)\nfor (let i = 0; i < automatedItems.length; i++) {\n  const item = automatedItems[i].json;\n  \n  // Skip header or empty rows\n  if (!item['Focus Keyword'] || item['Focus Keyword'] === 'Focus Keyword') {\n    continue;\n  }\n  \n  const keyword = {\n    id: `auto_keyword_${i + 1}_${Date.now()}`,\n    focusKeyword: item['Focus Keyword'] || '',\n    articleFormat: item['Article Format'] || 'Guide',\n    wordCount: item['Word Count'] || '1500-2000',\n    targetAudience: item['Target Audience'] || 'Solar Professionals',\n    objective: item['Objective'] || 'Education and Lead Generation',\n    source: 'ai',\n    generatedBy: 'n8n_workflow_1',\n    \n    // Additional metadata from your workflow\n    uniqueAngle: item['Unique Angle'] || '',\n    outline: item['Outline'] ? JSON.parse(item['Outline'] || '[]') : [],\n    keyTakeaways: item['Key Takeaways'] ? JSON.parse(item['Key Takeaways'] || '[]') : [],\n    examples: item['Examples'] ? JSON.parse(item['Examples'] || '[]') : [],\n    callToAction: item['Call To Action'] || '',\n    \n    // Sync metadata\n    lastUpdated: new Date().toISOString(),\n    syncSource: 'n8n_workflow_3',\n    sheetRowIndex: i + 1\n  };\n  \n  allKeywords.push(keyword);\n}\n\n// Process manual keywords\nfor (let i = 0; i < manualItems.length; i++) {\n  const item = manualItems[i].json;\n  \n  // Skip header or empty rows\n  if (!item['Focus Keyword'] || item['Focus Keyword'] === 'Focus Keyword') {\n    continue;\n  }\n  \n  const keyword = {\n    id: `manual_keyword_${i + 1}_${Date.now()}`,\n    focusKeyword: item['Focus Keyword'] || '',\n    articleFormat: item['Article Format'] || 'Guide',\n    wordCount: item['Word Count'] || '1500-2000',\n    targetAudience: item['Target Audience'] || 'Solar Professionals',\n    objective: item['Objective'] || 'Education and Lead Generation',\n    source: 'manual',\n    \n    // Manual keyword specific fields\n    priority: item['Priority'] || 'Medium',\n    difficulty: item['Difficulty'] || 'Medium',\n    searchVolume: item['Search Volume'] || '',\n    competition: item['Competition'] || '',\n    \n    // Sync metadata\n    lastUpdated: new Date().toISOString(),\n    syncSource: 'n8n_workflow_3',\n    sheetRowIndex: i + 1\n  };\n  \n  allKeywords.push(keyword);\n}\n\n// Categorize keywords for ArticleScribe (2 manual + 2 AI as required)\nconst manualKeywords = allKeywords.filter(k => k.source === 'manual').slice(0, 10); // Get up to 10 manual\nconst aiKeywords = allKeywords.filter(k => k.source === 'ai').slice(0, 10); // Get up to 10 AI\n\nconst result = {\n  keywords: allKeywords,\n  manual: manualKeywords,\n  auto: aiKeywords,\n  total: [...manualKeywords.slice(0, 2), ...aiKeywords.slice(0, 2)], // 2+2 for immediate use\n  totalCount: allKeywords.length,\n  manualCount: manualKeywords.length,\n  aiCount: aiKeywords.length,\n  syncTimestamp: new Date().toISOString(),\n  companyId: 'company_1' // Default to Wattmonk\n};\n\nconsole.log(`Prepared ${result.totalCount} total keywords (${result.manualCount} manual, ${result.aiCount} AI)`);\nconsole.log('Sample keywords for ArticleScribe:', result.total.map(k => k.focusKeyword));\n\nreturn [{ json: result }];"}, "id": "transform-keywords-001", "name": "Transform Keywords", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 100]}, {"parameters": {"url": "http://localhost:5000/api/n8n/sync/keywords", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify($json) }}", "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}, "timeout": 30000}}, "id": "sync-keywords-001", "name": "Sync Keywords to ArticleScribe", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 100]}, {"parameters": {"jsCode": "// Log keyword sync results\nconst items = $input.all();\nconst results = [];\n\nfor (const item of items) {\n  const response = item.json;\n  \n  console.log('Keywords Sync Response:', JSON.stringify(response, null, 2));\n  \n  const result = {\n    syncStatus: response.success ? 'success' : 'failed',\n    manualKeywords: response.manual || 0,\n    autoKeywords: response.auto || 0,\n    totalKeywords: response.total || 0,\n    message: response.message || 'No message',\n    timestamp: new Date().toISOString(),\n    workflowName: 'Keywords Sync Workflow 3'\n  };\n  \n  if (!response.success) {\n    console.error('Keywords sync failed:', response.error || 'Unknown error');\n    result.error = response.error || 'Unknown error';\n  } else {\n    console.log(`✓ Successfully synced ${response.total} keywords (${response.manual} manual, ${response.auto} auto)`);\n  }\n  \n  results.push(result);\n}\n\nreturn results.map(r => ({ json: r }));"}, "id": "log-keyword-results-001", "name": "Log Keyword Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 100]}, {"parameters": {"path": "keywords-sync-webhook", "options": {}}, "id": "webhook-keywords-001", "name": "Keywords Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 100], "webhookId": "keywords-sync-webhook-001"}, {"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "id": "schedule-keywords-001", "name": "Schedule Every 2 Hours", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 300]}], "connections": {"Read Generated Keywords": {"main": [[{"node": "Transform Keywords", "type": "main", "index": 0}]]}, "Read Manual Keywords": {"main": [[{"node": "Transform Keywords", "type": "main", "index": 0}]]}, "Transform Keywords": {"main": [[{"node": "Sync Keywords to ArticleScribe", "type": "main", "index": 0}]]}, "Sync Keywords to ArticleScribe": {"main": [[{"node": "Log Keyword Results", "type": "main", "index": 0}]]}, "Keywords Webhook": {"main": [[{"node": "Read Generated Keywords", "type": "main", "index": 0}, {"node": "Read Manual Keywords", "type": "main", "index": 0}]]}, "Schedule Every 2 Hours": {"main": [[{"node": "Read Generated Keywords", "type": "main", "index": 0}, {"node": "Read Manual Keywords", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "meta": {"instanceId": "bd13a791ef83e2ae6563714d8025574c31f1bfadf3ea50d267483e93fe42f56c"}, "tags": ["articlescribe", "keywords-sync", "real-time"]}