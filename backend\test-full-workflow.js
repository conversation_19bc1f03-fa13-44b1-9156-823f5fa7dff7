require('dotenv').config();
const draftService = require('./src/services/draft.service');
const contentService = require('./src/services/content.service');

async function testFullWorkflow() {
  console.log('🧪 TESTING FULL CONTENT GENERATION WORKFLOW');
  console.log('============================================');
  
  try {
    // Step 1: Create a draft with proper context
    console.log('📝 Step 1: Creating draft with context...');
    const draftData = {
      userId: 'test-user',
      selectedKeyword: 'AI automation tools',
      companyData: {
        companyName: 'TechSolutions Pro',
        serviceOverview: 'AI automation consulting and implementation',
        brandVoice: 'professional, innovative',
        targetAudience: 'small business owners and entrepreneurs'
      },
      finalMeta: {
        h1Title: 'Best AI Automation Tools for Small Business in 2025',
        metaDescription: 'Discover the top AI automation tools that can transform your small business operations, reduce costs, and boost productivity in 2025.',
        seoScore: 95,
        engagementScore: 90
      },
      keywordCluster: ['business automation', 'AI tools', 'workflow automation', 'productivity software'],
      competitorAnalysis: {
        topics: [
          { title: 'Top Automation Tools', description: 'Comprehensive tool reviews' },
          { title: 'Implementation Guide', description: 'Step-by-step setup' }
        ]
      },
      newsArticles: [
        { title: 'AI Automation Trends 2025', source: 'TechCrunch', publishedAt: '2025-01-15' },
        { title: 'Small Business AI Adoption', source: 'Forbes', publishedAt: '2025-01-10' }
      ],
      status: 'meta_selected'
    };
    
    const draft = await draftService.createDraft(draftData);
    console.log('✅ Draft created with ID:', draft.id);
    
    // Step 2: Generate content using the content service
    console.log('\n🚀 Step 2: Generating content with AI...');
    const content = await contentService.generateWordPressFormattedContent(
      draft.finalMeta,
      draft.selectedKeyword,
      draft.keywordCluster,
      draft.companyData,
      draft.competitorAnalysis,
      {},
      draft.newsArticles
    );
    
    console.log('✅ Content generated successfully!');
    console.log('📊 Content analysis:');
    console.log('  - Introduction length:', content.introduction?.length || 0);
    console.log('  - Sections count:', content.sections?.length || 0);
    console.log('  - Images count:', content.inBlogImages?.length || 0);
    console.log('  - References count:', content.references?.length || 0);
    
    // Step 3: Check if content is contextual
    console.log('\n🔍 Step 3: Verifying content context...');
    const h1InContent = content.introduction?.includes('AI automation tools') || 
                       content.introduction?.includes('Best AI Automation Tools');
    const metaInContent = content.introduction?.includes('small business') ||
                         content.introduction?.includes('productivity');
    
    console.log('  - H1 context found:', h1InContent ? '✅' : '❌');
    console.log('  - Meta context found:', metaInContent ? '✅' : '❌');
    
    // Step 4: Show actual content samples
    console.log('\n📝 Step 4: Content samples...');
    console.log('Introduction preview:');
    console.log(content.introduction?.substring(0, 200) + '...');
    
    if (content.sections && content.sections.length > 0) {
      console.log('\nFirst section H2:', content.sections[0].h2);
      console.log('First section preview:', content.sections[0].content?.substring(0, 200) + '...');
    }
    
    if (content.references && content.references.length > 0) {
      console.log('\nReferences:');
      content.references.forEach((ref, i) => {
        console.log(`${i + 1}. ${ref}`);
      });
    }
    
    // Step 5: Update draft with generated content
    console.log('\n💾 Step 5: Updating draft with content...');
    await draftService.updateDraft(draft.id, {
      structuredContent: content,
      status: 'content_generated'
    });
    
    console.log('✅ Draft updated successfully!');
    
    // Step 6: Retrieve and verify
    console.log('\n🔍 Step 6: Verifying saved content...');
    const updatedDraft = await draftService.getDraft(draft.id);
    const savedContent = updatedDraft.structuredContent;
    
    console.log('Saved content verification:');
    console.log('  - Has introduction:', !!savedContent?.introduction);
    console.log('  - Has sections:', savedContent?.sections?.length || 0);
    console.log('  - Has references:', savedContent?.references?.length || 0);
    
    console.log('\n🎉 FULL WORKFLOW TEST COMPLETED SUCCESSFULLY!');
    console.log('The AI is generating proper contextual content!');
    
  } catch (error) {
    console.error('❌ Workflow test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testFullWorkflow().catch(console.error);
