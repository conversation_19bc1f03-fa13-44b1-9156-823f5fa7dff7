"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { RefreshCw, Eye, Tag } from "lucide-react"
import type { MetaOption, Draft } from "@/types/api"
import { api } from "@/lib/api"
import { StepperHeader } from "@/components/stepper-header"
import { SEOScoreCircle } from "@/components/seo-score-circle"

export default function MetaPage() {
  const [draft, setDraft] = useState<Draft | null>(null)
  const [metaOptions, setMetaOptions] = useState<MetaOption[]>([])
  const [selectedMeta, setSelectedMeta] = useState<number>(-1)
  const [loading, setLoading] = useState(true)
  const [regenerating, setRegenerating] = useState<number>(-1)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  const draftId = params.draftId as string

  useEffect(() => {
    loadDraftAndGenerateMeta()
  }, [draftId])

  const loadDraftAndGenerateMeta = async () => {
    try {
      // Load draft data first
      const draftResponse = await api.getDraft(draftId)
      if (draftResponse.success && draftResponse.data) {
        const draftData = draftResponse.data
        setDraft(draftData)

        // Check if meta options already exist
        if (draftData.metaOptions && draftData.metaOptions.length > 0) {
          setMetaOptions(draftData.metaOptions)
          // If a meta is already selected, set it
          if (draftData.finalMeta) {
            const selectedIndex = draftData.metaOptions.findIndex(
              option => option.h1Title === draftData.finalMeta?.h1Title
            )
            if (selectedIndex !== -1) {
              setSelectedMeta(selectedIndex)
            }
          }
        } else {
          // Generate new meta options
          await generateMetaOptions()
        }
      }
    } catch (error) {
      toast({
        title: "Error loading draft",
        description: "Failed to load draft data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const generateMetaOptions = async () => {
    try {
      // Call backend API to generate meta options
      const response = await api.generateMetaScores(draftId)
      if (response.success && response.metaOptions) {
        setMetaOptions(response.metaOptions)
      }
    } catch (error) {
      toast({
        title: "Error generating meta options",
        description: "Failed to generate SEO meta options. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRegenerate = async (index: number) => {
    console.log(`Regenerating meta option at index: ${index}`);
    setRegenerating(index)
    try {
      const response = await api.regenerateMeta(draftId, index);
      console.log('Regeneration response:', response);

      if (response.success) {
        // Update the local state with new meta options
        setMetaOptions(response.metaOptions);
        toast({
          title: "Meta regenerated",
          description: "New meta option has been generated successfully.",
        })
      } else {
        throw new Error('Regeneration failed');
      }
    } catch (error) {
      console.error('Error regenerating meta:', error);
      toast({
        title: "Regeneration failed",
        description: "Failed to regenerate meta option. Please try again.",
        variant: "destructive",
      })
    } finally {
      setRegenerating(-1)
    }
  }

  const handleContinue = async () => {
    if (selectedMeta === -1) return

    try {
      await api.selectMeta(draftId, selectedMeta)
      router.push(`/blog/${draftId}/editor`)
    } catch (error) {
      toast({
        title: "Error selecting meta",
        description: "Failed to save meta selection. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={2} draftId={draftId} />
        <main className="max-w-7xl mx-auto px-6 py-8">
          <div className="space-y-6">
            <Skeleton className="h-8 w-64" />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-96" />
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StepperHeader currentStep={2} draftId={draftId} />

      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">SEO Meta Generation</h1>
            <p className="text-gray-600">Choose the best SEO-optimized meta information for your blog post</p>
          </div>

          <RadioGroup
            value={selectedMeta.toString()}
            onValueChange={(value) => setSelectedMeta(Number.parseInt(value))}
          >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {metaOptions.map((meta, index) => (
                <div key={index} className="relative">
                  <Label htmlFor={`meta-${index}`} className="cursor-pointer">
                    <Card
                      className={`hover:shadow-lg transition-all duration-200 ${
                        selectedMeta === index ? "ring-2 ring-[#0066cc] border-[#0066cc]" : "hover:border-gray-300"
                      }`}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-3">
                              <SEOScoreCircle score={meta.scores.totalScore} size="md" />
                              <RadioGroupItem value={index.toString()} id={`meta-${index}`} />
                            </div>
                            <CardTitle className="text-lg leading-tight">{meta.h1Title}</CardTitle>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <h4 className="font-medium text-sm text-gray-700 mb-1">Meta Title</h4>
                          <p className="text-sm text-gray-900 leading-relaxed">{meta.metaTitle}</p>
                          <span className="text-xs text-gray-500">{meta.metaTitle.length} characters</span>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm text-gray-700 mb-1">Meta Description</h4>
                          <p className="text-sm text-gray-600 leading-relaxed">{meta.metaDescription}</p>
                          <span className="text-xs text-gray-500">{meta.metaDescription.length} characters</span>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm text-gray-700 mb-2">Score Breakdown</h4>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex justify-between">
                              <span>Keywords:</span>
                              <span className={getScoreColor(meta.scores.keywordScore)}>
                                {meta.scores.keywordScore}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Length:</span>
                              <span className={getScoreColor(meta.scores.lengthScore)}>{meta.scores.lengthScore}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Readability:</span>
                              <span className={getScoreColor(meta.scores.readabilityScore)}>
                                {meta.scores.readabilityScore}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Trends:</span>
                              <span className={getScoreColor(meta.scores.trendScore)}>{meta.scores.trendScore}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm text-gray-700 mb-2">Keywords Included</h4>
                          <div className="flex flex-wrap gap-1">
                            {meta.keywordsIncluded.map((keyword, kidx) => (
                              <Badge key={kidx} variant="outline" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="flex gap-2 pt-2">
                          <Button
                            onClick={() => handleRegenerate(index)}
                            disabled={regenerating === index}
                            variant="outline"
                            size="sm"
                            className="flex-1"
                          >
                            <RefreshCw className={`h-4 w-4 mr-1 ${regenerating === index ? "animate-spin" : ""}`} />
                            {regenerating === index ? "Regenerating..." : "Regenerate"}
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>

          <div className="flex justify-between">
            <Button variant="outline" onClick={() => router.back()}>
              Back to Keywords
            </Button>

            <Button onClick={handleContinue} disabled={selectedMeta === -1} className="bg-[#0066cc] hover:bg-blue-700">
              Select & Continue
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
