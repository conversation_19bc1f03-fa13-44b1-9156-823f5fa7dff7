require('dotenv').config();
const axios = require('axios');

async function testSimpleAPI() {
  console.log('🔗 TESTING SIMPLE API CONNECTION');
  console.log('=================================');
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // Use the draft ID from our successful test
    const draftId = 'draft_1753098040669_lecbgcths'; // From our working test
    
    console.log('🚀 Testing content generation with known working draft...');
    console.log('Draft ID:', draftId);
    
    const response = await axios.post(`${baseURL}/blog/generate-structured-content`, {
      draftId: draftId
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 60000 // 60 second timeout
    });
    
    console.log('✅ SUCCESS! Content generation worked!');
    console.log('📊 Response details:');
    console.log('  - Success:', response.data.success);
    console.log('  - Has structuredContent:', !!response.data.structuredContent);
    console.log('  - Blocks count:', response.data.structuredContent?.length || 0);
    console.log('  - Message:', response.data.message);
    
    if (response.data.structuredContent && response.data.structuredContent.length > 0) {
      const firstBlock = response.data.structuredContent[0];
      console.log('\n📝 First block:');
      console.log('  - ID:', firstBlock.id);
      console.log('  - Type:', firstBlock.type);
      console.log('  - Content preview:', firstBlock.content?.substring(0, 150) + '...');
    }
    
    console.log('\n🎉 FRONTEND-BACKEND CONNECTION IS WORKING!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('📡 Response status:', error.response.status);
      console.error('📡 Response data:', error.response.data);
    }
    
    // Try to test image generation instead
    console.log('\n🎨 Testing image generation as fallback...');
    try {
      const imageResponse = await axios.post(`${baseURL}/blog/generate-ai-image`, {
        prompt: 'Professional business dashboard',
        keyword: 'AI automation'
      }, {
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✅ Image generation works!');
      console.log('📸 Response:', {
        success: imageResponse.data.success,
        fallback: imageResponse.data.fallback,
        message: imageResponse.data.message
      });
      
    } catch (imgError) {
      console.error('❌ Image generation also failed:', imgError.message);
    }
  }
}

testSimpleAPI().catch(console.error);
