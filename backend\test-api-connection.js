require('dotenv').config();
const axios = require('axios');

async function testAPIConnection() {
  console.log('🔗 TESTING FRONTEND-BACKEND API CONNECTION');
  console.log('==========================================');
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // Test 1: Health check
    console.log('\n🏥 Test 1: Health check...');
    try {
      const healthResponse = await axios.get(`${baseURL}/blog/drafts`);
      console.log('✅ Backend is responding');
      console.log('📊 Response status:', healthResponse.status);
    } catch (error) {
      console.log('❌ Backend health check failed:', error.message);
      return;
    }
    
    // Test 2: Create a test draft
    console.log('\n📝 Test 2: Creating test draft...');
    const draftData = {
      userId: 'test-frontend-user',
      selectedKeyword: 'AI automation tools',
      companyData: {
        companyName: 'TechSolutions Pro',
        serviceOverview: 'AI automation consulting',
        brandVoice: 'professional, innovative',
        targetAudience: 'small business owners'
      },
      finalMeta: {
        h1Title: 'Best AI Automation Tools for Small Business in 2025',
        metaDescription: 'Discover the top AI automation tools that can transform your small business operations, reduce costs, and boost productivity in 2025.',
        seoScore: 95,
        engagementScore: 90
      },
      keywordCluster: ['business automation', 'AI tools', 'workflow automation'],
      competitorAnalysis: {
        topics: [
          { title: 'Top Automation Tools', description: 'Comprehensive reviews' }
        ]
      },
      newsArticles: [
        { title: 'AI Automation Trends 2025', source: 'TechCrunch', publishedAt: '2025-01-15' }
      ],
      status: 'meta_selected'
    };
    
    const createResponse = await axios.post(`${baseURL}/blog/draft`, draftData, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    const draftId = createResponse.data.draft.id;
    console.log('✅ Draft created with ID:', draftId);
    
    // Test 3: Generate structured content (the main issue)
    console.log('\n🚀 Test 3: Generating structured content...');
    const contentResponse = await axios.post(`${baseURL}/blog/generate-structured-content`, {
      draftId: draftId
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('📡 Content generation response:');
    console.log('  - Success:', contentResponse.data.success);
    console.log('  - Has structuredContent:', !!contentResponse.data.structuredContent);
    console.log('  - Has blogBlocks:', !!contentResponse.data.blogBlocks);
    console.log('  - Blocks count:', contentResponse.data.structuredContent?.length || 0);
    console.log('  - Message:', contentResponse.data.message);
    
    if (contentResponse.data.structuredContent && contentResponse.data.structuredContent.length > 0) {
      const firstBlock = contentResponse.data.structuredContent[0];
      console.log('\n📝 First block preview:');
      console.log('  - Type:', firstBlock.type);
      console.log('  - Content length:', firstBlock.content?.length || 0);
      console.log('  - Content preview:', firstBlock.content?.substring(0, 100) + '...');
    }
    
    // Test 4: Test AI image generation
    console.log('\n🎨 Test 4: Testing AI image generation...');
    const imageResponse = await axios.post(`${baseURL}/blog/generate-ai-image`, {
      prompt: 'Professional business dashboard with AI automation metrics',
      keyword: 'AI automation tools'
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('📸 Image generation response:');
    console.log('  - Success:', imageResponse.data.success);
    console.log('  - Has imageBase64:', !!imageResponse.data.imageBase64);
    console.log('  - MIME type:', imageResponse.data.mimeType);
    console.log('  - Fallback mode:', imageResponse.data.fallback);
    console.log('  - Message:', imageResponse.data.message);
    
    console.log('\n🎉 ALL API TESTS PASSED!');
    console.log('✅ Backend is working correctly');
    console.log('✅ Content generation is functional');
    console.log('✅ Image generation is functional');
    console.log('✅ Frontend should now receive real AI content!');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('📡 Response status:', error.response.status);
      console.error('📡 Response data:', error.response.data);
    }
  }
}

testAPIConnection().catch(console.error);
