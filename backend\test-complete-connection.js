require('dotenv').config();
const axios = require('axios');

async function testCompleteConnection() {
  console.log('🔗 TESTING COMPLETE FRONTEND-BACKEND CONNECTION');
  console.log('===============================================');
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // Test 1: Basic API health
    console.log('\n🏥 Test 1: Basic API health check...');
    const healthResponse = await axios.get(`${baseURL}/test`);
    console.log('✅ Basic API working:', healthResponse.data.message);
    
    // Test 2: Blog routes health
    console.log('\n🏥 Test 2: Blog routes health check...');
    const blogHealthResponse = await axios.get(`${baseURL}/blog/test`);
    console.log('✅ Blog routes working:', blogHealthResponse.data.message);
    
    // Test 3: Test content generation (what frontend will use)
    console.log('\n🧪 Test 3: Test content generation...');
    const testContentResponse = await axios.post(`${baseURL}/blog/test-content`, {}, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Test content generation successful!');
    console.log('📊 Response details:');
    console.log('  - Success:', testContentResponse.data.success);
    console.log('  - Has structuredContent:', !!testContentResponse.data.structuredContent);
    console.log('  - Blocks count:', testContentResponse.data.structuredContent?.length || 0);
    console.log('  - Message:', testContentResponse.data.message);
    
    if (testContentResponse.data.structuredContent && testContentResponse.data.structuredContent.length > 0) {
      const firstBlock = testContentResponse.data.structuredContent[0];
      console.log('\n📝 First block preview:');
      console.log('  - ID:', firstBlock.id);
      console.log('  - Type:', firstBlock.type);
      console.log('  - Content preview:', firstBlock.content?.substring(0, 100) + '...');
      console.log('  - Editable:', firstBlock.editable);
      console.log('  - Word count:', firstBlock.wordCount);
    }
    
    // Test 4: AI Image generation
    console.log('\n🎨 Test 4: AI Image generation...');
    const imageResponse = await axios.post(`${baseURL}/blog/generate-ai-image`, {
      prompt: 'Professional business team using AI automation tools',
      keyword: 'AI automation tools'
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ AI Image generation successful!');
    console.log('📸 Image response details:');
    console.log('  - Success:', imageResponse.data.success);
    console.log('  - Has imageBase64:', !!imageResponse.data.imageBase64);
    console.log('  - MIME type:', imageResponse.data.mimeType);
    console.log('  - Original prompt:', imageResponse.data.originalPrompt);
    console.log('  - Enhanced prompt preview:', imageResponse.data.enhancedPrompt?.substring(0, 100) + '...');
    console.log('  - Fallback mode:', imageResponse.data.fallback);
    
    if (imageResponse.data.placeholderUrl) {
      console.log('  - Placeholder URL:', imageResponse.data.placeholderUrl);
    }
    
    // Test 5: Draft operations
    console.log('\n📄 Test 5: Draft operations...');
    try {
      const draftsResponse = await axios.get(`${baseURL}/blog/drafts`);
      console.log('✅ Draft listing working, found', draftsResponse.data.length || 0, 'drafts');
    } catch (error) {
      console.log('⚠️ Draft listing error (expected if no drafts):', error.response?.status);
    }
    
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('=====================================');
    console.log('✅ Backend API is fully functional');
    console.log('✅ Blog routes are working correctly');
    console.log('✅ Content generation is operational');
    console.log('✅ AI image generation is working');
    console.log('✅ Frontend can now connect successfully');
    console.log('');
    console.log('🚀 NEXT STEPS:');
    console.log('1. Open http://localhost:3000 in your browser');
    console.log('2. Navigate to the blog editor');
    console.log('3. Click "Generate Content" to test frontend-backend connection');
    console.log('4. The frontend should now receive real AI-generated content!');
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    if (error.response) {
      console.error('📡 Response status:', error.response.status);
      console.error('📡 Response data:', error.response.data);
    }
  }
}

testCompleteConnection().catch(console.error);
