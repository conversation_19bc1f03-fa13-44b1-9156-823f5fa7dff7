# ArticleScribe N8N Workflows Setup Guide

## 🎯 Overview

This folder contains 4 N8N workflows that integrate with your ArticleScribe application:

1. **Workflow 1**: Content Ideas Generation (Your existing workflow)
2. **Workflow 2**: Company Data Sync
3. **Workflow 3**: Keywords Sync  
4. **Workflow 4**: Content Generation Pipeline

## 📋 Prerequisites

### 1. N8N Installation
```bash
# Install N8N globally
npm install -g n8n

# Or run with npx
npx n8n
```

### 2. Required Credentials
- **Google Sheets OAuth2**: For reading/writing Google Sheets
- **Google PaLM API**: For Gemini AI content generation
- **ArticleScribe Backend**: Running on `http://localhost:5000`

## 🚀 Setup Instructions

### Step 1: Import Workflows

1. Open N8N interface (usually `http://localhost:5678`)
2. Go to **Workflows** → **Import from File**
3. Import each JSON file:
   - `Workflow 1 (generate content ideas).json` ✅ (Already done)
   - `Workflow 2 (company sync).json`
   - `Workflow 3 (keywords sync).json`
   - `Workflow 4 (content generation).json`

### Step 2: Configure Credentials

#### Google Sheets OAuth2:
1. Go to **Credentials** → **Add Credential**
2. Select **Google Sheets OAuth2 API**
3. Use your existing credential ID: `TXBfVPVDwrhp999t`

#### Google PaLM API:
1. Go to **Credentials** → **Add Credential**
2. Select **Google PaLM API**
3. Use your existing credential ID: `mfmYziwwRaDXEvbP`

### Step 3: Update Sheet IDs (if needed)

All workflows are pre-configured with your sheet IDs:
- **Company KT Sheet**: `1F6afV2T3QxBQHrLfrbwqmR0a8Aag3YqCDZkvwwQf1R4`
- **WattMonk Blog Data**: `1Apb72AR5glFViGd3O9DIa29ICMzrk35K-Io_czPSQSA`

### Step 4: Test Each Workflow

#### Test Workflow 2 (Company Sync):
```bash
# Manual trigger via webhook
curl -X POST http://localhost:5678/webhook/company-sync-webhook

# Check ArticleScribe backend logs
curl http://localhost:5000/api/n8n/health
```

#### Test Workflow 3 (Keywords Sync):
```bash
# Manual trigger via webhook
curl -X POST http://localhost:5678/webhook/keywords-sync-webhook

# Verify keywords in ArticleScribe
curl http://localhost:5000/api/n8n/status
```

#### Test Workflow 4 (Content Generation):
```bash
# Test content generation webhook
curl -X POST http://localhost:5678/webhook/generate-content \
  -H "Content-Type: application/json" \
  -d '{
    "draftId": "test_draft_123",
    "keyword": "solar panel installation",
    "companyData": {
      "companyName": "WattMonk",
      "servicesOffered": "Solar Installation",
      "tone": "Professional"
    }
  }'
```

## 📊 Workflow Details

### Workflow 1: Content Ideas Generation ✅
- **Trigger**: Weekly schedule (Mondays 9 AM)
- **Function**: Generates 2 unique blog ideas
- **Output**: Saves to "Automated Keywords" sheet
- **Status**: Already working

### Workflow 2: Company Data Sync
- **Trigger**: Every 6 hours + Webhook
- **Function**: Syncs company data to ArticleScribe
- **Endpoint**: `POST /api/n8n/sync/companies`
- **Webhook**: `http://localhost:5678/webhook/company-sync-webhook`

### Workflow 3: Keywords Sync
- **Trigger**: Every 2 hours + Webhook  
- **Function**: Syncs manual + AI keywords to ArticleScribe
- **Endpoint**: `POST /api/n8n/sync/keywords`
- **Webhook**: `http://localhost:5678/webhook/keywords-sync-webhook`

### Workflow 4: Content Generation
- **Trigger**: Webhook only
- **Function**: Generates full blog content with blocks
- **Endpoint**: `POST /api/n8n/content-callback`
- **Webhook**: `http://localhost:5678/webhook/generate-content`

## 🔄 Integration Flow

```
Google Sheets → N8N Workflows → ArticleScribe Backend → Frontend
```

1. **Data Source**: Google Sheets (Company KT + Blog Data)
2. **Processing**: N8N workflows transform and enhance data
3. **API Integration**: N8N sends data to ArticleScribe endpoints
4. **Frontend**: ArticleScribe displays real-time data

## 🛠 Troubleshooting

### Common Issues:

#### 1. Credentials Not Working:
- Verify Google OAuth2 permissions
- Check API quotas and limits
- Ensure service account has sheet access

#### 2. Webhook Not Responding:
- Check N8N is running on port 5678
- Verify webhook URLs are correct
- Check ArticleScribe backend is running on port 5000

#### 3. Data Not Syncing:
- Check N8N execution logs
- Verify sheet IDs are correct
- Check ArticleScribe backend logs

### Debug Commands:
```bash
# Check N8N status
curl http://localhost:5678/healthz

# Check ArticleScribe N8N endpoints
curl http://localhost:5000/api/n8n/health
curl http://localhost:5000/api/n8n/status

# Test company sync
curl -X POST http://localhost:5000/api/n8n/sync/companies \
  -H "Content-Type: application/json" \
  -d '{"companies": [{"companyName": "Test", "servicesOffered": "Testing"}]}'
```

## 📈 Monitoring

### N8N Dashboard:
- Monitor workflow executions
- Check success/failure rates
- View execution logs

### ArticleScribe Integration:
- Keywords should appear in frontend
- Companies should load in dropdown
- Content generation should work via N8N

## 🎯 Next Steps

1. **Import all 4 workflows** into N8N
2. **Test each workflow** individually
3. **Monitor execution logs** for any issues
4. **Verify data sync** in ArticleScribe frontend
5. **Set up monitoring** for production use

## 📞 Support

If you encounter issues:
1. Check N8N execution logs
2. Verify ArticleScribe backend logs
3. Test individual API endpoints
4. Check Google Sheets permissions

Your N8N integration is now ready to provide real-time data sync between Google Sheets and ArticleScribe! 🚀
