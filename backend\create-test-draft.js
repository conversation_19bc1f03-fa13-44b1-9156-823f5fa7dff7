require('dotenv').config();
const draftService = require('./src/services/draft.service');

async function createTestDraft() {
  console.log('📝 CREATING TEST DRAFT FOR FRONTEND TESTING');
  console.log('==========================================');
  
  try {
    // Create a draft with the exact ID from the URL
    const draftId = 'draft_1753114749779'; // From the URL in the screenshot
    
    const draftData = {
      id: draftId,
      userId: 'test-user',
      selectedKeyword: 'AI automation tools',
      companyData: {
        companyName: 'TechSolutions Pro',
        serviceOverview: 'AI automation consulting and implementation services',
        brandVoice: 'professional, innovative, and approachable',
        targetAudience: 'small business owners and entrepreneurs looking to streamline operations'
      },
      finalMeta: {
        h1Title: 'Best AI Automation Tools for Small Business in 2025',
        metaDescription: 'Discover the top AI automation tools that can transform your small business operations, reduce costs, and boost productivity in 2025.',
        seoScore: 95,
        engagementScore: 90
      },
      keywordCluster: ['business automation', 'AI tools', 'workflow automation', 'productivity software', 'small business technology'],
      competitorAnalysis: {
        topics: [
          { title: 'Top Automation Tools', description: 'Comprehensive tool reviews and comparisons' },
          { title: 'Implementation Guide', description: 'Step-by-step setup and best practices' },
          { title: 'ROI Analysis', description: 'Cost-benefit analysis of automation tools' }
        ]
      },
      newsArticles: [
        { title: 'AI Automation Trends 2025', source: 'TechCrunch', publishedAt: '2025-01-15' },
        { title: 'Small Business AI Adoption Surges', source: 'Forbes', publishedAt: '2025-01-10' },
        { title: 'Automation Tools Market Report', source: 'Business Insider', publishedAt: '2025-01-08' }
      ],
      trends: {
        emergingTopics: ['AI-powered customer service', 'Automated marketing campaigns', 'Smart inventory management'],
        marketInsights: ['Growing demand for no-code solutions', 'Integration capabilities are key', 'ROI focus drives adoption']
      },
      status: 'meta_selected',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Create the draft with specific ID
    console.log('🚀 Creating draft with ID:', draftId);
    await draftService.createDraftWithId(draftId, draftData);
    
    console.log('✅ Test draft created successfully!');
    console.log('📊 Draft details:');
    console.log('  - ID:', draftId);
    console.log('  - Keyword:', draftData.selectedKeyword);
    console.log('  - H1:', draftData.finalMeta.h1Title);
    console.log('  - Company:', draftData.companyData.companyName);
    console.log('  - Status:', draftData.status);
    
    console.log('\n🎯 FRONTEND TESTING READY!');
    console.log('Now the frontend can generate real AI content for this draft.');
    console.log('URL: http://localhost:3000/blog/' + draftId + '/editor');
    
  } catch (error) {
    console.error('❌ Failed to create test draft:', error.message);
    console.error('Stack:', error.stack);
  }
}

createTestDraft().catch(console.error);
