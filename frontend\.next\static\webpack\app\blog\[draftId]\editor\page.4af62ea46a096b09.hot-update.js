"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[draftId]/editor/page",{

/***/ "(app-pages-browser)/./app/blog/[draftId]/editor/page.tsx":
/*!********************************************!*\
  !*** ./app/blog/[draftId]/editor/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_stepper_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/stepper-header */ \"(app-pages-browser)/./components/stepper-header.tsx\");\n/* harmony import */ var _components_content_block__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/content-block */ \"(app-pages-browser)/./components/content-block.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    const [draft, setDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [blocks, setBlocks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatingLinks, setGeneratingLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingBlock, setEditingBlock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editContent, setEditContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const draftId = params.draftId;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            loadDraftAndContent();\n        }\n    }[\"EditorPage.useEffect\"], [\n        draftId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // Calculate total word count\n            const total = blocks.reduce({\n                \"EditorPage.useEffect.total\": (sum, block)=>sum + (block.wordCount || 0)\n            }[\"EditorPage.useEffect.total\"], 0);\n            setWordCount(total);\n        }\n    }[\"EditorPage.useEffect\"], [\n        blocks\n    ]);\n    const loadDraftAndContent = async ()=>{\n        try {\n            // Load draft data first\n            const draftResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.getDraft(draftId);\n            if (draftResponse.success && draftResponse.data) {\n                const draftData = draftResponse.data;\n                setDraft(draftData);\n                // Check if structured content already exists\n                if (draftData.structuredContent && draftData.structuredContent.length > 0) {\n                    setBlocks(draftData.structuredContent);\n                } else {\n                    // Generate new structured content\n                    await generateContent();\n                }\n            }\n        } catch (error) {\n            toast({\n                title: \"Error loading draft\",\n                description: \"Failed to load draft data. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const generateContent = async ()=>{\n        try {\n            console.log('🚀 FRONTEND: Generating real AI content for draft:', draftId);\n            // Call backend API to generate real structured content\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateStructuredContent(draftId);\n            console.log('📡 FRONTEND: API Response:', response);\n            if (response.success && response.structuredContent) {\n                console.log('✅ FRONTEND: Using real AI-generated content with', response.structuredContent.length, 'blocks');\n                setBlocks(response.structuredContent);\n                toast({\n                    title: \"AI Content generated successfully!\",\n                    description: \"Generated \".concat(response.structuredContent.length, \" content blocks with Gemini 2.0 Flash.\")\n                });\n                return;\n            }\n            console.log('⚠️ FRONTEND: API response invalid, using fallback content');\n            // Fallback: Mock blog content blocks if API fails\n            const mockBlocks = [\n                {\n                    id: \"intro-1\",\n                    type: \"introduction\",\n                    content: \"In today's fast-paced business environment, small business owners are constantly looking for ways to streamline operations, reduce manual tasks, and focus on what matters most - growing their business. AI automation tools have emerged as game-changers, offering sophisticated solutions that were once only available to large enterprises.\\n\\nThis comprehensive guide will walk you through the best AI automation tools specifically designed for small businesses, helping you understand how to implement them effectively and maximize your return on investment. Whether you're looking to automate customer service, marketing campaigns, or internal processes, we've got you covered.\",\n                    editable: true,\n                    wordCount: 124\n                },\n                {\n                    id: \"feature-img-1\",\n                    type: \"image\",\n                    imageType: \"feature\",\n                    alt: \"AI automation tools dashboard showing various business processes being automated\",\n                    editable: false\n                },\n                {\n                    id: \"section-1\",\n                    type: \"section\",\n                    h2: \"What Are AI Automation Tools and Why Do Small Businesses Need Them?\",\n                    content: \"AI automation tools are software solutions that use artificial intelligence to perform tasks that typically require human intervention. These tools can analyze data, make decisions, and execute actions based on predefined rules or learned patterns.\\n\\nFor small businesses, AI automation offers several key benefits:\\n\\n• **Cost Reduction**: Automate repetitive tasks to reduce labor costs\\n• **Improved Accuracy**: Minimize human errors in data processing and analysis\\n• **24/7 Operations**: Keep critical processes running around the clock\\n• **Scalability**: Handle increased workload without proportional staff increases\\n• **Competitive Advantage**: Access enterprise-level capabilities at affordable prices\\n\\nThe key is choosing the right tools that align with your specific business needs and budget constraints.\",\n                    editable: true,\n                    wordCount: 156\n                },\n                {\n                    id: \"section-2\",\n                    type: \"section\",\n                    h2: \"Top 5 AI Automation Tools for Small Business Operations\",\n                    content: \"After extensive research and testing, we've identified the top AI automation tools that deliver exceptional value for small businesses:\\n\\n**1. Zapier with AI Features**\\nZapier's AI-powered automation connects over 5,000 apps, making it easy to create complex workflows without coding. Recent AI enhancements include smart suggestions and natural language processing for trigger creation.\\n\\n**2. HubSpot's AI-Powered CRM**\\nHubSpot offers AI-driven lead scoring, predictive analytics, and automated email sequences. Their free tier makes it accessible for startups and growing businesses.\\n\\n**3. Chatfuel for Customer Service**\\nThis AI chatbot platform handles customer inquiries 24/7, reducing response times and freeing up your team for more complex tasks.\\n\\n**4. Calendly with Smart Scheduling**\\nAI-powered scheduling that learns from your preferences and automatically optimizes meeting times based on participant availability and preferences.\\n\\n**5. QuickBooks AI for Financial Management**\\nAutomated expense categorization, invoice processing, and financial forecasting powered by machine learning algorithms.\",\n                    editable: true,\n                    wordCount: 189\n                },\n                {\n                    id: \"inblog-img-1\",\n                    type: \"image\",\n                    imageType: \"in-blog\",\n                    alt: \"Comparison chart showing different AI automation tools and their features\",\n                    editable: false\n                },\n                {\n                    id: \"section-3\",\n                    type: \"section\",\n                    h2: \"Implementation Strategy: How to Successfully Deploy AI Automation\",\n                    content: \"Successfully implementing AI automation in your small business requires a strategic approach:\\n\\n**Phase 1: Assessment and Planning (Week 1-2)**\\n• Identify repetitive, time-consuming tasks\\n• Map current workflows and processes\\n• Set clear goals and success metrics\\n• Determine budget and resource allocation\\n\\n**Phase 2: Tool Selection and Setup (Week 3-4)**\\n• Research and compare automation tools\\n• Start with free trials or basic plans\\n• Configure initial automations for low-risk processes\\n• Train team members on new tools\\n\\n**Phase 3: Testing and Optimization (Week 5-8)**\\n• Monitor automation performance closely\\n• Gather feedback from team members\\n• Make adjustments and improvements\\n• Gradually expand to more complex processes\\n\\n**Phase 4: Scale and Advanced Features (Month 3+)**\\n• Implement more sophisticated automations\\n• Integrate multiple tools for comprehensive workflows\\n• Analyze ROI and adjust strategy as needed\\n• Explore advanced AI features and capabilities\",\n                    editable: true,\n                    wordCount: 178\n                },\n                {\n                    id: \"section-4\",\n                    type: \"section\",\n                    h2: \"Measuring ROI: Key Metrics for AI Automation Success\",\n                    content: \"To ensure your AI automation investment pays off, track these essential metrics:\\n\\n**Time Savings Metrics:**\\n• Hours saved per week on automated tasks\\n• Reduction in manual data entry time\\n• Faster response times to customer inquiries\\n\\n**Cost Efficiency Metrics:**\\n• Reduction in operational costs\\n• Decreased need for additional staff\\n• Lower error rates and associated costs\\n\\n**Business Growth Metrics:**\\n• Increased lead generation and conversion rates\\n• Improved customer satisfaction scores\\n• Enhanced team productivity and focus on strategic tasks\\n\\n**Quality Improvements:**\\n• Reduced error rates in data processing\\n• More consistent customer experiences\\n• Better compliance with business processes\\n\\nMost small businesses see a positive ROI within 3-6 months of implementing AI automation tools, with some reporting up to 300% return on investment within the first year.\",\n                    editable: true,\n                    wordCount: 145\n                },\n                {\n                    id: \"inblog-img-2\",\n                    type: \"image\",\n                    imageType: \"in-blog\",\n                    alt: \"ROI dashboard showing positive returns from AI automation implementation\",\n                    editable: false\n                },\n                {\n                    id: \"conclusion-1\",\n                    type: \"conclusion\",\n                    content: \"AI automation tools are no longer a luxury reserved for large corporations – they're essential for small businesses looking to compete and thrive in today's digital landscape. By starting with simple automations and gradually expanding your capabilities, you can transform your operations, reduce costs, and focus on what you do best: serving your customers and growing your business.\\n\\nRemember, the key to successful AI automation is starting small, measuring results, and continuously optimizing your processes. Choose tools that integrate well with your existing systems, provide excellent support, and offer room for growth as your business expands.\\n\\nReady to get started? Begin by identifying one repetitive task in your business and explore how AI automation can help you reclaim those valuable hours for more strategic activities.\",\n                    editable: true,\n                    wordCount: 142\n                },\n                {\n                    id: \"references-1\",\n                    type: \"references\",\n                    content: '1. McKinsey Global Institute. (2023). \"The Age of AI: Artificial Intelligence and the Future of Work.\"\\n2. Small Business Administration. (2023). \"Technology Adoption in Small Businesses: 2023 Report.\"\\n3. Zapier. (2023). \"State of Business Automation Report 2023.\"\\n4. HubSpot Research. (2023). \"AI in Small Business: Adoption and Impact Study.\"\\n5. Deloitte. (2023). \"AI and Automation in Small and Medium Enterprises.\"',\n                    editable: true,\n                    wordCount: 67\n                }\n            ];\n            console.log('📝 FRONTEND: Using fallback mock content');\n            setBlocks(mockBlocks);\n            toast({\n                title: \"Using template content\",\n                description: \"API content generation failed, using template. Check console for details.\",\n                variant: \"destructive\"\n            });\n        } catch (error) {\n            console.error('❌ FRONTEND: Content generation error:', error);\n            toast({\n                title: \"Error generating content\",\n                description: \"Failed to generate blog content: \".concat(error.message),\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        setSaving(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.saveDraft(draftId, {\n                structuredContent: blocks,\n                status: 'content_generation'\n            });\n            toast({\n                title: \"Draft saved\",\n                description: \"Your blog draft has been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Save failed\",\n                description: \"Failed to save draft. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleGenerateLinks = async ()=>{\n        setGeneratingLinks(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateLinks(draftId);\n            toast({\n                title: \"Links generated\",\n                description: \"Internal and external links have been generated.\"\n            });\n            router.push(\"/blog/\".concat(draftId, \"/review\"));\n        } catch (error) {\n            toast({\n                title: \"Link generation failed\",\n                description: \"Failed to generate links. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setGeneratingLinks(false);\n        }\n    };\n    const handleEditBlock = (block)=>{\n        setEditingBlock(block);\n        setEditContent(block.content || \"\");\n        setCustomPrompt(\"\");\n    };\n    const handleRegenerateBlock = async (blockId, type)=>{\n        console.log(\"=== FRONTEND: Regenerating block \".concat(blockId, \" with type \").concat(type, \" ===\"));\n        console.log('Draft ID:', draftId);\n        console.log('Custom prompt:', type === \"ai\" ? customPrompt : undefined);\n        console.log('Edit content:', type === \"manual\" ? editContent : undefined);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.regenerateBlock(draftId, blockId, type, type === \"ai\" ? customPrompt : undefined, type === \"manual\" ? editContent : undefined);\n            console.log('=== FRONTEND: Regeneration response ===', response);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                setEditingBlock(null);\n                toast({\n                    title: \"Block updated\",\n                    description: \"Content block has been successfully updated.\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error regenerating block:', error);\n            toast({\n                title: \"Update failed\",\n                description: \"Failed to update content block. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteBlock = (blockId)=>{\n        setBlocks(blocks.filter((block)=>block.id !== blockId));\n        toast({\n            title: \"Block deleted\",\n            description: \"Content block has been removed.\"\n        });\n    };\n    const handleImageGenerate = async (blockId, description)=>{\n        console.log(\"Generating image for block \".concat(blockId, \" with description: \").concat(description));\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateImage(draftId, blockId, description);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                toast({\n                    title: \"Image generated successfully!\",\n                    description: \"AI has generated an image description and placeholder for your content.\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error generating image:', error);\n            toast({\n                title: \"Image generation failed\",\n                description: \"Failed to generate image. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImageUpload = async (blockId, file)=>{\n        console.log(\"Uploading image for block \".concat(blockId, \":\"), file.name);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.uploadImage(draftId, blockId, file);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                toast({\n                    title: \"Image uploaded\",\n                    description: \"Image has been uploaded successfully!\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n            toast({\n                title: \"Upload failed\",\n                description: \"Failed to upload image. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                    currentStep: 3,\n                    draftId: draftId\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-5xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                className: \"h-16 w-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-32 w-full\"\n                                }, i, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                currentStep: 3,\n                draftId: draftId\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Content Editor\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        wordCount,\n                                        \" words\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSaveDraft,\n                                    disabled: saving,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this),\n                                        saving ? \"Saving...\" : \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Preview\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleGenerateLinks,\n                                    disabled: generatingLinks,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        generatingLinks ? \"Generating...\" : \"Generate Links\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/blog/\".concat(draftId, \"/deploy\")),\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Deploy to WordPress\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-5xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: blocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_content_block__WEBPACK_IMPORTED_MODULE_12__.ContentBlock, {\n                                block: block,\n                                onEdit: ()=>handleEditBlock(block),\n                                onRegenerate: ()=>handleRegenerateBlock(block.id, \"ai\"),\n                                onDelete: block.editable ? ()=>handleDeleteBlock(block.id) : undefined,\n                                onImageGenerate: handleImageGenerate,\n                                onImageUpload: handleImageUpload\n                            }, block.id, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: !!editingBlock,\n                        onOpenChange: ()=>setEditingBlock(null),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                            className: \"max-w-4xl max-h-[80vh] overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                            children: \"Edit Content Block\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                            children: \"Modify the content manually or use AI to regenerate with a custom prompt\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Content\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                    value: editContent,\n                                                    onChange: (e)=>setEditContent(e.target.value),\n                                                    rows: 8,\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"AI Regeneration Prompt (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    value: customPrompt,\n                                                    onChange: (e)=>setCustomPrompt(e.target.value),\n                                                    placeholder: \"e.g., Make it more engaging, Add statistics, Simplify for beginners...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleRegenerateBlock((editingBlock === null || editingBlock === void 0 ? void 0 : editingBlock.id) || \"\", \"manual\"),\n                                                    className: \"flex-1\",\n                                                    children: \"Save Manual Changes\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleRegenerateBlock((editingBlock === null || editingBlock === void 0 ? void 0 : editingBlock.id) || \"\", \"ai\"),\n                                                    disabled: !customPrompt,\n                                                    variant: \"outline\",\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Regenerate with AI\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"itvx8VbRR5EQhXBtC+m1KKHomDw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/[draftId]/editor/page.tsx\n"));

/***/ })

});