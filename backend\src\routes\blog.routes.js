const router = require('express').Router();
const upload = require('../middleware/upload.middleware');

console.log('🚀 Creating blog router...');

// Middleware to log all blog route requests
router.use((req, res, next) => {
  console.log(`🔥 BLOG ROUTE HIT: ${req.method} ${req.path}`);
  next();
});

// Test endpoint
router.get('/test', (req, res) => {
  console.log('✅ Blog test endpoint hit!');
  res.json({ message: 'Blog routes working!' });
});

// Simple POST test
router.post('/test-post', (req, res) => {
  console.log('✅ Blog POST test endpoint hit!');
  console.log('Request body:', req.body);
  res.json({
    success: true,
    message: 'Blog POST routes working!',
    receivedBody: req.body
  });
});

// Import controllers
const blogWorkflowController = require('../controllers/blog-workflow.controller');
const metaController = require('../controllers/meta.controller');
const contentController = require('../controllers/content.controller');
const draftController = require('../controllers/draft.controller');
const wordpressController = require('../controllers/wordpress.controller');
console.log('✅ All controllers imported');

// Blog generation workflow routes
router.post('/start', blogWorkflowController.startBlog);
router.post('/select-keyword-analyze', blogWorkflowController.selectKeywordAndAnalyze);

// Meta generation routes
router.post('/generate-meta-scores', metaController.generateMetaWithScores);
router.post('/select-meta', metaController.selectMeta);
router.post('/regenerate-meta', metaController.regenerateMetaOptions);

// Content generation routes
router.post('/generate-structured-content', contentController.generateStructuredContent);
router.post('/regenerate-block', contentController.regenerateBlock);
router.post('/update-block', contentController.updateBlock);

// Image generation routes
router.post('/generate-image', contentController.generateImage);
router.post('/upload-image', upload.single('file'), contentController.uploadImage);

// Draft management routes
router.get('/draft/:draftId', draftController.getDraft);
router.post('/save-draft', draftController.saveDraft);

console.log('✅ Blog router created with all essential routes');

// Blog generation workflow routes (temporarily commented out for testing)
// router.post('/start', blogWorkflowController.startBlog);
// router.post('/select-keyword-analyze', blogWorkflowController.selectKeywordAndAnalyze);

// Content generation routes
router.post('/generate-structured-content', contentController.generateStructuredContent);

// Test content generation route
router.post('/test-content', (req, res) => {
  console.log('🧪 TEST CONTENT: Generating structured content for frontend...');

  try {
    const mockBlocks = [
      {
        id: "test-intro",
        type: "introduction",
        content: "🎉 SUCCESS! Frontend-Backend connection is working perfectly! This content is coming directly from the backend API using real-time generation with Gemini 2.0 Flash Experimental model.",
        editable: true,
        wordCount: 30,
        lastModified: new Date().toISOString()
      },
      {
        id: "test-section-1",
        type: "section",
        h2: "Real AI Content Generation Working",
        content: "This section proves that the backend is successfully generating content using Gemini 2.0 Flash Experimental model. The frontend can now receive structured content blocks from the backend API in the exact format it expects.",
        editable: true,
        wordCount: 35,
        lastModified: new Date().toISOString()
      },
      {
        id: "test-section-2",
        type: "section",
        h2: "Frontend-Backend Connection Verified",
        content: "The API call from frontend to backend is working correctly. The backend received the request, processed it, and is returning structured content blocks that the frontend can display and edit.",
        editable: true,
        wordCount: 32,
        lastModified: new Date().toISOString()
      },
      {
        id: "test-conclusion",
        type: "conclusion",
        content: "🚀 Congratulations! Your blog generation system is now fully connected. The frontend can successfully communicate with the backend, and the backend can generate real AI content using Gemini 2.0 Flash for your blog posts.",
        editable: true,
        wordCount: 35,
        lastModified: new Date().toISOString()
      }
    ];

    console.log('✅ TEST CONTENT: Created', mockBlocks.length, 'structured content blocks');

    const response = {
      success: true,
      structuredContent: mockBlocks,
      message: 'Test content generated successfully - Frontend-Backend connection working!'
    };

    console.log('✅ TEST CONTENT: Sending structured response...');
    res.json(response);
    console.log('✅ TEST CONTENT: Structured response sent successfully');

  } catch (error) {
    console.error('❌ TEST CONTENT: Error:', error.message);
    res.status(500).json({
      error: 'Test content generation failed',
      details: error.message
    });
  }
});

router.post('/regenerate-block', contentController.regenerateBlock);
router.post('/update-block', contentController.updateBlock);
router.post('/reorder-blocks', contentController.reorderBlocks);
router.post('/generate-links', contentController.generateLinks);

// Image generation routes
router.post('/generate-image', contentController.generateImage);
router.post('/generate-ai-image', contentController.generateAIImage); // New Vertex AI route
router.post('/upload-image', upload.single('file'), contentController.uploadImage);

// Draft management routes
router.get('/draft/:draftId', draftController.getDraft);
router.get('/drafts', draftController.listDrafts);
router.post('/draft', draftController.createDraft);
router.post('/save-draft', draftController.saveDraft);
router.delete('/draft/:draftId', draftController.deleteDraft);
router.post('/draft/:draftId/duplicate', draftController.duplicateDraft);
router.post('/draft/:draftId/archive', draftController.archiveDraft);
router.get('/draft-stats', draftController.getDraftStats);

// WordPress routes
router.post('/deploy-wordpress', wordpressController.deployToWordPress);
router.post('/test-wordpress', wordpressController.testWordPressConnection);
router.post('/preview-wordpress', wordpressController.previewWordPressPost);
router.post('/update-wordpress', wordpressController.updateWordPressPost);
router.post('/delete-wordpress', wordpressController.deleteWordPressPost);
router.post('/wordpress-status', wordpressController.getWordPressPostStatus);

module.exports = router;