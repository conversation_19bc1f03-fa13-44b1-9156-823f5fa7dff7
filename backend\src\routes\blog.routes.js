const router = require('express').Router();

// Import smaller, focused controllers
const blogWorkflowController = require('../controllers/blog-workflow.controller');
const metaController = require('../controllers/meta.controller');
const contentController = require('../controllers/content.controller');
const draftController = require('../controllers/draft.controller');
const wordpressController = require('../controllers/wordpress.controller');

// Blog generation workflow routes
router.post('/start', blogWorkflowController.startBlog);
router.post('/select-keyword-analyze', blogWorkflowController.selectKeywordAndAnalyze);

// Meta generation routes
router.post('/generate-meta-scores', metaController.generateMetaWithScores);
router.post('/select-meta', metaController.selectMeta);
router.post('/regenerate-meta', metaController.regenerateMetaOptions);

// Content generation routes
router.post('/generate-structured-content', contentController.generateStructuredContent);
router.post('/regenerate-block', contentController.regenerateBlock);
router.post('/update-block', contentController.updateBlock);
router.post('/reorder-blocks', contentController.reorderBlocks);
router.post('/generate-links', contentController.generateLinks);

// Draft management routes
router.get('/draft/:draftId', draftController.getDraft);
router.get('/drafts', draftController.listDrafts);
router.post('/draft', draftController.createDraft);
router.post('/save-draft', draftController.saveDraft);
router.delete('/draft/:draftId', draftController.deleteDraft);
router.post('/draft/:draftId/duplicate', draftController.duplicateDraft);
router.post('/draft/:draftId/archive', draftController.archiveDraft);
router.get('/draft-stats', draftController.getDraftStats);

// WordPress routes
router.post('/deploy-wordpress', wordpressController.deployToWordPress);
router.post('/test-wordpress', wordpressController.testWordPressConnection);
router.post('/preview-wordpress', wordpressController.previewWordPressPost);
router.post('/update-wordpress', wordpressController.updateWordPressPost);
router.post('/delete-wordpress', wordpressController.deleteWordPressPost);
router.post('/wordpress-status', wordpressController.getWordPressPostStatus);

module.exports = router;