const router = require('express').Router();

console.log('🚀 Creating blog router...');

// Middleware to log all blog route requests
router.use((req, res, next) => {
  console.log(`🔥 BLOG ROUTE HIT: ${req.method} ${req.path}`);
  next();
});

// Test endpoint
router.get('/test', (req, res) => {
  console.log('✅ Blog test endpoint hit!');
  res.json({ message: 'Blog routes working!' });
});

console.log('✅ Blog router created with test route');

// Blog generation workflow routes (temporarily commented out for testing)
// router.post('/start', blogWorkflowController.startBlog);
// router.post('/select-keyword-analyze', blogWorkflowController.selectKeywordAndAnalyze);

// Temporarily commenting out all routes for testing
/*
// Meta generation routes
router.post('/generate-meta-scores', metaController.generateMetaWithScores);
router.post('/select-meta', metaController.selectMeta);
router.post('/regenerate-meta', metaController.regenerateMetaOptions);

// Content generation routes
router.post('/generate-structured-content', contentController.generateStructuredContent);
router.post('/regenerate-block', contentController.regenerateBlock);
router.post('/update-block', contentController.updateBlock);
router.post('/reorder-blocks', contentController.reorderBlocks);
router.post('/generate-links', contentController.generateLinks);

// Image generation routes
router.post('/generate-image', contentController.generateImage);
router.post('/upload-image', upload.single('file'), contentController.uploadImage);

// Draft management routes
router.get('/draft/:draftId', draftController.getDraft);
router.get('/drafts', draftController.listDrafts);
router.post('/draft', draftController.createDraft);
router.post('/save-draft', draftController.saveDraft);
router.delete('/draft/:draftId', draftController.deleteDraft);
router.post('/draft/:draftId/duplicate', draftController.duplicateDraft);
router.post('/draft/:draftId/archive', draftController.archiveDraft);
router.get('/draft-stats', draftController.getDraftStats);

// WordPress routes
router.post('/deploy-wordpress', wordpressController.deployToWordPress);
router.post('/test-wordpress', wordpressController.testWordPressConnection);
router.post('/preview-wordpress', wordpressController.previewWordPressPost);
router.post('/update-wordpress', wordpressController.updateWordPressPost);
router.post('/delete-wordpress', wordpressController.deleteWordPressPost);
router.post('/wordpress-status', wordpressController.getWordPressPostStatus);
*/

module.exports = router;