const router = require('express').Router();
const upload = require('../middleware/upload.middleware');

// Test endpoint
router.get('/test', (req, res) => {
  console.log('Blog test endpoint hit!');
  res.json({ message: 'Blog routes working!' });
});

// Import smaller, focused controllers
console.log('Importing controllers...');
const blogWorkflowController = require('../controllers/blog-workflow.controller');
console.log('✅ Blog workflow controller imported');
const metaController = require('../controllers/meta.controller');
console.log('✅ Meta controller imported');
const contentController = require('../controllers/content.controller');
console.log('✅ Content controller imported');
const draftController = require('../controllers/draft.controller');
console.log('✅ Draft controller imported');
const wordpressController = require('../controllers/wordpress.controller');
console.log('✅ WordPress controller imported');

// Blog generation workflow routes
router.post('/start', blogWorkflowController.startBlog);
router.post('/select-keyword-analyze', blogWorkflowController.selectKeywordAndAnalyze);

// Meta generation routes
router.post('/generate-meta-scores', metaController.generateMetaWithScores);
router.post('/select-meta', metaController.selectMeta);
router.post('/regenerate-meta', metaController.regenerateMetaOptions);

// Content generation routes
router.post('/generate-structured-content', contentController.generateStructuredContent);
router.post('/regenerate-block', contentController.regenerateBlock);
router.post('/update-block', contentController.updateBlock);
router.post('/reorder-blocks', contentController.reorderBlocks);
router.post('/generate-links', contentController.generateLinks);

// Image generation routes
router.post('/generate-image', contentController.generateImage);
router.post('/upload-image', upload.single('file'), contentController.uploadImage);

// Draft management routes
router.get('/draft/:draftId', draftController.getDraft);
router.get('/drafts', draftController.listDrafts);
router.post('/draft', draftController.createDraft);
router.post('/save-draft', draftController.saveDraft);
router.delete('/draft/:draftId', draftController.deleteDraft);
router.post('/draft/:draftId/duplicate', draftController.duplicateDraft);
router.post('/draft/:draftId/archive', draftController.archiveDraft);
router.get('/draft-stats', draftController.getDraftStats);

// WordPress routes
router.post('/deploy-wordpress', wordpressController.deployToWordPress);
router.post('/test-wordpress', wordpressController.testWordPressConnection);
router.post('/preview-wordpress', wordpressController.previewWordPressPost);
router.post('/update-wordpress', wordpressController.updateWordPressPost);
router.post('/delete-wordpress', wordpressController.deleteWordPressPost);
router.post('/wordpress-status', wordpressController.getWordPressPostStatus);

module.exports = router;