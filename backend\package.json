{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.10.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "googleapis": "^153.0.0", "multer": "^2.0.2"}, "description": "", "devDependencies": {"nodemon": "^3.1.10"}}