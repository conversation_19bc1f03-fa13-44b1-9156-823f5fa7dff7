require('dotenv').config();
const vertexAIImageService = require('./src/services/vertex-ai-image.service');

async function testVertexAIImage() {
  console.log('🎨 TESTING VERTEX AI IMAGE GENERATION');
  console.log('=====================================');
  
  const testPrompts = [
    "Professional business team using AI automation tools in modern office",
    "Dashboard showing AI automation metrics and productivity gains",
    "Small business owner working with AI chatbot on laptop"
  ];
  
  for (let i = 0; i < testPrompts.length; i++) {
    const prompt = testPrompts[i];
    console.log(`\n🧪 Test ${i + 1}: ${prompt}`);
    console.log('=' .repeat(50));
    
    try {
      // Test prompt enhancement
      console.log('🔧 Enhancing prompt with AI...');
      const enhancedPrompt = await vertexAIImageService.enhancePrompt(prompt, 'AI automation tools');
      console.log('✅ Enhanced prompt:', enhancedPrompt.substring(0, 100) + '...');
      
      // Test image generation
      console.log('\n🎨 Generating image...');
      const result = await vertexAIImageService.generateImage(enhancedPrompt);
      
      if (result.success) {
        console.log('✅ Image generation successful!');
        console.log('📊 Result details:');
        console.log('  - MIME Type:', result.mimeType);
        console.log('  - Base64 length:', result.imageBase64?.length || 0);
        console.log('  - Fallback mode:', result.fallback ? 'Yes' : 'No');
        
        if (result.fallback) {
          console.log('  - Placeholder URL:', result.placeholderUrl);
          console.log('  - Message:', result.message);
        }
        
        // Save base64 to file for testing (first 100 chars)
        console.log('  - Base64 preview:', result.imageBase64?.substring(0, 100) + '...');
        
      } else {
        console.log('❌ Image generation failed');
      }
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    }
  }
  
  console.log('\n🎉 VERTEX AI IMAGE TESTING COMPLETED!');
}

testVertexAIImage().catch(console.error);
