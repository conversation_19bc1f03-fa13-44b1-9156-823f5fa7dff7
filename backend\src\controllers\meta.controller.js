// Meta Controller - Handles meta title, description generation and selection
const draftService = require('../services/draft.service');
const metaService = require('../services/meta.service');

class MetaController {
  // Generate meta titles, descriptions with SEO scores
  async generateMetaWithScores(req, res) {
    try {
      const { draftId } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Generate 3 meta options with SEO scores
      const metaOptions = await metaService.generateMetaOptions(
        draft.selectedKeyword,
        draft.companyData,
        draft.competitorAnalysis
      );

      // Update draft
      await draftService.updateDraft(draftId, {
        metaOptions,
        status: 'meta_selection'
      });

      res.json({
        success: true,
        metaOptions,
        message: 'Meta options generated successfully'
      });

    } catch (error) {
      console.error('Error generating meta:', error);
      res.status(500).json({ error: 'Failed to generate meta options' });
    }
  }

  // Select final meta and proceed to content generation
  async selectMeta(req, res) {
    try {
      const { draftId, selectedMetaIndex } = req.body;
      
      if (!draftId || selectedMetaIndex === undefined) {
        return res.status(400).json({ error: 'Draft ID and selected meta index are required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      const selectedMeta = draft.metaOptions[selectedMetaIndex];
      if (!selectedMeta) {
        return res.status(400).json({ error: 'Invalid meta option selected' });
      }

      // Update draft with final meta
      await draftService.updateDraft(draftId, {
        finalMeta: selectedMeta,
        status: 'content_generation'
      });

      res.json({
        success: true,
        finalMeta: selectedMeta,
        message: 'Meta selected successfully'
      });

    } catch (error) {
      console.error('Error selecting meta:', error);
      res.status(500).json({ error: 'Failed to select meta' });
    }
  }

  // Regenerate meta options
  async regenerateMetaOptions(req, res) {
    try {
      const { draftId, optionIndex } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Regenerate specific meta option or all if no index provided
      const newMetaOptions = await metaService.regenerateMetaOptions(
        draft.selectedKeyword,
        draft.companyData,
        draft.competitorAnalysis,
        optionIndex
      );

      // Update draft with new meta options
      const updatedMetaOptions = optionIndex !== undefined 
        ? draft.metaOptions.map((option, index) => 
            index === optionIndex ? newMetaOptions[0] : option
          )
        : newMetaOptions;

      await draftService.updateDraft(draftId, {
        metaOptions: updatedMetaOptions
      });

      res.json({
        success: true,
        metaOptions: updatedMetaOptions,
        message: 'Meta options regenerated successfully'
      });

    } catch (error) {
      console.error('Error regenerating meta options:', error);
      res.status(500).json({ error: 'Failed to regenerate meta options' });
    }
  }
}

module.exports = new MetaController();
