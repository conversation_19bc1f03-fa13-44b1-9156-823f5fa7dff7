"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { Save, Eye, Link, RefreshCw, FileText } from "lucide-react"
import type { BlogBlock, Draft } from "@/types/api"
import { api } from "@/lib/api"
import { StepperHeader } from "@/components/stepper-header"
import { ContentBlock } from "@/components/content-block"

export default function EditorPage() {
  const [draft, setDraft] = useState<Draft | null>(null)
  const [blocks, setBlocks] = useState<BlogBlock[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [generatingLinks, setGeneratingLinks] = useState(false)
  const [editingBlock, setEditingBlock] = useState<BlogBlock | null>(null)
  const [editContent, setEditContent] = useState("")
  const [customPrompt, setCustomPrompt] = useState("")
  const [wordCount, setWordCount] = useState(0)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  const draftId = params.draftId as string

  useEffect(() => {
    loadDraftAndContent()
  }, [draftId])

  useEffect(() => {
    // Calculate total word count
    const total = blocks.reduce((sum, block) => sum + (block.wordCount || 0), 0)
    setWordCount(total)
  }, [blocks])

  const loadDraftAndContent = async () => {
    try {
      // Load draft data first
      const draftResponse = await api.getDraft(draftId)
      if (draftResponse.success && draftResponse.data) {
        const draftData = draftResponse.data
        setDraft(draftData)

        // Check if structured content already exists
        if (draftData.structuredContent && draftData.structuredContent.length > 0) {
          setBlocks(draftData.structuredContent)
        } else {
          // Generate new structured content
          await generateContent()
        }
      }
    } catch (error) {
      toast({
        title: "Error loading draft",
        description: "Failed to load draft data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const generateContent = async () => {
    try {
      console.log('🚀 FRONTEND: Generating real AI content for draft:', draftId)

      // Call backend API to generate real structured content
      const response = await api.generateStructuredContent(draftId)
      console.log('📡 FRONTEND: API Response:', response)

      if (response.success && response.structuredContent) {
        console.log('✅ FRONTEND: Using real AI-generated content with', response.structuredContent.length, 'blocks')
        setBlocks(response.structuredContent)

        toast({
          title: "AI Content generated successfully!",
          description: `Generated ${response.structuredContent.length} content blocks with Gemini 2.0 Flash.`,
        })
        return
      }

      console.log('⚠️ FRONTEND: API response invalid, using fallback content')
      // Fallback: Mock blog content blocks if API fails
      const mockBlocks = [
        {
          id: "intro-1",
          type: "introduction" as const,
          content:
            "In today's fast-paced business environment, small business owners are constantly looking for ways to streamline operations, reduce manual tasks, and focus on what matters most - growing their business. AI automation tools have emerged as game-changers, offering sophisticated solutions that were once only available to large enterprises.\n\nThis comprehensive guide will walk you through the best AI automation tools specifically designed for small businesses, helping you understand how to implement them effectively and maximize your return on investment. Whether you're looking to automate customer service, marketing campaigns, or internal processes, we've got you covered.",
          editable: true,
          wordCount: 124,
        },
        {
          id: "feature-img-1",
          type: "image" as const,
          imageType: "feature" as const,
          alt: "AI automation tools dashboard showing various business processes being automated",
          editable: false,
        },
        {
          id: "section-1",
          type: "section" as const,
          h2: "What Are AI Automation Tools and Why Do Small Businesses Need Them?",
          content:
            "AI automation tools are software solutions that use artificial intelligence to perform tasks that typically require human intervention. These tools can analyze data, make decisions, and execute actions based on predefined rules or learned patterns.\n\nFor small businesses, AI automation offers several key benefits:\n\n• **Cost Reduction**: Automate repetitive tasks to reduce labor costs\n• **Improved Accuracy**: Minimize human errors in data processing and analysis\n• **24/7 Operations**: Keep critical processes running around the clock\n• **Scalability**: Handle increased workload without proportional staff increases\n• **Competitive Advantage**: Access enterprise-level capabilities at affordable prices\n\nThe key is choosing the right tools that align with your specific business needs and budget constraints.",
          editable: true,
          wordCount: 156,
        },
        {
          id: "section-2",
          type: "section" as const,
          h2: "Top 5 AI Automation Tools for Small Business Operations",
          content:
            "After extensive research and testing, we've identified the top AI automation tools that deliver exceptional value for small businesses:\n\n**1. Zapier with AI Features**\nZapier's AI-powered automation connects over 5,000 apps, making it easy to create complex workflows without coding. Recent AI enhancements include smart suggestions and natural language processing for trigger creation.\n\n**2. HubSpot's AI-Powered CRM**\nHubSpot offers AI-driven lead scoring, predictive analytics, and automated email sequences. Their free tier makes it accessible for startups and growing businesses.\n\n**3. Chatfuel for Customer Service**\nThis AI chatbot platform handles customer inquiries 24/7, reducing response times and freeing up your team for more complex tasks.\n\n**4. Calendly with Smart Scheduling**\nAI-powered scheduling that learns from your preferences and automatically optimizes meeting times based on participant availability and preferences.\n\n**5. QuickBooks AI for Financial Management**\nAutomated expense categorization, invoice processing, and financial forecasting powered by machine learning algorithms.",
          editable: true,
          wordCount: 189,
        },
        {
          id: "inblog-img-1",
          type: "image" as const,
          imageType: "in-blog" as const,
          alt: "Comparison chart showing different AI automation tools and their features",
          editable: false,
        },
        {
          id: "section-3",
          type: "section" as const,
          h2: "Implementation Strategy: How to Successfully Deploy AI Automation",
          content:
            "Successfully implementing AI automation in your small business requires a strategic approach:\n\n**Phase 1: Assessment and Planning (Week 1-2)**\n• Identify repetitive, time-consuming tasks\n• Map current workflows and processes\n• Set clear goals and success metrics\n• Determine budget and resource allocation\n\n**Phase 2: Tool Selection and Setup (Week 3-4)**\n• Research and compare automation tools\n• Start with free trials or basic plans\n• Configure initial automations for low-risk processes\n• Train team members on new tools\n\n**Phase 3: Testing and Optimization (Week 5-8)**\n• Monitor automation performance closely\n• Gather feedback from team members\n• Make adjustments and improvements\n• Gradually expand to more complex processes\n\n**Phase 4: Scale and Advanced Features (Month 3+)**\n• Implement more sophisticated automations\n• Integrate multiple tools for comprehensive workflows\n• Analyze ROI and adjust strategy as needed\n• Explore advanced AI features and capabilities",
          editable: true,
          wordCount: 178,
        },
        {
          id: "section-4",
          type: "section" as const,
          h2: "Measuring ROI: Key Metrics for AI Automation Success",
          content:
            "To ensure your AI automation investment pays off, track these essential metrics:\n\n**Time Savings Metrics:**\n• Hours saved per week on automated tasks\n• Reduction in manual data entry time\n• Faster response times to customer inquiries\n\n**Cost Efficiency Metrics:**\n• Reduction in operational costs\n• Decreased need for additional staff\n• Lower error rates and associated costs\n\n**Business Growth Metrics:**\n• Increased lead generation and conversion rates\n• Improved customer satisfaction scores\n• Enhanced team productivity and focus on strategic tasks\n\n**Quality Improvements:**\n• Reduced error rates in data processing\n• More consistent customer experiences\n• Better compliance with business processes\n\nMost small businesses see a positive ROI within 3-6 months of implementing AI automation tools, with some reporting up to 300% return on investment within the first year.",
          editable: true,
          wordCount: 145,
        },
        {
          id: "inblog-img-2",
          type: "image" as const,
          imageType: "in-blog" as const,
          alt: "ROI dashboard showing positive returns from AI automation implementation",
          editable: false,
        },
        {
          id: "conclusion-1",
          type: "conclusion" as const,
          content:
            "AI automation tools are no longer a luxury reserved for large corporations – they're essential for small businesses looking to compete and thrive in today's digital landscape. By starting with simple automations and gradually expanding your capabilities, you can transform your operations, reduce costs, and focus on what you do best: serving your customers and growing your business.\n\nRemember, the key to successful AI automation is starting small, measuring results, and continuously optimizing your processes. Choose tools that integrate well with your existing systems, provide excellent support, and offer room for growth as your business expands.\n\nReady to get started? Begin by identifying one repetitive task in your business and explore how AI automation can help you reclaim those valuable hours for more strategic activities.",
          editable: true,
          wordCount: 142,
        },
        {
          id: "references-1",
          type: "references" as const,
          content:
            '1. McKinsey Global Institute. (2023). "The Age of AI: Artificial Intelligence and the Future of Work."\n2. Small Business Administration. (2023). "Technology Adoption in Small Businesses: 2023 Report."\n3. Zapier. (2023). "State of Business Automation Report 2023."\n4. HubSpot Research. (2023). "AI in Small Business: Adoption and Impact Study."\n5. Deloitte. (2023). "AI and Automation in Small and Medium Enterprises."',
          editable: true,
          wordCount: 67,
        },
      ]

      console.log('📝 FRONTEND: Using fallback mock content')
      setBlocks(mockBlocks)

      toast({
        title: "Using template content",
        description: "API content generation failed, using template. Check console for details.",
        variant: "destructive",
      })
    } catch (error) {
      console.error('❌ FRONTEND: Content generation error:', error)
      toast({
        title: "Error generating content",
        description: `Failed to generate blog content: ${error.message}`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSaveDraft = async () => {
    setSaving(true)
    try {
      await api.saveDraft(draftId, {
        structuredContent: blocks,
        status: 'content_generation'
      })
      toast({
        title: "Draft saved",
        description: "Your blog draft has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Save failed",
        description: "Failed to save draft. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleGenerateLinks = async () => {
    setGeneratingLinks(true)
    try {
      await api.generateLinks(draftId)
      toast({
        title: "Links generated",
        description: "Internal and external links have been generated.",
      })
      router.push(`/blog/${draftId}/review`)
    } catch (error) {
      toast({
        title: "Link generation failed",
        description: "Failed to generate links. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGeneratingLinks(false)
    }
  }

  const handleEditBlock = (block: BlogBlock) => {
    setEditingBlock(block)
    setEditContent(block.content || "")
    setCustomPrompt("")
  }

  const handleRegenerateBlock = async (blockId: string, type: "ai" | "manual") => {
    console.log(`=== FRONTEND: Regenerating block ${blockId} with type ${type} ===`);
    console.log('Draft ID:', draftId);
    console.log('Custom prompt:', type === "ai" ? customPrompt : undefined);
    console.log('Edit content:', type === "manual" ? editContent : undefined);

    try {
      const response = await api.regenerateBlock(
        draftId,
        blockId,
        type,
        type === "ai" ? customPrompt : undefined,
        type === "manual" ? editContent : undefined,
      )

      console.log('=== FRONTEND: Regeneration response ===', response);

      if (response.success && response.block) {
        setBlocks(
          blocks.map((block) =>
            block.id === blockId ? { ...block, ...response.block } : block,
          ),
        )

        setEditingBlock(null)
        toast({
          title: "Block updated",
          description: "Content block has been successfully updated.",
        })
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error regenerating block:', error);
      toast({
        title: "Update failed",
        description: "Failed to update content block. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteBlock = (blockId: string) => {
    setBlocks(blocks.filter((block) => block.id !== blockId))
    toast({
      title: "Block deleted",
      description: "Content block has been removed.",
    })
  }

  const handleImageGenerate = async (blockId: string, description: string) => {
    console.log(`Generating image for block ${blockId} with description: ${description}`);
    try {
      const response = await api.generateImage(draftId, blockId, description);

      if (response.success && response.block) {
        setBlocks(blocks.map(block =>
          block.id === blockId ? { ...block, ...response.block } : block
        ));

        toast({
          title: "Image generated successfully!",
          description: "AI has generated an image description and placeholder for your content.",
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast({
        title: "Image generation failed",
        description: "Failed to generate image. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handleImageUpload = async (blockId: string, file: File) => {
    console.log(`Uploading image for block ${blockId}:`, file.name);
    try {
      const response = await api.uploadImage(draftId, blockId, file);

      if (response.success && response.block) {
        setBlocks(blocks.map(block =>
          block.id === blockId ? { ...block, ...response.block } : block
        ));

        toast({
          title: "Image uploaded",
          description: "Image has been uploaded successfully!",
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Upload failed",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={3} draftId={draftId} />
        <main className="max-w-5xl mx-auto px-6 py-8">
          <div className="space-y-6">
            <Skeleton className="h-16 w-full" />
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-32 w-full" />
            ))}
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StepperHeader currentStep={3} draftId={draftId} />

      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-5xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-semibold">Content Editor</h1>
            <Badge variant="outline" className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {wordCount} words
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button onClick={handleSaveDraft} disabled={saving} variant="outline" size="sm">
              <Save className="h-4 w-4 mr-1" />
              {saving ? "Saving..." : "Save Draft"}
            </Button>

            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-1" />
              Preview
            </Button>

            <Button
              onClick={handleGenerateLinks}
              disabled={generatingLinks}
              variant="outline"
              size="sm"
            >
              <Link className="h-4 w-4 mr-1" />
              {generatingLinks ? "Generating..." : "Generate Links"}
            </Button>

            <Button
              onClick={() => router.push(`/blog/${draftId}/deploy`)}
              className="bg-green-600 hover:bg-green-700"
              size="sm"
            >
              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Deploy to WordPress
            </Button>
          </div>
        </div>
      </div>

      <main className="max-w-5xl mx-auto px-6 py-8">
        <div className="space-y-6">
          {blocks.map((block, index) => (
            <ContentBlock
              key={block.id}
              block={block}
              onEdit={() => handleEditBlock(block)}
              onRegenerate={() => handleRegenerateBlock(block.id, "ai")}
              onDelete={block.editable ? () => handleDeleteBlock(block.id) : undefined}
              onImageGenerate={handleImageGenerate}
              onImageUpload={handleImageUpload}
            />
          ))}
        </div>

        {/* Edit Modal */}
        <Dialog open={!!editingBlock} onOpenChange={() => setEditingBlock(null)}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Content Block</DialogTitle>
              <DialogDescription>
                Modify the content manually or use AI to regenerate with a custom prompt
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Content</label>
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  rows={8}
                  className="w-full"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">AI Regeneration Prompt (Optional)</label>
                <Input
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="e.g., Make it more engaging, Add statistics, Simplify for beginners..."
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={() => handleRegenerateBlock(editingBlock?.id || "", "manual")} className="flex-1">
                  Save Manual Changes
                </Button>

                <Button
                  onClick={() => handleRegenerateBlock(editingBlock?.id || "", "ai")}
                  disabled={!customPrompt}
                  variant="outline"
                  className="flex-1"
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Regenerate with AI
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  )
}
