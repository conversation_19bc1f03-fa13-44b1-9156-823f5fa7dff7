"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, Image as ImageIcon, Download } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { api } from "@/lib/api"

interface AIImageGeneratorProps {
  keyword?: string
  onImageGenerated?: (imageData: { imageBase64: string; mimeType: string; prompt: string }) => void
}

export function AIImageGenerator({ keyword, onImageGenerated }: AIImageGeneratorProps) {
  const [prompt, setPrompt] = useState("")
  const [loading, setLoading] = useState(false)
  const [generatedImage, setGeneratedImage] = useState<{
    imageBase64: string
    mimeType: string
    originalPrompt: string
    enhancedPrompt: string
    placeholderUrl?: string
    fallback?: boolean
  } | null>(null)

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt required",
        description: "Please enter a description for the image you want to generate.",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      console.log('🎨 Generating AI image with prompt:', prompt)
      const response = await api.generateAIImage(prompt, keyword)
      console.log('📸 Image generation response:', response)

      if (response.success) {
        setGeneratedImage({
          imageBase64: response.imageBase64,
          mimeType: response.mimeType,
          originalPrompt: response.originalPrompt,
          enhancedPrompt: response.enhancedPrompt,
          placeholderUrl: response.placeholderUrl,
          fallback: response.fallback
        })

        toast({
          title: "Image generated successfully!",
          description: response.fallback 
            ? "Using placeholder image - configure Google Cloud for AI generation"
            : "AI image generated successfully",
        })

        // Callback to parent component
        if (onImageGenerated) {
          onImageGenerated({
            imageBase64: response.imageBase64,
            mimeType: response.mimeType,
            prompt: response.enhancedPrompt
          })
        }
      } else {
        throw new Error('Image generation failed')
      }
    } catch (error) {
      console.error('❌ Image generation error:', error)
      toast({
        title: "Image generation failed",
        description: `Failed to generate image: ${error.message}`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const downloadImage = () => {
    if (!generatedImage) return

    try {
      // Create download link for base64 image
      const link = document.createElement('a')
      link.href = `data:${generatedImage.mimeType};base64,${generatedImage.imageBase64}`
      link.download = `ai-generated-${Date.now()}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Image downloaded",
        description: "Image has been downloaded to your device.",
      })
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download image. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="h-5 w-5" />
          AI Image Generator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Image Description</label>
          <Input
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder={`Professional ${keyword || 'business'} image, modern office setting...`}
            disabled={loading}
          />
          {keyword && (
            <p className="text-xs text-muted-foreground">
              Context: {keyword}
            </p>
          )}
        </div>

        <Button 
          onClick={handleGenerate} 
          disabled={loading || !prompt.trim()}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Generating Image...
            </>
          ) : (
            <>
              <ImageIcon className="h-4 w-4 mr-2" />
              Generate AI Image
            </>
          )}
        </Button>

        {generatedImage && (
          <div className="space-y-3">
            <div className="border rounded-lg p-4 bg-muted/50">
              <h4 className="font-medium mb-2">Generated Image</h4>
              
              {generatedImage.fallback && generatedImage.placeholderUrl ? (
                <div className="space-y-2">
                  <img 
                    src={generatedImage.placeholderUrl} 
                    alt="Generated placeholder" 
                    className="w-full h-48 object-cover rounded border"
                  />
                  <p className="text-xs text-amber-600">
                    📸 Placeholder image - configure Google Cloud credentials for AI generation
                  </p>
                </div>
              ) : (
                <img 
                  src={`data:${generatedImage.mimeType};base64,${generatedImage.imageBase64}`}
                  alt="AI Generated" 
                  className="w-full h-48 object-cover rounded border"
                />
              )}
              
              <div className="mt-3 space-y-2">
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Original Prompt:</p>
                  <p className="text-sm">{generatedImage.originalPrompt}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-muted-foreground">Enhanced Prompt:</p>
                  <p className="text-sm text-muted-foreground">{generatedImage.enhancedPrompt}</p>
                </div>
              </div>

              <Button 
                onClick={downloadImage}
                variant="outline" 
                size="sm" 
                className="mt-3 w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Image
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
