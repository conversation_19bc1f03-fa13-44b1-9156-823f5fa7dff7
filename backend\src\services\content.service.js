class ContentService {
  async generateWordPressFormattedContent(meta, keyword, keywordCluster, companyData, competitorAnalysis, trends, newsArticles = []) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    // Prepare news articles context
    const newsContext = newsArticles.length > 0
      ? `\n      News Articles for Reference:\n${newsArticles.map((article, index) =>
          `      ${index + 1}. "${article.title}" - ${article.source} (${article.publishedAt})`
        ).join('\n')}\n`
      : '';

    const prompt = `
      Create a comprehensive blog post with this EXACT structure:

      Title: ${meta.h1Title || meta.h1 || `${keyword} - ${companyData.companyName}`}
      Meta Description: ${meta.metaDescription || `Learn about ${keyword} with ${companyData.companyName}`}
      Primary Keyword: ${keyword}
      Word Count: 2000-2500 words
      Company: ${companyData.companyName}
      Year: ${currentYear}
      ${newsContext}
      Competitor H2 topics to cover: ${competitorAnalysis?.commonH2Topics?.join(', ') || 'No competitor data'}
      Trends to incorporate: ${trends?.currentTrends?.join(', ') || 'No trends data'}
      Keywords to naturally include: ${keywordCluster?.lsiKeywords?.join(', ') || keywordCluster?.join(', ') || 'No additional keywords'}
      
      REQUIRED STRUCTURE:
      1. Introduction (150-200 words) - Hook, problem statement, what reader will learn
      2. Feature Image - Description for hero image showcasing the main topic
      3. 5-6 H2 Sections, each with:
         - H2 heading (keyword-optimized)
         - 3-4 paragraphs (250-400 words total)
         - Include statistics and data points
         - Include 1-2 internal links to company blog posts (use format: <a href="/blog/related-topic">anchor text</a>)
         - Reference news articles naturally when relevant
      4. In-blog Image 1 - After 2nd H2 section
      5. In-blog Image 2 - After 4th H2 section
      6. Conclusion (150-200 words) - Summary, CTA for ${companyData.companyName}
      7. References section - List all news articles used

      IMPORTANT FORMATTING RULES:
      - DO NOT use markdown formatting (no **, *, #, etc.)
      - Write content in plain text with proper punctuation
      - Use natural language without any special formatting symbols
      - Content should be ready for direct display in HTML
      - Write in a professional, engaging blog style
      - Use short paragraphs (2-3 sentences each)
      - Include specific examples and actionable insights
      - Make content scannable and easy to read
      
      Return in this EXACT JSON format:
      {
        "introduction": "Full introduction text...",
        "featureImage": {
          "alt": "Descriptive alt text",
          "title": "Image title",
          "description": "What the image should show",
          "placement": "after_introduction"
        },
        "sections": [
          {
            "h2": "H2 Heading Text",
            "content": "Full content for this section...",
            "targetKeyword": "specific keyword for this section"
          }
        ],
        "inBlogImages": [
          {
            "alt": "Alt text for image 1",
            "title": "Image 1 title",
            "description": "What image 1 should show",
            "placement": "after_section_2"
          },
          {
            "alt": "Alt text for image 2",
            "title": "Image 2 title",
            "description": "What image 2 should show",
            "placement": "after_section_4"
          }
        ],
        "conclusion": "Full conclusion text...",
        "references": [
          ${newsArticles.map((article, index) => `{
            "title": "${article.title}",
            "source": "${article.source}",
            "url": "${article.url || '#'}",
            "publishedAt": "${article.publishedAt}"
          }`).join(',\n          ')}
        ]
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Content parsing error:', parseError);
    }

    // Fallback structured content
    return this.generateFallbackContent(meta, keyword, companyData, currentYear);
  }

  generateFallbackContent(meta, keyword, companyData, year) {
    return {
      introduction: `Welcome to our comprehensive guide on ${keyword}. As the solar industry continues to evolve in ${year}, understanding ${keyword} has become crucial for professionals and businesses alike. In this guide, ${companyData.companyName} shares expert insights and proven strategies to help you succeed.`,
      featureImage: {
        alt: `${keyword} comprehensive guide`,
        title: `Professional ${keyword} Guide`,
        description: `Hero image showcasing modern solar installation with ${keyword} elements highlighted`,
        placement: "after_introduction"
      },
      sections: [
        {
          h2: `What is ${keyword}?`,
          content: `Understanding ${keyword} is fundamental to success in the solar industry. This comprehensive overview covers the essential concepts, terminology, and applications that every professional should know. We'll explore how ${keyword} impacts project efficiency, cost-effectiveness, and overall success rates in solar installations.`,
          targetKeyword: keyword
        },
        {
          h2: `Benefits of Professional ${keyword} Services`,
          content: `Implementing proper ${keyword} strategies offers numerous advantages for solar projects. From improved efficiency and reduced costs to enhanced safety and compliance, the benefits are substantial. ${companyData.companyName} has helped countless professionals achieve these benefits through our specialized services and expertise.`,
          targetKeyword: `${keyword} benefits`
        },
        {
          h2: `Step-by-Step ${keyword} Implementation Guide`,
          content: `Success with ${keyword} requires a systematic approach. This detailed guide walks you through each phase of implementation, from initial planning to final execution. Follow these proven steps to ensure optimal results and avoid common pitfalls that can derail projects.`,
          targetKeyword: `${keyword} guide`
        },
        {
          h2: `Common ${keyword} Challenges and Solutions`,
          content: `Every solar project faces unique challenges related to ${keyword}. Understanding these potential issues and their solutions prepares you for success. Learn from real-world examples and expert insights to navigate obstacles effectively and maintain project momentum.`,
          targetKeyword: `${keyword} challenges`
        },
        {
          h2: `${keyword} Best Practices for ${year}`,
          content: `Stay ahead of the curve with the latest ${keyword} best practices for ${year}. These industry-leading strategies reflect recent technological advances, regulatory changes, and market trends. Implementing these practices ensures your projects meet current standards and exceed client expectations.`,
          targetKeyword: `${keyword} best practices`
        },
        {
          h2: `Future of ${keyword} in Solar Industry`,
          content: `The solar industry continues to evolve rapidly, and ${keyword} plays a crucial role in this transformation. Explore emerging trends, upcoming technologies, and future opportunities that will shape how we approach ${keyword} in the coming years.`,
          targetKeyword: `future of ${keyword}`
        }
      ],
      inBlogImages: [
        {
          alt: `${keyword} implementation process diagram`,
          title: "Implementation Process",
          description: `Detailed flowchart showing the step-by-step ${keyword} implementation process`,
          placement: "after_section_2"
        },
        {
          alt: `${keyword} best practices infographic`,
          title: "Best Practices Overview",
          description: `Visual guide highlighting key ${keyword} best practices and tips`,
          placement: "after_section_4"
        }
      ],
      conclusion: `Mastering ${keyword} is essential for success in today's competitive solar industry. By implementing the strategies and best practices outlined in this guide, you'll be well-equipped to deliver exceptional results for your clients. ${companyData.companyName} is here to support your journey with professional services and expert guidance. Contact us today to learn how we can help you excel in ${keyword} and take your solar projects to the next level.`
    };
  }

  async generateInboundOutboundLinks(keyword, blocks, companyData) {
    const { model } = require('../config/gemini.config');

    // Extract all H2 topics
    const h2Topics = blocks
      .filter(b => b.type === 'section')
      .map(b => b.h2);

    const prompt = `
      Generate internal and external link suggestions for a blog about "${keyword}".
      Company: ${companyData.companyName}
      H2 Topics: ${h2Topics.join(', ')}

      Provide:
      1. 3-4 Internal links (to other potential ${companyData.companyName} pages)
      2. 4-5 External links (to authoritative non-competitor sites)

      Return in JSON format:
      {
        "internalLinks": [
          {
            "anchorText": "text to link",
            "suggestedUrl": "/suggested-page-url",
            "context": "where in the content this fits",
            "relevance": "why this link adds value"
          }
        ],
        "externalLinks": [
          {
            "anchorText": "text to link",
            "domain": "authoritative domain",
            "context": "where in the content this fits",
            "relevance": "why this link adds value"
          }
        ]
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Links parsing error:', parseError);
    }

    // Fallback links
    return {
      internalLinks: [
        {
          anchorText: "solar installation services",
          suggestedUrl: "/services/solar-installation",
          context: "In introduction or services section",
          relevance: "Links to main service page"
        },
        {
          anchorText: "get a quote",
          suggestedUrl: "/quote",
          context: "In conclusion CTA",
          relevance: "Conversion opportunity"
        }
      ],
      externalLinks: [
        {
          anchorText: "Department of Energy",
          domain: "energy.gov",
          context: "When mentioning incentives",
          relevance: "Authority on solar incentives"
        },
        {
          anchorText: "solar industry statistics",
          domain: "seia.org",
          context: "When citing industry data",
          relevance: "Industry authority"
        }
      ]
    };
  }

  convertToWordPressHTML(draft) {
    let html = '';

    draft.blogBlocks.forEach((block, index) => {
      switch (block.type) {
        case 'introduction':
          html += `<p>${block.content}</p>\n\n`;
          break;

        case 'image':
          if (block.imageType === 'feature') {
            html += `<!-- wp:image {"align":"wide"} -->\n`;
            html += `<figure class="wp-block-image alignwide"><img src="[PLACEHOLDER_${block.imageType}_IMAGE]" alt="${block.alt}" title="${block.title}"/><figcaption>${block.title}</figcaption></figure>\n`;
            html += `<!-- /wp:image -->\n\n`;
          } else {
            html += `<!-- wp:image -->\n`;
            html += `<figure class="wp-block-image"><img src="[PLACEHOLDER_${block.imageType}_IMAGE_${index}]" alt="${block.alt}" title="${block.title}"/><figcaption>${block.title}</figcaption></figure>\n`;
            html += `<!-- /wp:image -->\n\n`;
          }
          break;

        case 'section':
          html += `<!-- wp:heading -->\n`;
          html += `<h2>${block.h2}</h2>\n`;
          html += `<!-- /wp:heading -->\n\n`;
          html += `<!-- wp:paragraph -->\n`;
          html += `<p>${block.content}</p>\n`;
          html += `<!-- /wp:paragraph -->\n\n`;
          break;

        case 'conclusion':
          html += `<!-- wp:paragraph -->\n`;
          html += `<p><strong>${block.content}</strong></p>\n`;
          html += `<!-- /wp:paragraph -->\n\n`;
          break;

        case 'references':
          html += block.content + '\n\n';
          break;
      }
    });

    // Add schema markup for SEO
    html += this.generateSchemaMarkup(draft);

    return html;
  }

  generateSchemaMarkup(draft) {
    const schema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": draft.finalMeta.h1Title,
      "description": draft.finalMeta.metaDescription,
      "author": {
        "@type": "Organization",
        "name": draft.companyData.companyName
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
      "publisher": {
        "@type": "Organization",
        "name": draft.companyData.companyName
      }
    };

    return `\n<!-- Schema Markup -->\n<script type="application/ld+json">\n${JSON.stringify(schema, null, 2)}\n</script>`;
  }
}

module.exports = new ContentService();
