const { GoogleGenerativeAI } = require('@google/generative-ai');

class GoogleVisionService {
  constructor() {
    // Use GOOGLE_API_KEY for Google services, GEMINI_API_KEY for Gemini
    const apiKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.visionModel = this.genAI.getGenerativeModel({
      model: 'gemini-1.5-pro-vision',
      generationConfig: {
        temperature: 0.8,
        topK: 32,
        topP: 0.95,
        maxOutputTokens: 4096,
      }
    });
  }

  async generateImageDescription(keyword, h1Title, companyData, imageType = 'feature') {
    try {
      const prompt = `
        Create a detailed, professional image description for a ${imageType} image for a blog post.
        
        Blog Context:
        - H1 Title: ${h1Title}
        - Primary Keyword: ${keyword}
        - Company: ${companyData.companyName}
        - Industry: Solar Energy
        - Image Type: ${imageType}
        
        Requirements:
        1. The image should visually support the H1 title and keyword
        2. Professional, high-quality appearance
        3. Relevant to solar industry and ${companyData.companyName}
        4. Modern, clean aesthetic
        5. Include specific visual elements that relate to the topic
        
        Return JSON format:
        {
          "description": "Detailed description of what the image should show",
          "alt": "SEO-optimized alt text",
          "title": "Image title",
          "style": "Photography style (e.g., professional, modern, clean)",
          "elements": ["element1", "element2", "element3"],
          "prompt": "Enhanced prompt for image generation"
        }
      `;

      const result = await this.visionModel.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json
\n?/g, '').replace(/
```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Vision service parsing error:', parseError);
      }

      // Fallback
      return {
        description: `Professional ${imageType} image showing ${keyword} related to ${companyData.companyName} solar services`,
        alt: `${keyword} - ${companyData.companyName}`,
        title: `Professional ${keyword} Guide`,
        style: "professional, modern, clean",
        elements: [keyword, "solar panels", "professional setting"],
        prompt: `Professional ${imageType} image showing ${keyword}, modern solar installation, clean aesthetic, high quality photography`
      };

    } catch (error) {
      console.error('Error generating image description:', error);
      throw error;
    }
  }

  async enhanceImagePrompt(basicDescription, keyword, context) {
    try {
      const prompt = `
        Enhance this image description for professional image generation:
        
        Basic Description: ${basicDescription}
        Keyword Context: ${keyword}
        Additional Context: ${context}
        
        Create an enhanced, detailed prompt that will generate a high-quality, professional image.
        Focus on:
        - Professional photography style
        - High resolution and quality
        - Relevant visual elements
        - Modern, clean aesthetic
        - Industry-appropriate imagery
        
        Return only the enhanced prompt text, no JSON.
      `;

      const result = await this.visionModel.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();

    } catch (error) {
      console.error('Error enhancing image prompt:', error);
      return basicDescription; // Fallback to basic description
    }
  }

  async generateMultipleImageDescriptions(keyword, h1Title, companyData, imageTypes = ['feature', 'section', 'section']) {
    try {
      const descriptions = [];
      
      for (let i = 0; i < imageTypes.length; i++) {
        const imageType = imageTypes[i];
        const description = await this.generateImageDescription(keyword, h1Title, companyData, imageType);
        descriptions.push({
          ...description,
          type: imageType,
          index: i
        });
      }
      
      return descriptions;
    } catch (error) {
      console.error('Error generating multiple image descriptions:', error);
      throw error;
    }
  }
}

module.exports = new GoogleVisionService();

    } catch (parseError) {
      console.error('Content parsing error:', parseError);
    }

    // Fallback structured content
    return this.generateFallbackContent(meta, keyword, companyData, currentYear);
  }

  generateFallbackContent(meta, keyword, companyData, year) {
    return {
      introduction: `Welcome to our comprehensive guide on ${keyword}. As the solar industry continues to evolve in ${year}, understanding ${keyword} has become crucial for professionals and businesses alike. In this guide, ${companyData.companyName} shares expert insights and proven strategies to help you succeed.`,
      featureImage: {
        alt: `${keyword} comprehensive guide`,
        title: `Professional ${keyword} Guide`,
        description: `Hero image showcasing modern solar installation with ${keyword} elements highlighted`,
        placement: "after_introduction"
      },
      sections: [
        {
          h2: `What is ${keyword}?`,
          content: `Understanding ${keyword} is fundamental to success in the solar industry. This comprehensive overview covers the essential concepts, terminology, and applications that every professional should know. We'll explore how ${keyword} impacts project efficiency, cost-effectiveness, and overall success rates in solar installations.`,
          targetKeyword: keyword
        },
        {
          h2: `Benefits of Professional ${keyword} Services`,
          content: `Implementing proper ${keyword} strategies offers numerous advantages for solar projects. From improved efficiency and reduced costs to enhanced safety and compliance, the benefits are substantial. ${companyData.companyName} has helped countless professionals achieve these benefits through our specialized services and expertise.`,
          targetKeyword: `${keyword} benefits`
        },
        {
          h2: `Step-by-Step ${keyword} Implementation Guide`,
          content: `Success with ${keyword} requires a systematic approach. This detailed guide walks you through each phase of implementation, from initial planning to final execution. Follow these proven steps to ensure optimal results and avoid common pitfalls that can derail projects.`,
          targetKeyword: `${keyword} guide`
        },
        {
          h2: `Common ${keyword} Challenges and Solutions`,
          content: `Every solar project faces unique challenges related to ${keyword}. Understanding these potential issues and their solutions prepares you for success. Learn from real-world examples and expert insights to navigate obstacles effectively and maintain project momentum.`,
          targetKeyword: `${keyword} challenges`
        },
        {
          h2: `${keyword} Best Practices for ${year}`,
          content: `Stay ahead of the curve with the latest ${keyword} best practices for ${year}. These industry-leading strategies reflect recent technological advances, regulatory changes, and market trends. Implementing these practices ensures your projects meet current standards and exceed client expectations.`,
          targetKeyword: `${keyword} best practices`
        },
        {
          h2: `Future of ${keyword} in Solar Industry`,
          content: `The solar industry continues to evolve rapidly, and ${keyword} plays a crucial role in this transformation. Explore emerging trends, upcoming technologies, and future opportunities that will shape how we approach ${keyword} in the coming years.`,
          targetKeyword: `future of ${keyword}`
        }
      ],
      inBlogImages: [
        {
          alt: `${keyword} implementation process diagram`,
          title: "Implementation Process",
          description: `Detailed flowchart showing the step-by-step ${keyword} implementation process`,
          placement: "after_section_2"
        },
        {
          alt: `${keyword} best practices infographic`,
          title: "Best Practices Overview",
          description: `Visual guide highlighting key ${keyword} best practices and tips`,
          placement: "after_section_4"
        }
      ],
      conclusion: `Mastering ${keyword} is essential for success in today's competitive solar industry. By implementing the strategies and best practices outlined in this guide, you'll be well-equipped to deliver exceptional results for your clients. ${companyData.companyName} is here to support your journey with professional services and expert guidance. Contact us today to learn how we can help you excel in ${keyword} and take your solar projects to the next level.`
    };
  }

  async generateInboundOutboundLinks(keyword, blocks, companyData) {
    const { model } = require('../config/gemini.config');

    // Extract all H2 topics
    const h2Topics = blocks
      .filter(b => b.type === 'section')
      .map(b => b.h2);

    const prompt = `
      Generate internal and external link suggestions for a blog about "${keyword}".
      Company: ${companyData.companyName}
      H2 Topics: ${h2Topics.join(', ')}

      Provide:
      1. 3-4 Internal links (to other potential ${companyData.companyName} pages)
      2. 4-5 External links (to authoritative non-competitor sites)

      Return in JSON format:
      {
        "internalLinks": [
          {
            "anchorText": "text to link",
            "suggestedUrl": "/suggested-page-url",
            "context": "where in the content this fits",
            "relevance": "why this link adds value"
          }
        ],
        "externalLinks": [
          {
            "anchorText": "text to link",
            "domain": "authoritative domain",
            "context": "where in the content this fits",
            "relevance": "why this link adds value"
          }
        ]
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Links parsing error:', parseError);
    }

    // Fallback links
    return {
      internalLinks: [
        {
          anchorText: "solar installation services",
          suggestedUrl: "/services/solar-installation",
          context: "In introduction or services section",
          relevance: "Links to main service page"
        },
        {
          anchorText: "get a quote",
          suggestedUrl: "/quote",
          context: "In conclusion CTA",
          relevance: "Conversion opportunity"
        }
      ],
      externalLinks: [
        {
          anchorText: "Department of Energy",
          domain: "energy.gov",
          context: "When mentioning incentives",
          relevance: "Authority on solar incentives"
        },
        {
          anchorText: "solar industry statistics",
          domain: "seia.org",
          context: "When citing industry data",
          relevance: "Industry authority"
        }
      ]
    };
  }

  convertToWordPressHTML(draft) {
    let html = '';

    draft.blogBlocks.forEach((block, index) => {
      switch (block.type) {
        case 'introduction':
          html += `<p>${block.content}</p>\n\n`;
          break;

        case 'image':
          if (block.imageType === 'feature') {
            html += `<!-- wp:image {"align":"wide"} -->\n`;
            html += `<figure class="wp-block-image alignwide"><img src="[PLACEHOLDER_${block.imageType}_IMAGE]" alt="${block.alt}" title="${block.title}"/><figcaption>${block.title}</figcaption></figure>\n`;
            html += `<!-- /wp:image -->\n\n`;
          } else {
            html += `<!-- wp:image -->\n`;
            html += `<figure class="wp-block-image"><img src="[PLACEHOLDER_${block.imageType}_IMAGE_${index}]" alt="${block.alt}" title="${block.title}"/><figcaption>${block.title}</figcaption></figure>\n`;
            html += `<!-- /wp:image -->\n\n`;
          }
          break;

        case 'section':
          html += `<!-- wp:heading -->\n`;
          html += `<h2>${block.h2}</h2>\n`;
          html += `<!-- /wp:heading -->\n\n`;
          html += `<!-- wp:paragraph -->\n`;
          html += `<p>${block.content}</p>\n`;
          html += `<!-- /wp:paragraph -->\n\n`;
          break;

        case 'conclusion':
          html += `<!-- wp:paragraph -->\n`;
          html += `<p><strong>${block.content}</strong></p>\n`;
          html += `<!-- /wp:paragraph -->\n\n`;
          break;

        case 'references':
          html += block.content + '\n\n';
          break;
      }
    });

    // Add schema markup for SEO
    html += this.generateSchemaMarkup(draft);

    return html;
  }

  generateSchemaMarkup(draft) {
    const schema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": draft.finalMeta.h1Title,
      "description": draft.finalMeta.metaDescription,
      "author": {
        "@type": "Organization",
        "name": draft.companyData.companyName
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
      "publisher": {
        "@type": "Organization",
        "name": draft.companyData.companyName
      }
    };

    return `\n<!-- Schema Markup -->\n<script type="application/ld+json">\n${JSON.stringify(schema, null, 2)}\n</script>`;
  }
}

module.exports = new ContentService();
