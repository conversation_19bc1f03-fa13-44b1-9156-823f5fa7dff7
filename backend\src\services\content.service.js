class ContentService {
  async generateWordPressFormattedContent(meta, keyword, keywordCluster, companyData, competitorAnalysis, trends, newsArticles = []) {
    // Use GEMINI_API_KEY for content generation
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    console.log('🎯 Content generation using:', {
      h1Title: meta.h1Title,
      metaDescription: meta.metaDescription,
      keyword: keyword,
      companyName: companyData.companyName
    });
    
    // Prepare news articles context
    const newsContext = newsArticles.length > 0
      ? `News Articles for Reference:\n${newsArticles.map((article, index) =>
          `${index + 1}. "${article.title}" - ${article.source} (${article.publishedAt})`
        ).join('\n')}\n`
      : '';

    // Add randomization to ensure unique content each time
    const timestamp = Date.now();
    const randomSeed = Math.floor(Math.random() * 1000);

    const prompt = `You are an expert content writer for ${companyData.companyName}. Create a comprehensive, engaging blog post that EXACTLY matches the selected H1 and meta description.

UNIQUE CONTENT REQUIREMENT: Generate completely fresh, original content (Seed: ${randomSeed}, Time: ${timestamp}). Do NOT repeat previous content.

CRITICAL REQUIREMENTS:
- The content MUST be directly related to and support this EXACT H1 title: "${meta.h1Title || meta.h1 || `${keyword} - ${companyData.companyName}`}"
- The content MUST fulfill the promise made in this meta description: "${meta.metaDescription || `Learn about ${keyword} with ${companyData.companyName}`}"
- Write in ${companyData.brandVoice || 'professional, authoritative'} tone
- Target audience: ${companyData.targetAudience || 'solar industry professionals and business owners'}
- Company expertise: ${companyData.serviceOverview || 'solar energy solutions'}
- GENERATE UNIQUE CONTENT: Use different examples, statistics, and approaches each time

CONTENT SPECIFICATIONS:
Primary Keyword: ${keyword}
Word Count: 2000-2500 words
Company: ${companyData.companyName}
Year: ${currentYear}
${newsContext}

COMPETITIVE INTELLIGENCE:
Competitor H2 topics to outrank: ${competitorAnalysis?.commonH2Topics?.join(', ') || 'No competitor data'}
Current industry trends: ${trends?.currentTrends?.join(', ') || 'No trends data'}
LSI Keywords to include naturally: ${keywordCluster?.lsiKeywords?.join(', ') || keywordCluster?.join(', ') || 'No additional keywords'}

BLOG WRITING REQUIREMENTS:
Write like a professional blog writer, NOT like an AI assistant answering a prompt.

BLOG STRUCTURE (EXACTLY):
1. INTRODUCTION (150-200 words):
   - Start with a compelling hook (question, statistic, or bold statement)
   - Establish the problem or opportunity
   - Preview what readers will learn
   - Include primary keyword naturally

2. FEATURE IMAGE: Professional hero image description

3. MAIN CONTENT SECTIONS (5-6 H2s):
   Each section should be 300-400 words with:
   - Compelling H2 that promises value
   - Opening paragraph that hooks the reader
   - 2-3 supporting paragraphs with specific examples
   - Include relevant statistics and data
   - Add 1-2 internal links
   - Reference news articles naturally when relevant
   - End each section with a transition to the next

4. VISUAL ELEMENTS:
   - Image 1: After 2nd H2 section (relevant to that topic)
   - Image 2: After 4th H2 section (supporting the content)

5. CONCLUSION (150-200 words):
   - Summarize key takeaways (3-4 bullet points)
   - Strong call-to-action for ${companyData.companyName}
   - Next steps for the reader

6. REFERENCES: All news articles used in research

CRITICAL WRITING STYLE REQUIREMENTS:
- Write like a PROFESSIONAL BLOG WRITER, not an AI assistant
- NO generic AI phrases like "In this comprehensive guide" or "Let's explore"
- Use conversational, engaging tone that speaks directly to readers
- Start paragraphs with strong, specific statements
- Include real-world examples and scenarios
- Use industry-specific terminology naturally
- Write with authority and expertise
- NO markdown formatting (**, *, #, etc.)
- Content should feel human-written and engaging
- Each paragraph should add unique value
- Use active voice and strong verbs
- Include specific numbers, percentages, and data points

Return in this EXACT JSON format:
{
  "introduction": "Full introduction text...",
  "featureImage": {
    "alt": "Descriptive alt text",
    "title": "Image title",
    "description": "What the image should show",
    "placement": "after_introduction"
  },
  "sections": [
    {
      "h2": "Section heading",
      "content": "Full section content...",
      "internalLinks": ["link1", "link2"]
    }
  ],
  "images": [
    {
      "alt": "Image alt text",
      "title": "Image title", 
      "description": "What the image should show",
      "placement": "after_section_2"
    }
  ],
  "conclusion": "Full conclusion text...",
  "references": []
}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedContent = JSON.parse(jsonMatch[0]);
        console.log('✅ Content generated successfully');
        return parsedContent;
      }
    } catch (parseError) {
      console.error('Content parsing error:', parseError);
    }

    // Fallback structured content
    console.log('⚠️ Using fallback content');
    return this.generateFallbackContent(meta, keyword, companyData, currentYear);
  }

  generateFallbackContent(meta, keyword, companyData, year) {
    return {
      introduction: `Welcome to our comprehensive guide on ${keyword}. As the solar industry continues to evolve in ${year}, understanding ${keyword} has become crucial for professionals and businesses alike. In this guide, ${companyData.companyName} shares expert insights and proven strategies to help you succeed.`,
      featureImage: {
        alt: `${keyword} comprehensive guide`,
        title: `Professional ${keyword} Guide`,
        description: `Hero image showcasing modern solar installation with ${keyword} elements highlighted`,
        placement: "after_introduction"
      },
      sections: [
        {
          h2: `Understanding ${keyword} in ${year}`,
          content: `The landscape of ${keyword} has transformed significantly in recent years. ${companyData.companyName} has been at the forefront of these changes, helping clients navigate the evolving requirements and opportunities. Current market conditions show increased demand for efficient solutions, making this an optimal time for businesses to invest in ${keyword} strategies.`,
          internalLinks: []
        },
        {
          h2: `Key Benefits and Applications`,
          content: `Implementing ${keyword} solutions offers numerous advantages for modern businesses. From cost savings to improved efficiency, the benefits are substantial. ${companyData.companyName} has documented significant improvements in client operations through strategic ${keyword} implementation.`,
          internalLinks: []
        }
      ],
      images: [
        {
          alt: `${keyword} implementation example`,
          title: `${keyword} in Action`,
          description: `Professional image showing ${keyword} implementation in a real-world setting`,
          placement: "after_section_1"
        }
      ],
      conclusion: `${keyword} represents a significant opportunity for businesses looking to improve their operations in ${year}. ${companyData.companyName} stands ready to help you implement these solutions effectively. Contact our team today to learn how we can support your ${keyword} initiatives.`,
      references: []
    };
  }
}

module.exports = new ContentService();
