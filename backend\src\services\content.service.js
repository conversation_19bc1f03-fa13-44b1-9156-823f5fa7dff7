class ContentService {
  async generateWordPressFormattedContent(meta, keyword, keywordCluster, companyData, competitorAnalysis, trends, newsArticles = []) {
    // Use GEMINI_API_KEY for content generation
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    console.log('🎯 Content generation using ACTUAL CONTEXT:', {
      h1Title: meta.h1Title,
      metaDescription: meta.metaDescription,
      keyword: keyword,
      companyName: companyData.companyName,
      keywordCluster: keywordCluster?.length || 0,
      competitorAnalysis: competitorAnalysis?.topics?.length || 0,
      newsArticles: newsArticles?.length || 0
    });

    // CRITICAL: Validate that we have the required context
    if (!meta.h1Title || !meta.metaDescription) {
      console.error('❌ MISSING CONTEXT: H1 or Meta Description not provided!');
      throw new Error('H1 title and meta description are required for contextual content generation');
    }
    
    // Prepare news articles context
    const newsContext = newsArticles.length > 0
      ? `News Articles for Reference:\n${newsArticles.map((article, index) =>
          `${index + 1}. "${article.title}" - ${article.source} (${article.publishedAt})`
        ).join('\n')}\n`
      : '';

    // Add randomization to ensure unique content each time
    const timestamp = Date.now();
    const randomSeed = Math.floor(Math.random() * 1000);

    // Prepare competitor insights
    const competitorInsights = competitorAnalysis?.topics ?
      `Competitor Analysis Insights:\n${competitorAnalysis.topics.slice(0, 3).map((topic, i) =>
        `${i + 1}. ${topic.title} - ${topic.description}`
      ).join('\n')}\n` : '';

    // Prepare keyword cluster context
    const keywordContext = keywordCluster?.length > 0 ?
      `Related Keywords to Include: ${keywordCluster.slice(0, 5).join(', ')}\n` : '';

    const prompt = `You are an expert content writer for ${companyData.companyName}. Create a comprehensive, engaging blog post that EXACTLY matches the selected H1 and meta description.

CRITICAL CONTEXT REQUIREMENTS:
- This content MUST directly support this EXACT H1 title: "${meta.h1Title}"
- This content MUST fulfill this EXACT meta description promise: "${meta.metaDescription}"
- The H1 and meta were specifically chosen by the user - the content MUST match them perfectly
- DO NOT write generic content - write specifically for this H1 and meta combination

UNIQUE CONTENT REQUIREMENT (Seed: ${randomSeed}, Time: ${timestamp}):
- Generate completely fresh, original content that has never been written before
- Use different examples, case studies, statistics, and approaches
- Avoid generic phrases like "comprehensive guide" or "everything you need to know"
- Write like a professional blog writer, not an AI assistant

CONTEXT TO INTEGRATE:
${newsContext}${competitorInsights}${keywordContext}
Company Details:
- Name: ${companyData.companyName}
- Services: ${companyData.serviceOverview || 'solar energy solutions'}
- Brand Voice: ${companyData.brandVoice || 'professional, authoritative'}
- Target Audience: ${companyData.targetAudience || 'solar industry professionals and business owners'}

CONTENT SPECIFICATIONS:
Primary Keyword: ${keyword}
Word Count: 2000-2500 words
Company: ${companyData.companyName}
Year: ${currentYear}
${newsContext}

COMPETITIVE INTELLIGENCE:
Competitor H2 topics to outrank: ${competitorAnalysis?.commonH2Topics?.join(', ') || 'No competitor data'}
Current industry trends: ${trends?.currentTrends?.join(', ') || 'No trends data'}
LSI Keywords to include naturally: ${keywordCluster?.lsiKeywords?.join(', ') || keywordCluster?.join(', ') || 'No additional keywords'}

BLOG WRITING REQUIREMENTS:
Write like a professional blog writer, NOT like an AI assistant answering a prompt.

BLOG STRUCTURE (EXACTLY):
1. INTRODUCTION (150-200 words):
   - Start with a compelling hook (question, statistic, or bold statement)
   - Establish the problem or opportunity
   - Preview what readers will learn
   - Include primary keyword naturally

2. FEATURE IMAGE: Professional hero image description

3. MAIN CONTENT SECTIONS (5-6 H2s):
   Each section should be 300-400 words with:
   - Compelling H2 that promises value and supports the main H1
   - Opening paragraph that hooks the reader
   - 2-3 supporting paragraphs with specific examples
   - Include relevant statistics and data with sources
   - Add 1-2 inbound links to authoritative sources (government sites, industry reports, company blogs)
   - Reference the provided news articles naturally when relevant
   - Include internal links to ${companyData.companyName} services where appropriate
   - End each section with a transition to the next

4. VISUAL ELEMENTS:
   - Image 1: After 2nd H2 section (relevant to that topic)
   - Image 2: After 4th H2 section (supporting the content)

5. CONCLUSION (150-200 words):
   - Summarize key takeaways (3-4 bullet points)
   - Strong call-to-action for ${companyData.companyName}
   - Next steps for the reader

6. REFERENCES SECTION:
   - Include ALL provided news articles with proper attribution
   - Add relevant industry sources and government data links
   - Include company blog links where appropriate
   - Format as: "Title" - Source (Date) [URL if available]
   - Minimum 3-5 authoritative references

CRITICAL WRITING STYLE REQUIREMENTS:
- Write like a PROFESSIONAL BLOG WRITER, not an AI assistant
- NO generic AI phrases like "In this comprehensive guide" or "Let's explore"
- Use conversational, engaging tone that speaks directly to readers
- Start paragraphs with strong, specific statements
- Include real-world examples and scenarios
- Use industry-specific terminology naturally
- Write with authority and expertise
- NO markdown formatting (**, *, #, etc.)
- Content should feel human-written and engaging
- Each paragraph should add unique value
- Use active voice and strong verbs
- Include specific numbers, percentages, and data points

CRITICAL: Return ONLY valid JSON in this EXACT format. No markdown, no extra text:

{
  "introduction": "Full introduction text that matches the H1 and meta description (200-250 words)",
  "featureImage": {
    "alt": "${keyword} professional guide",
    "title": "${meta.h1Title} - Hero Image",
    "description": "Professional hero image for ${keyword} guide",
    "placement": "after_introduction"
  },
  "sections": [
    {
      "h2": "Understanding ${keyword} Basics",
      "content": "Full section content with inbound links and references (400-500 words)",
      "targetKeyword": "${keyword}",
      "internalLinks": ["${companyData.companyName} services", "Professional installation"]
    },
    {
      "h2": "Cost Analysis and ROI for ${keyword}",
      "content": "Full section content (400-500 words)",
      "targetKeyword": "solar installation cost",
      "internalLinks": ["Cost calculator", "Financing options"]
    },
    {
      "h2": "Installation Process and Timeline",
      "content": "Full section content (400-500 words)",
      "targetKeyword": "installation process",
      "internalLinks": ["Installation team", "Project timeline"]
    },
    {
      "h2": "Maintenance and Performance Optimization",
      "content": "Full section content (400-500 words)",
      "targetKeyword": "solar maintenance",
      "internalLinks": ["Maintenance services", "Performance monitoring"]
    },
    {
      "h2": "Choosing the Right Solar Partner",
      "content": "Full section content (400-500 words)",
      "targetKeyword": "solar installer",
      "internalLinks": ["${companyData.companyName} expertise", "Customer testimonials"]
    }
  ],
  "inBlogImages": [
    {
      "alt": "${keyword} professional installation",
      "title": "Professional ${keyword} Installation",
      "description": "Professional installation process for ${keyword}",
      "placement": "after_section_2"
    },
    {
      "alt": "${keyword} maintenance guide",
      "title": "${keyword} Maintenance Guide",
      "description": "Maintenance and optimization for ${keyword}",
      "placement": "after_section_4"
    }
  ],
  "conclusion": "Compelling conclusion with strong CTA for ${companyData.companyName} (200-250 words)",
  "references": [
    "Federal Solar Tax Credit Extended Through 2025 - Solar Power World",
    "New Solar Panel Efficiency Records Set in 2025 - PV Magazine",
    "Impact of Solar Panel Installations on Home Values - NREL",
    "Residential Solar PV System Costs - EnergySage",
    "${companyData.companyName} Professional Installation Services"
  ]
}`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      console.log('🔍 Raw AI response length:', text.length);
      console.log('🔍 Raw AI response preview:', text.substring(0, 200) + '...');

      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      console.log('🧹 Cleaned text length:', cleanedText.length);

      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        console.log('✅ JSON match found, parsing...');
        const parsedContent = JSON.parse(jsonMatch[0]);
        console.log('✅ Content generated successfully with sections:', parsedContent.sections?.length || 0);
        console.log('✅ References count:', parsedContent.references?.length || 0);
        return parsedContent;
      } else {
        console.error('❌ No JSON match found in response');
      }
    } catch (parseError) {
      console.error('❌ Content parsing error:', parseError.message);
      console.error('❌ Failed text preview:', text.substring(0, 500));
    }

    // Fallback structured content
    console.log('⚠️ Using fallback content');
    return this.generateFallbackContent(meta, keyword, companyData, currentYear);
  }

  generateFallbackContent(meta, keyword, companyData, year) {
    return {
      introduction: `Welcome to our comprehensive guide on ${keyword}. As the solar industry continues to evolve in ${year}, understanding ${keyword} has become crucial for professionals and businesses alike. In this guide, ${companyData.companyName} shares expert insights and proven strategies to help you succeed.`,
      featureImage: {
        alt: `${keyword} comprehensive guide`,
        title: `Professional ${keyword} Guide`,
        description: `Hero image showcasing modern solar installation with ${keyword} elements highlighted`,
        placement: "after_introduction"
      },
      sections: [
        {
          h2: `Understanding ${keyword} in ${year}`,
          content: `The landscape of ${keyword} has transformed significantly in recent years. ${companyData.companyName} has been at the forefront of these changes, helping clients navigate the evolving requirements and opportunities. Current market conditions show increased demand for efficient solutions, making this an optimal time for businesses to invest in ${keyword} strategies.`,
          internalLinks: []
        },
        {
          h2: `Key Benefits and Applications`,
          content: `Implementing ${keyword} solutions offers numerous advantages for modern businesses. From cost savings to improved efficiency, the benefits are substantial. ${companyData.companyName} has documented significant improvements in client operations through strategic ${keyword} implementation.`,
          internalLinks: []
        }
      ],
      images: [
        {
          alt: `${keyword} implementation example`,
          title: `${keyword} in Action`,
          description: `Professional image showing ${keyword} implementation in a real-world setting`,
          placement: "after_section_1"
        }
      ],
      conclusion: `${keyword} represents a significant opportunity for businesses looking to improve their operations in ${year}. ${companyData.companyName} stands ready to help you implement these solutions effectively. Contact our team today to learn how we can support your ${keyword} initiatives.`,
      references: []
    };
  }
}

module.exports = new ContentService();
