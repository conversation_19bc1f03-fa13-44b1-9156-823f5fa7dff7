"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[draftId]/keywords/page",{

/***/ "(app-pages-browser)/./app/blog/[draftId]/keywords/page.tsx":
/*!**********************************************!*\
  !*** ./app/blog/[draftId]/keywords/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ KeywordsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,FileText,Search,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,FileText,Search,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,FileText,Search,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,FileText,Search,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,FileText,Search,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,FileText,Search,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_stepper_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/stepper-header */ \"(app-pages-browser)/./components/stepper-header.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction KeywordsPage() {\n    var _analysis_competitors_topRankingPages, _analysis_competitors, _analysis_cluster_secondaryKeywords, _analysis_cluster, _analysis_trends, _analysis_trends1, _analysis_trends2, _analysis_trends3;\n    _s();\n    const [draft, setDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedKeyword, setSelectedKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [analysis, setAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [analyzing, setAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAnalysis, setShowAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const draftId = params.draftId;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"KeywordsPage.useEffect\": ()=>{\n            loadDraftData();\n        }\n    }[\"KeywordsPage.useEffect\"], [\n        draftId\n    ]);\n    const loadDraftData = async ()=>{\n        try {\n            // Load draft data from backend\n            const draftResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.api.getDraft(draftId);\n            if (draftResponse.success && draftResponse.data) {\n                const draftData = draftResponse.data;\n                setDraft(draftData);\n                // Set keywords from draft's keyword suggestions (2 manual + 2 automated)\n                if (draftData.keywordSuggestions && draftData.keywordSuggestions.total) {\n                    setKeywords(draftData.keywordSuggestions.total);\n                }\n                // If keyword is already selected and analysis exists, show it\n                if (draftData.selectedKeyword) {\n                    setSelectedKeyword(draftData.selectedKeyword);\n                    if (draftData.competitorAnalysis && draftData.keywordCluster && draftData.trends) {\n                        setAnalysis({\n                            competitors: draftData.competitorAnalysis,\n                            cluster: draftData.keywordCluster,\n                            trends: draftData.trends\n                        });\n                        setShowAnalysis(true);\n                    }\n                }\n            }\n        } catch (error) {\n            toast({\n                title: \"Error loading draft\",\n                description: \"Failed to load draft data. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAnalyzeKeyword = async ()=>{\n        if (!selectedKeyword) return;\n        setAnalyzing(true);\n        try {\n            // Call backend API to analyze keyword\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.api.selectKeywordAnalyze(draftId, selectedKeyword);\n            if (response.success) {\n                setAnalysis({\n                    competitors: response.competitorAnalysis,\n                    cluster: response.keywordCluster,\n                    trends: response.trends\n                });\n                setShowAnalysis(true);\n                toast({\n                    title: \"Analysis complete\",\n                    description: \"Keyword analysis has been generated successfully.\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Analysis failed\",\n                description: \"Failed to analyze keyword. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setAnalyzing(false);\n        }\n    };\n    const handleContinue = async ()=>{\n        console.log('Continue button clicked!');\n        console.log('selectedKeyword:', selectedKeyword);\n        console.log('analysis:', analysis);\n        if (selectedKeyword && analysis) {\n            try {\n                console.log('Saving draft with data:', {\n                    selectedKeyword,\n                    competitorAnalysis: analysis.competitors,\n                    keywordCluster: analysis.cluster,\n                    trends: analysis.trends,\n                    status: 'meta_generation'\n                });\n                // Save the current state before navigating\n                await _lib_api__WEBPACK_IMPORTED_MODULE_12__.api.saveDraft(draftId, {\n                    selectedKeyword,\n                    competitorAnalysis: analysis.competitors,\n                    keywordCluster: analysis.cluster,\n                    trends: analysis.trends,\n                    status: 'meta_generation'\n                });\n                console.log('Draft saved successfully, navigating to meta page');\n                router.push(\"/blog/\".concat(draftId, \"/meta\"));\n            } catch (error) {\n                console.error('Error in handleContinue:', error);\n                toast({\n                    title: \"Error saving progress\",\n                    description: \"Failed to save keyword selection. Please try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } else {\n            console.log('Cannot continue - missing selectedKeyword or analysis');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                    currentStep: 1,\n                    draftId: draftId\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                className: \"h-8 w-64\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    1,\n                                    2,\n                                    3,\n                                    4\n                                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                        className: \"h-48\"\n                                    }, i, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                currentStep: 1,\n                draftId: draftId\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"Select Focus Keyword\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Choose the primary keyword for your blog post\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroup, {\n                            value: selectedKeyword,\n                            onValueChange: setSelectedKeyword,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: keywords.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"keyword-\".concat(index),\n                                            className: \"cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"hover:shadow-md transition-all duration-200 \".concat(selectedKeyword === keyword.focusKeyword ? \"ring-2 ring-[#0066cc] border-[#0066cc]\" : \"hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: keyword.focusKeyword\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex gap-2 mt-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    children: keyword.articleFormat\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                    lineNumber: 196,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: keyword.source === \"ai\" ? \"default\" : \"secondary\",\n                                                                                    children: keyword.source === \"manual\" ? \"Manual\" : \"AI Generated\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                    lineNumber: 197,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_6__.RadioGroupItem, {\n                                                                    value: keyword.focusKeyword,\n                                                                    id: \"keyword-\".concat(index),\n                                                                    className: \"mt-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-gray-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 208,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    keyword.wordCount,\n                                                                                    \" words\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 209,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-gray-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 212,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: keyword.targetAudience\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 213,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            keyword.tone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: keyword.tone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-500 mt-0.5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: keyword.objective\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleAnalyzeKeyword,\n                                    disabled: !selectedKeyword || analyzing,\n                                    className: \"bg-[#0066cc] hover:bg-blue-700\",\n                                    children: analyzing ? \"Analyzing...\" : \"Analyze Keyword\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                analysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleContinue,\n                                    className: \"bg-[#00aa66] hover:bg-green-700\",\n                                    children: \"Continue to Meta Generation\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        showAnalysis && analysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Keyword Analysis Results\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full justify-between bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Competitor Analysis\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: ((_analysis_competitors = analysis.competitors) === null || _analysis_competitors === void 0 ? void 0 : (_analysis_competitors_topRankingPages = _analysis_competitors.topRankingPages) === null || _analysis_competitors_topRankingPages === void 0 ? void 0 : _analysis_competitors_topRankingPages.map((competitor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-b pb-3 last:border-b-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: competitor.competitor\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                        children: competitor.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-4 mt-2 text-xs text-gray-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"Words: \",\n                                                                                competitor.wordCount\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    competitor.keyPoints && competitor.keyPoints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs font-medium text-gray-700\",\n                                                                                children: \"Key Points:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 280,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                className: \"text-xs text-gray-600 list-disc list-inside\",\n                                                                                children: competitor.keyPoints.map((point, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: point\n                                                                                    }, idx, false, {\n                                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                        lineNumber: 283,\n                                                                                        columnNumber: 37\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 27\n                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"No competitor data available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full justify-between bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Keyword Cluster\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                            className: \"w-full text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"border-b\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"text-left py-2\",\n                                                                                children: \"Keyword\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"text-left py-2\",\n                                                                                children: \"Volume\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 317,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"text-left py-2\",\n                                                                                children: \"Difficulty\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                className: \"text-left py-2\",\n                                                                                children: \"Relevance\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                    children: ((_analysis_cluster = analysis.cluster) === null || _analysis_cluster === void 0 ? void 0 : (_analysis_cluster_secondaryKeywords = _analysis_cluster.secondaryKeywords) === null || _analysis_cluster_secondaryKeywords === void 0 ? void 0 : _analysis_cluster_secondaryKeywords.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            className: \"border-b\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"py-2\",\n                                                                                    children: item.keyword\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                    lineNumber: 325,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"py-2\",\n                                                                                    children: \"-\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                    lineNumber: 326,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"py-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: \"secondary\",\n                                                                                        children: \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                        lineNumber: 328,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                    className: \"py-2\",\n                                                                                    children: [\n                                                                                        Math.round(item.relevance * 100),\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                    lineNumber: 332,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 31\n                                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            colSpan: 4,\n                                                                            className: \"py-4 text-center text-sm text-gray-500\",\n                                                                            children: \"No keyword cluster data available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.Collapsible, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full justify-between bg-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Trends Analysis\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_8__.CollapsibleContent, {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    ((_analysis_trends = analysis.trends) === null || _analysis_trends === void 0 ? void 0 : _analysis_trends.currentTrends) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: \"Current Trends\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                                children: analysis.trends.currentTrends.map((trend, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                                            className: \"pt-3 pb-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                        lineNumber: 370,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: trend\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                        lineNumber: 371,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, index, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    ((_analysis_trends1 = analysis.trends) === null || _analysis_trends1 === void 0 ? void 0 : _analysis_trends1.emergingTechnologies) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: \"Emerging Technologies\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                                children: analysis.trends.emergingTechnologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                                            className: \"pt-3 pb-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_FileText_Search_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: tech\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                        lineNumber: 389,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, index, false, {\n                                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !((_analysis_trends2 = analysis.trends) === null || _analysis_trends2 === void 0 ? void 0 : _analysis_trends2.currentTrends) && !((_analysis_trends3 = analysis.trends) === null || _analysis_trends3 === void 0 ? void 0 : _analysis_trends3.emergingTechnologies) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"No trend data available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\keywords\\\\page.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(KeywordsPage, \"A+Bu8daarXfjrbWG/1n6muhtGGA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = KeywordsPage;\nvar _c;\n$RefreshReg$(_c, \"KeywordsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/blog/[draftId]/keywords/page.tsx\n"));

/***/ })

});