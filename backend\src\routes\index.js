const router = require('express').Router();

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'API routes working!' });
});

// Mount routes - comment these out for now if files don't exist
try {
  console.log('Loading blog routes...');
  router.use('/blog', require('./blog.routes'));
  console.log('✅ Blog routes loaded');

  console.log('Loading company routes...');
  router.use('/company', require('./company.routes'));
  console.log('✅ Company routes loaded');

  console.log('Loading n8n routes...');
  router.use('/n8n', require('./n8n.routes'));
  console.log('✅ N8N routes loaded');
} catch (error) {
  console.error('❌ Route loading error:', error.message);
  console.error('Stack:', error.stack);
}

module.exports = router;