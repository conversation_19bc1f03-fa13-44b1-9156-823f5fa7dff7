const router = require('express').Router();

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'API routes working!' });
});

// Mount routes - comment these out for now if files don't exist
try {
  router.use('/blog', require('./blog.routes'));
  router.use('/company', require('./company.routes'));
  router.use('/n8n', require('./n8n.routes'));
} catch (error) {
  console.error('Route loading error:', error.message);
}

module.exports = router;