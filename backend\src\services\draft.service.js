const { getDb } = require('../config/firebase.config');

// Temporary in-memory storage (fallback)
const drafts = new Map();

class DraftService {
  constructor() {
    this.db = null;
    this.collection = 'drafts';
  }

  async getDatabase() {
    if (!this.db) {
      this.db = getDb();
    }
    return this.db;
  }
  async createDraft(data) {
    try {
      const draftId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const draft = {
        ...data,
        id: draftId,
        status: data.status || 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const db = await this.getDatabase();

      if (db) {
        // Store in Firebase
        await db.collection(this.collection).doc(draftId).set(draft);
        console.log(`✅ Draft created in Firebase: ${draftId}`);
      } else {
        // Fallback to in-memory storage
        drafts.set(draftId, draft);
        console.log(`⚠️  Draft created in memory: ${draftId}`);
      }

      return draft;
    } catch (error) {
      console.error('Error creating draft:', error);
      // Fallback to in-memory storage on error
      const draftId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const draft = {
        ...data,
        id: draftId,
        status: data.status || 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      drafts.set(draftId, draft);
      console.log(`⚠️  Draft created in memory (fallback): ${draftId}`);
      return draft;
    }
  }

  async createDraftWithId(draftId, data) {
    try {
      const draft = {
        ...data,
        id: draftId,
        status: data.status || 'draft',
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const db = await this.getDatabase();

      if (db) {
        // Store in Firebase
        await db.collection(this.collection).doc(draftId).set(draft);
        console.log(`✅ Draft created in Firebase with custom ID: ${draftId}`);
      } else {
        // Fallback to in-memory storage
        drafts.set(draftId, draft);
        console.log(`⚠️  Draft created in memory with custom ID: ${draftId}`);
      }

      return draft;
    } catch (error) {
      console.error('Error creating draft with custom ID:', error);
      // Fallback to in-memory storage on error
      const draft = {
        ...data,
        id: draftId,
        status: data.status || 'draft',
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      drafts.set(draftId, draft);
      console.log(`⚠️  Draft created in memory (fallback) with custom ID: ${draftId}`);
      return draft;
    }
  }

  async getDraft(draftId) {
    try {
      const db = await this.getDatabase();

      if (db) {
        // Get from Firebase
        const doc = await db.collection(this.collection).doc(draftId).get();
        if (doc.exists) {
          console.log(`✅ Draft retrieved from Firebase: ${draftId}`);
          return doc.data();
        }
      }

      // Fallback to in-memory storage
      const draft = drafts.get(draftId);
      if (draft) {
        console.log(`⚠️  Draft retrieved from memory: ${draftId}`);
        return draft;
      }

      throw new Error('Draft not found');
    } catch (error) {
      console.error('Error getting draft:', error);
      throw error;
    }
  }

  async updateDraft(draftId, updates) {
    try {
      const db = await this.getDatabase();
      let currentDraft = null;

      if (db) {
        // Get current draft from Firebase
        const doc = await db.collection(this.collection).doc(draftId).get();
        if (doc.exists) {
          currentDraft = doc.data();
        }
      }

      // Fallback to in-memory storage
      if (!currentDraft) {
        currentDraft = drafts.get(draftId);
      }

      if (!currentDraft) {
        throw new Error('Draft not found');
      }

      const updatedDraft = {
        ...currentDraft,
        ...updates,
        id: draftId,
        updatedAt: new Date().toISOString()
      };

      if (db) {
        // Update in Firebase
        await db.collection(this.collection).doc(draftId).set(updatedDraft);
        console.log(`✅ Draft updated in Firebase: ${draftId}`);
      } else {
        // Update in memory
        drafts.set(draftId, updatedDraft);
        console.log(`⚠️  Draft updated in memory: ${draftId}`);
      }

      return updatedDraft;
    } catch (error) {
      console.error('Error updating draft:', error);
      throw error;
    }
  }

  async listDrafts(userId, options = {}) {
    try {
      const db = await this.getDatabase();
      let draftList = [];

      if (db) {
        try {
          // Get from Firebase with simple query first
          let query = db.collection(this.collection);

          // Apply pagination
          const limit = options.limit || 50;
          query = query.limit(limit);

          const snapshot = await query.get();
          let allDrafts = snapshot.docs.map(doc => doc.data());

          // Apply filters in memory (until indexes are created)
          if (userId && userId !== 'anonymous') {
            allDrafts = allDrafts.filter(d => d.userId === userId);
          }

          if (options.status) {
            allDrafts = allDrafts.filter(d => d.status === options.status);
          }

          if (options.companyId) {
            allDrafts = allDrafts.filter(d => d.companyData?.id === options.companyId);
          }

          // Sort by creation date (newest first)
          allDrafts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

          // Apply offset
          const offset = options.offset || 0;
          draftList = allDrafts.slice(offset, offset + limit);

          console.log(`✅ Retrieved ${draftList.length} drafts from Firebase`);
        } catch (firebaseError) {
          console.log('⚠️  Firebase query failed, falling back to memory storage');
          throw firebaseError;
        }
      } else {
        // Fallback to in-memory storage
        draftList = Array.from(drafts.values());

        // Filter by user ID
        if (userId && userId !== 'anonymous') {
          draftList = draftList.filter(d => d.userId === userId);
        }

        // Apply additional filters
        if (options.status) {
          draftList = draftList.filter(d => d.status === options.status);
        }
        if (options.companyId) {
          draftList = draftList.filter(d => d.companyData?.id === options.companyId);
        }

        // Sort by creation date (newest first)
        draftList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // Apply pagination
        const limit = options.limit || 50;
        const offset = options.offset || 0;
        draftList = draftList.slice(offset, offset + limit);
        console.log(`⚠️  Retrieved ${draftList.length} drafts from memory`);
      }

      return draftList;
    } catch (error) {
      console.error('Error listing drafts:', error);
      throw error;
    }
  }

  async deleteDraft(draftId) {
    try {
      const db = await this.getDatabase();
      let draftExists = false;

      if (db) {
        // Check if draft exists in Firebase
        const doc = await db.collection(this.collection).doc(draftId).get();
        if (doc.exists) {
          await db.collection(this.collection).doc(draftId).delete();
          console.log(`✅ Draft deleted from Firebase: ${draftId}`);
          draftExists = true;
        }
      }

      // Check in-memory storage
      if (drafts.has(draftId)) {
        drafts.delete(draftId);
        console.log(`⚠️  Draft deleted from memory: ${draftId}`);
        draftExists = true;
      }

      if (!draftExists) {
        throw new Error('Draft not found');
      }

      return true;
    } catch (error) {
      console.error('Error deleting draft:', error);
      throw error;
    }
  }

  async saveAlternativeKeywords(draftId, alternativeKeywords) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }

      // Store alternative keywords for future use
      const updatedDraft = {
        ...draft,
        alternativeKeywords,
        updatedAt: new Date().toISOString()
      };

      drafts.set(draftId, updatedDraft);
      console.log(`Alternative keywords saved for draft: ${draftId}`);
      return updatedDraft;
    } catch (error) {
      console.error('Error saving alternative keywords:', error);
      throw error;
    }
  }

  async getDraftStats(userId) {
    try {
      let userDrafts = Array.from(drafts.values());

      if (userId && userId !== 'anonymous') {
        userDrafts = userDrafts.filter(d => d.userId === userId);
      }

      const stats = {
        total: userDrafts.length,
        byStatus: {},
        recentActivity: userDrafts
          .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
          .slice(0, 5)
          .map(d => ({
            id: d.id,
            status: d.status,
            companyName: d.companyData?.companyName,
            selectedKeyword: d.selectedKeyword,
            updatedAt: d.updatedAt
          }))
      };

      // Count by status
      userDrafts.forEach(draft => {
        const status = draft.status || 'unknown';
        stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error getting draft stats:', error);
      throw error;
    }
  }
}

module.exports = new DraftService();