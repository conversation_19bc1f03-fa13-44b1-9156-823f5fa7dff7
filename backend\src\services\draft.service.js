// Temporary in-memory storage
const drafts = new Map();

class DraftService {
  async createDraft(data) {
    try {
      const draftId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const draft = {
        ...data,
        id: draftId,
        status: data.status || 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      drafts.set(draftId, draft);
      console.log(`Draft created: ${draftId}`);
      return draft;
    } catch (error) {
      console.error('Error creating draft:', error);
      throw error;
    }
  }

  async getDraft(draftId) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }
      return draft;
    } catch (error) {
      console.error('Error getting draft:', error);
      throw error;
    }
  }

  async updateDraft(draftId, updates) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }

      const updatedDraft = {
        ...draft,
        ...updates,
        id: draftId,
        updatedAt: new Date().toISOString()
      };

      drafts.set(draftId, updatedDraft);
      console.log(`Draft updated: ${draftId}`);
      return updatedDraft;
    } catch (error) {
      console.error('Error updating draft:', error);
      throw error;
    }
  }

  async listDrafts(userId, options = {}) {
    try {
      let draftList = Array.from(drafts.values());

      // Filter by user ID
      if (userId && userId !== 'anonymous') {
        draftList = draftList.filter(d => d.userId === userId);
      }

      // Apply additional filters
      if (options.status) {
        draftList = draftList.filter(d => d.status === options.status);
      }
      if (options.companyId) {
        draftList = draftList.filter(d => d.companyData?.id === options.companyId);
      }

      // Sort by creation date (newest first)
      draftList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // Apply pagination
      const limit = options.limit || 50;
      const offset = options.offset || 0;

      return draftList.slice(offset, offset + limit);
    } catch (error) {
      console.error('Error listing drafts:', error);
      throw error;
    }
  }

  async deleteDraft(draftId) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }

      drafts.delete(draftId);
      console.log(`Draft deleted: ${draftId}`);
      return true;
    } catch (error) {
      console.error('Error deleting draft:', error);
      throw error;
    }
  }

  async saveAlternativeKeywords(draftId, alternativeKeywords) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }

      // Store alternative keywords for future use
      const updatedDraft = {
        ...draft,
        alternativeKeywords,
        updatedAt: new Date().toISOString()
      };

      drafts.set(draftId, updatedDraft);
      console.log(`Alternative keywords saved for draft: ${draftId}`);
      return updatedDraft;
    } catch (error) {
      console.error('Error saving alternative keywords:', error);
      throw error;
    }
  }

  async getDraftStats(userId) {
    try {
      let userDrafts = Array.from(drafts.values());

      if (userId && userId !== 'anonymous') {
        userDrafts = userDrafts.filter(d => d.userId === userId);
      }

      const stats = {
        total: userDrafts.length,
        byStatus: {},
        recentActivity: userDrafts
          .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
          .slice(0, 5)
          .map(d => ({
            id: d.id,
            status: d.status,
            companyName: d.companyData?.companyName,
            selectedKeyword: d.selectedKeyword,
            updatedAt: d.updatedAt
          }))
      };

      // Count by status
      userDrafts.forEach(draft => {
        const status = draft.status || 'unknown';
        stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error getting draft stats:', error);
      throw error;
    }
  }
}

module.exports = new DraftService();