const axios = require('axios');

class WordPressService {
  constructor() {
    this.baseUrl = process.env.WORDPRESS_URL;
    this.username = process.env.WORDPRESS_USERNAME;
    this.password = process.env.WORDPRESS_PASSWORD;
    this.authToken = Buffer.from(`${this.username}:${this.password}`).toString('base64');
  }

  async createPost(postData) {
    try {
      const wpPostData = {
        title: postData.title,
        content: postData.content,
        excerpt: postData.excerpt,
        status: postData.status || 'draft',
        format: 'standard',
        categories: [], // Add category IDs if needed
        tags: [], // Add tag IDs if needed
        meta: postData.meta || {}
      };

      const response = await axios.post(
        `${this.baseUrl}/wp-json/wp/v2/posts`,
        wpPostData,
        {
          headers: {
            'Authorization': `Basic ${this.authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        postId: response.data.id,
        postUrl: response.data.link,
        editUrl: `${this.baseUrl}/wp-admin/post.php?post=${response.data.id}&action=edit`
      };
    } catch (error) {
      console.error('WordPress API Error:', error.response?.data || error.message);
      throw new Error(`Failed to create WordPress post: ${error.message}`);
    }
  }

  async testConnection(config) {
    try {
      // Use provided config or default to environment variables
      const baseUrl = config?.url || this.baseUrl;
      const username = config?.username || this.username;
      const password = config?.password || this.password;
      const authToken = Buffer.from(`${username}:${password}`).toString('base64');

      const response = await axios.get(
        `${baseUrl}/wp-json/wp/v2/users/me`,
        {
          headers: {
            'Authorization': `Basic ${authToken}`
          }
        }
      );

      return {
        connected: true,
        message: 'WordPress connection successful',
        details: {
          user: response.data.name,
          userId: response.data.id,
          site: baseUrl
        }
      };
    } catch (error) {
      return {
        connected: false,
        message: 'WordPress connection failed',
        error: error.message
      };
    }
  }

  async deployPost(postData) {
    try {
      const config = postData.config || {};
      const baseUrl = config.url || this.baseUrl;
      const username = config.username || this.username;
      const password = config.password || this.password;
      const authToken = Buffer.from(`${username}:${password}`).toString('base64');

      const wpPostData = {
        title: postData.title,
        content: postData.content,
        excerpt: postData.metaDescription,
        status: config.status || 'draft',
        format: 'standard',
        categories: config.categories || [],
        tags: config.tags || [],
        meta: {
          _yoast_wpseo_title: postData.metaTitle,
          _yoast_wpseo_metadesc: postData.metaDescription,
          ...config.meta
        }
      };

      const response = await axios.post(
        `${baseUrl}/wp-json/wp/v2/posts`,
        wpPostData,
        {
          headers: {
            'Authorization': `Basic ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // Set featured image if provided
      if (postData.featuredImage) {
        await this.setFeaturedImage(response.data.id, postData.featuredImage, config);
      }

      return {
        success: true,
        postId: response.data.id,
        url: response.data.link,
        editUrl: `${baseUrl}/wp-admin/post.php?post=${response.data.id}&action=edit`
      };
    } catch (error) {
      console.error('WordPress deployment error:', error.response?.data || error.message);
      throw new Error(`Failed to deploy to WordPress: ${error.message}`);
    }
  }

  async updatePost(updateData) {
    try {
      const config = updateData.config || {};
      const baseUrl = config.url || this.baseUrl;
      const username = config.username || this.username;
      const password = config.password || this.password;
      const authToken = Buffer.from(`${username}:${password}`).toString('base64');

      const wpPostData = {
        title: updateData.title,
        content: updateData.content,
        excerpt: updateData.metaDescription,
        meta: {
          _yoast_wpseo_title: updateData.metaTitle,
          _yoast_wpseo_metadesc: updateData.metaDescription,
          ...config.meta
        }
      };

      // Only include fields that should be updated
      if (updateData.updateFields) {
        const filteredData = {};
        updateData.updateFields.forEach(field => {
          if (wpPostData[field] !== undefined) {
            filteredData[field] = wpPostData[field];
          }
        });
        Object.assign(wpPostData, filteredData);
      }

      const response = await axios.post(
        `${baseUrl}/wp-json/wp/v2/posts/${updateData.postId}`,
        wpPostData,
        {
          headers: {
            'Authorization': `Basic ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        postId: response.data.id,
        url: response.data.link,
        editUrl: `${baseUrl}/wp-admin/post.php?post=${response.data.id}&action=edit`
      };
    } catch (error) {
      console.error('WordPress update error:', error.response?.data || error.message);
      throw new Error(`Failed to update WordPress post: ${error.message}`);
    }
  }

  async deletePost(deleteData) {
    try {
      const config = deleteData.config || {};
      const baseUrl = config.url || this.baseUrl;
      const username = config.username || this.username;
      const password = config.password || this.password;
      const authToken = Buffer.from(`${username}:${password}`).toString('base64');

      await axios.delete(
        `${baseUrl}/wp-json/wp/v2/posts/${deleteData.postId}`,
        {
          headers: {
            'Authorization': `Basic ${authToken}`
          }
        }
      );

      return {
        success: true,
        message: 'Post deleted successfully'
      };
    } catch (error) {
      console.error('WordPress delete error:', error.response?.data || error.message);
      throw new Error(`Failed to delete WordPress post: ${error.message}`);
    }
  }

  async getPostStatus(statusData) {
    try {
      const config = statusData.config || {};
      const baseUrl = config.url || this.baseUrl;
      const username = config.username || this.username;
      const password = config.password || this.password;
      const authToken = Buffer.from(`${username}:${password}`).toString('base64');

      const response = await axios.get(
        `${baseUrl}/wp-json/wp/v2/posts/${statusData.postId}`,
        {
          headers: {
            'Authorization': `Basic ${authToken}`
          }
        }
      );

      return {
        id: response.data.id,
        status: response.data.status,
        title: response.data.title.rendered,
        url: response.data.link,
        modified: response.data.modified,
        author: response.data.author
      };
    } catch (error) {
      console.error('WordPress status error:', error.response?.data || error.message);
      throw new Error(`Failed to get WordPress post status: ${error.message}`);
    }
  }

  async setFeaturedImage(postId, imageData, config) {
    try {
      // This is a placeholder for featured image functionality
      // In a real implementation, you would upload the image and set it as featured
      console.log(`Setting featured image for post ${postId}:`, imageData);
      return { success: true };
    } catch (error) {
      console.error('Featured image error:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = new WordPressService();