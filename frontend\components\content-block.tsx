"use client"

import React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Edit3, <PERSON>freshC<PERSON>, Trash2, FileText, ImageIcon, Upload } from "lucide-react"
import type { BlogBlock } from "@/types/api"

// Function to format content and remove markdown formatting
function formatContent(content: string): string {
  if (!content) return '';

  return content
    // Remove markdown bold (**text**)
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Remove markdown italic (*text*)
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert line breaks to <br> tags
    .replace(/\n/g, '<br>')
    // Remove any remaining asterisks
    .replace(/\*/g, '')
    // Clean up extra spaces
    .replace(/\s+/g, ' ')
    .trim();
}

interface ContentBlockProps {
  block: BlogBlock
  onEdit: () => void
  onRegenerate: () => void
  onDelete?: () => void
  onImageGenerate?: (blockId: string, description: string) => void
  onImageUpload?: (blockId: string, file: File) => void
}

export function ContentBlock({ block, onEdit, onRegenerate, onDelete, onImageGenerate, onImageUpload }: ContentBlockProps) {
  const [imageDescription, setImageDescription] = React.useState(block.description || '')
  const [altText, setAltText] = React.useState(block.alt || '')
  const [isGenerating, setIsGenerating] = React.useState(false)
  const getBlockIcon = () => {
    switch (block.type) {
      case "image":
        return <ImageIcon className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getBlockTitle = () => {
    switch (block.type) {
      case "introduction":
        return "Introduction"
      case "section":
        return block.h2 || "Section"
      case "image":
        return `${block.imageType === "feature" ? "Feature" : "In-blog"} Image`
      case "conclusion":
        return "Conclusion"
      case "references":
        return "References"
      default:
        return "Content Block"
    }
  }

  if (block.type === "image") {
    return (
      <Card className="border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="py-8">
          <div className="text-center space-y-4">
            {block.imageUrl ? (
              <div className="space-y-4">
                <img
                  src={block.imageUrl}
                  alt={block.alt || 'Generated image'}
                  className="mx-auto max-w-full h-48 object-cover rounded-lg border"
                />
                <div>
                  <h3 className="font-medium text-gray-900">{getBlockTitle()}</h3>
                  <p className="text-sm text-gray-500 mt-1">{block.description || block.alt}</p>
                  {block.enhancedPrompt && (
                    <p className="text-xs text-gray-400 mt-2">AI Prompt: {block.enhancedPrompt}</p>
                  )}
                </div>
              </div>
            ) : (
              <>
                <div className="mx-auto w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>

                <div>
                  <h3 className="font-medium text-gray-900">{getBlockTitle()}</h3>
                  <p className="text-sm text-gray-500 mt-1">Upload an image or generate with AI</p>
                </div>
              </>
            )}

            <div className="space-y-2">
              <Input
                placeholder="Alt text (required)"
                value={altText}
                onChange={(e) => setAltText(e.target.value)}
                className="max-w-md mx-auto"
              />
              <Input
                placeholder="Image description for AI generation"
                value={imageDescription}
                onChange={(e) => setImageDescription(e.target.value)}
                className="max-w-md mx-auto"
              />
            </div>

            <div className="flex gap-2 justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = 'image/*';
                  input.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file && onImageUpload) {
                      onImageUpload(block.id, file);
                    }
                  };
                  input.click();
                }}
              >
                <Upload className="h-4 w-4 mr-1" />
                Upload Image
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={!imageDescription || isGenerating}
                onClick={() => {
                  if (imageDescription && onImageGenerate) {
                    setIsGenerating(true);
                    onImageGenerate(block.id, imageDescription);
                    // Reset generating state after a delay (will be handled by parent)
                    setTimeout(() => setIsGenerating(false), 3000);
                  }
                }}
              >
                {isGenerating ? "Generating..." : "Generate with AI"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getBlockIcon()}
            <CardTitle className="text-lg">{getBlockTitle()}</CardTitle>
            {block.wordCount && (
              <Badge variant="outline" className="text-xs">
                {block.wordCount} words
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-1">
            <Button onClick={onEdit} variant="ghost" size="sm">
              <Edit3 className="h-4 w-4" />
            </Button>

            <Button onClick={onRegenerate} variant="ghost" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>

            {onDelete && (
              <Button onClick={onDelete} variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="prose prose-sm max-w-none">
          {block.h2 && <h2 className="text-xl font-semibold text-gray-900 mb-3">{block.h2}</h2>}
          <div
            className="text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{
              __html: formatContent(block.content || '')
            }}
          />
        </div>
      </CardContent>
    </Card>
  )
}
