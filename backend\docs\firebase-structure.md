# Firebase Database Structure

## Collections

### 1. `drafts` Collection

Stores all blog drafts with their complete workflow state.

#### Document Structure:
```javascript
{
  id: "draft_1642123456789_abc123def",
  userId: "user_123",
  status: "draft" | "keyword_analysis" | "meta_generation" | "content_generation" | "completed",
  createdAt: "2025-01-21T10:00:00.000Z",
  updatedAt: "2025-01-21T10:30:00.000Z",
  
  // Company Information
  companyData: {
    id: "company_123",
    companyName: "SolarTech Solutions",
    industry: "Solar Energy",
    serviceOverview: "Complete solar installation services",
    targetAudience: "Homeowners",
    brandVoice: "Professional and trustworthy",
    contactInfo: {
      email: "<EMAIL>",
      phone: "******-0123"
    }
  },
  
  // Keyword Analysis
  selectedKeyword: "solar panel installation",
  alternativeKeywords: ["solar panels", "solar energy", "renewable energy"],
  keywordCluster: {
    primaryKeyword: "solar panel installation",
    secondaryKeywords: ["solar installation cost", "solar benefits"],
    longTailKeywords: ["how much does solar installation cost"],
    lsiKeywords: ["renewable energy", "energy efficiency"]
  },
  
  // Competitor Analysis
  competitorAnalysis: {
    competitors: [
      {
        name: "SunPower",
        authority: "High",
        contentFocus: "Premium solar solutions"
      }
    ],
    commonH2Topics: ["Installation Process", "Cost Analysis"],
    contentGaps: ["Local permit requirements"],
    opportunities: ["Focus on ROI calculations"]
  },
  
  // Trends
  trends: {
    currentTrends: ["Battery storage integration", "Smart home connectivity"],
    seasonalFactors: ["Tax incentive deadlines"],
    industryInsights: ["Federal policy changes"]
  },
  
  // Meta Options
  metaOptions: [
    {
      id: "meta_1",
      h1Title: "Complete Solar Panel Installation Guide 2025",
      metaDescription: "Expert guide to solar panel installation...",
      seoScore: 92,
      engagementScore: 88,
      keywordDensity: 2.5
    }
  ],
  selectedMeta: {
    id: "meta_1",
    h1Title: "Complete Solar Panel Installation Guide 2025",
    metaDescription: "Expert guide to solar panel installation..."
  },
  
  // Generated Content
  content: {
    introduction: "Rising energy costs have homeowners...",
    featureImage: {
      alt: "Solar panel installation process",
      title: "Professional Solar Installation",
      description: "Workers installing solar panels on residential roof"
    },
    sections: [
      {
        id: "section_1",
        heading: "Understanding Solar Panel Installation",
        content: "Solar panel installation involves...",
        wordCount: 350
      }
    ],
    images: [
      {
        id: "image_1",
        alt: "Solar installation tools",
        title: "Essential Installation Tools",
        description: "Professional tools used in solar installation",
        placement: "after_section_2"
      }
    ],
    conclusion: "Solar panel installation offers...",
    references: [
      {
        title: "Federal Solar Tax Credit Extended",
        source: "Solar Power World",
        url: "https://example.com/news1",
        publishedAt: "2025-01-15"
      }
    ]
  },
  
  // WordPress Deployment
  wordpressData: {
    postId: 12345,
    postUrl: "https://example.com/blog/solar-panel-installation-guide",
    deployedAt: "2025-01-21T11:00:00.000Z",
    status: "published"
  }
}
```

#### Indexes:
- `userId` (for user-specific queries)
- `status` (for filtering by workflow stage)
- `createdAt` (for chronological sorting)
- `companyData.id` (for company-specific queries)

### 2. `companies` Collection (Future Enhancement)

Stores company information for faster access.

#### Document Structure:
```javascript
{
  id: "company_123",
  companyName: "SolarTech Solutions",
  industry: "Solar Energy",
  serviceOverview: "Complete solar installation services",
  targetAudience: "Homeowners",
  brandVoice: "Professional and trustworthy",
  contactInfo: {
    email: "<EMAIL>",
    phone: "******-0123"
  },
  createdAt: "2025-01-21T10:00:00.000Z",
  updatedAt: "2025-01-21T10:00:00.000Z"
}
```

### 3. `users` Collection (Future Enhancement)

Stores user information and preferences.

#### Document Structure:
```javascript
{
  id: "user_123",
  email: "<EMAIL>",
  name: "John Doe",
  role: "content_creator",
  preferences: {
    defaultCompany: "company_123",
    contentStyle: "professional",
    wordCountPreference: 2500
  },
  createdAt: "2025-01-21T10:00:00.000Z",
  lastLoginAt: "2025-01-21T10:00:00.000Z"
}
```

## Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Drafts collection
    match /drafts/{draftId} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         request.auth.token.admin == true);
    }
    
    // Companies collection
    match /companies/{companyId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.token.admin == true;
    }
    
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (resource.id == request.auth.uid || 
         request.auth.token.admin == true);
    }
  }
}
```

## Backup Strategy

1. **Automated Backups**: Enable Firebase automatic backups
2. **Export Scripts**: Regular exports to Cloud Storage
3. **Version Control**: Track schema changes in git
4. **Recovery Procedures**: Documented restoration process

## Performance Optimization

1. **Composite Indexes**: For complex queries
2. **Pagination**: Limit query results
3. **Caching**: Cache frequently accessed data
4. **Batch Operations**: Group related writes
