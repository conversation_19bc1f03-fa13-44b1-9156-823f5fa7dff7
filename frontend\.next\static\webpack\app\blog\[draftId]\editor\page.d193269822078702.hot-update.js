"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/[draftId]/editor/page",{

/***/ "(app-pages-browser)/./components/content-block.tsx":
/*!**************************************!*\
  !*** ./components/content-block.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentBlock: () => (/* binding */ ContentBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ ContentBlock auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Function to format content and remove markdown formatting\nfunction formatContent(content) {\n    if (!content) return '';\n    return content// Remove markdown bold (**text**)\n    .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')// Remove markdown italic (*text*)\n    .replace(/\\*(.*?)\\*/g, '<em>$1</em>')// Convert double line breaks to paragraphs\n    .replace(/\\n\\n/g, '</p><p>')// Convert single line breaks to <br> tags\n    .replace(/\\n/g, '<br>')// Remove any remaining asterisks\n    .replace(/\\*/g, '')// Clean up extra spaces\n    .replace(/\\s+/g, ' ')// Wrap in paragraph tags if not already wrapped\n    .replace(/^(?!<p>)/, '<p>').replace(/(?!<\\/p>)$/, '</p>')// Fix empty paragraphs\n    .replace(/<p><\\/p>/g, '').trim();\n}\nfunction ContentBlock(param) {\n    let { block, onEdit, onRegenerate, onDelete, onImageGenerate, onImageUpload } = param;\n    _s();\n    const [imageDescription, setImageDescription] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(block.description || '');\n    const [altText, setAltText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(block.alt || '');\n    const [isGenerating, setIsGenerating] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const getBlockIcon = ()=>{\n        switch(block.type){\n            case \"image\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBlockTitle = ()=>{\n        switch(block.type){\n            case \"introduction\":\n                return \"Introduction\";\n            case \"section\":\n                return block.h2 || \"Section\";\n            case \"image\":\n                return \"\".concat(block.imageType === \"feature\" ? \"Feature\" : \"In-blog\", \" Image\");\n            case \"conclusion\":\n                return \"Conclusion\";\n            case \"references\":\n                return \"References\";\n            default:\n                return \"Content Block\";\n        }\n    };\n    if (block.type === \"image\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        block.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: block.imageUrl,\n                                    alt: block.alt || 'Generated image',\n                                    className: \"mx-auto max-w-full h-48 object-cover rounded-lg border\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: getBlockTitle()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-1\",\n                                            children: block.description || block.alt\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this),\n                                        block.enhancedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mt-2\",\n                                            children: [\n                                                \"AI Prompt: \",\n                                                block.enhancedPrompt\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: getBlockTitle()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-1\",\n                                            children: \"Upload an image or generate with AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    placeholder: \"Alt text (required)\",\n                                    value: altText,\n                                    onChange: (e)=>setAltText(e.target.value),\n                                    className: \"max-w-md mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    placeholder: \"Image description for AI generation\",\n                                    value: imageDescription,\n                                    onChange: (e)=>setImageDescription(e.target.value),\n                                    className: \"max-w-md mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        const input = document.createElement('input');\n                                        input.type = 'file';\n                                        input.accept = 'image/*';\n                                        input.onchange = (e)=>{\n                                            var _e_target_files;\n                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                            if (file && onImageUpload) {\n                                                onImageUpload(block.id, file);\n                                            }\n                                        };\n                                        input.click();\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upload Image\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    disabled: !imageDescription || isGenerating,\n                                    onClick: ()=>{\n                                        if (imageDescription && onImageGenerate) {\n                                            setIsGenerating(true);\n                                            onImageGenerate(block.id, imageDescription);\n                                            // Reset generating state after a delay (will be handled by parent)\n                                            setTimeout(()=>setIsGenerating(false), 3000);\n                                        }\n                                    },\n                                    children: isGenerating ? \"Generating...\" : \"Generate with AI\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"hover:shadow-md transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getBlockIcon(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: getBlockTitle()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                block.wordCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        block.wordCount,\n                                        \" words\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onEdit,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onRegenerate,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onDelete,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-lg max-w-none\",\n                    children: [\n                        block.h2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4 leading-tight border-b border-gray-200 pb-2\",\n                            children: block.h2\n                        }, void 0, false, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-800 leading-relaxed text-base blog-content\",\n                            dangerouslySetInnerHTML: {\n                                __html: formatContent(block.content || '')\n                            },\n                            style: {\n                                lineHeight: '1.7',\n                                fontSize: '16px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(ContentBlock, \"abHsapQepg1C143rQeIcFnowAUk=\");\n_c = ContentBlock;\nvar _c;\n$RefreshReg$(_c, \"ContentBlock\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvY29udGVudC1ibG9jay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5QjtBQUVzRDtBQUNoQztBQUNGO0FBQ0E7QUFDdUM7QUFHcEYsNERBQTREO0FBQzVELFNBQVNjLGNBQWNDLE9BQWU7SUFDcEMsSUFBSSxDQUFDQSxTQUFTLE9BQU87SUFFckIsT0FBT0EsT0FDTCxrQ0FBa0M7S0FDakNDLE9BQU8sQ0FBQyxrQkFBa0Isc0JBQzNCLGtDQUFrQztLQUNqQ0EsT0FBTyxDQUFDLGNBQWMsY0FDdkIsMkNBQTJDO0tBQzFDQSxPQUFPLENBQUMsU0FBUyxVQUNsQiwwQ0FBMEM7S0FDekNBLE9BQU8sQ0FBQyxPQUFPLE9BQ2hCLGlDQUFpQztLQUNoQ0EsT0FBTyxDQUFDLE9BQU8sR0FDaEIsd0JBQXdCO0tBQ3ZCQSxPQUFPLENBQUMsUUFBUSxJQUNqQixnREFBZ0Q7S0FDL0NBLE9BQU8sQ0FBQyxZQUFZLE9BQ3BCQSxPQUFPLENBQUMsY0FBYyxPQUN2Qix1QkFBdUI7S0FDdEJBLE9BQU8sQ0FBQyxhQUFhLElBQ3JCQyxJQUFJO0FBQ1Q7QUFXTyxTQUFTQyxhQUFhLEtBQTRGO1FBQTVGLEVBQUVDLEtBQUssRUFBRUMsTUFBTSxFQUFFQyxZQUFZLEVBQUVDLFFBQVEsRUFBRUMsZUFBZSxFQUFFQyxhQUFhLEVBQXFCLEdBQTVGOztJQUMzQixNQUFNLENBQUNDLGtCQUFrQkMsb0JBQW9CLEdBQUcxQixxREFBYyxDQUFDbUIsTUFBTVMsV0FBVyxJQUFJO0lBQ3BGLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHOUIscURBQWMsQ0FBQ21CLE1BQU1ZLEdBQUcsSUFBSTtJQUMxRCxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHakMscURBQWMsQ0FBQztJQUN2RCxNQUFNa0MsZUFBZTtRQUNuQixPQUFRZixNQUFNZ0IsSUFBSTtZQUNoQixLQUFLO2dCQUNILHFCQUFPLDhEQUFDdkIsNEhBQVNBO29CQUFDd0IsV0FBVTs7Ozs7O1lBQzlCO2dCQUNFLHFCQUFPLDhEQUFDekIsNEhBQVFBO29CQUFDeUIsV0FBVTs7Ozs7O1FBQy9CO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEIsT0FBUWxCLE1BQU1nQixJQUFJO1lBQ2hCLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBT2hCLE1BQU1tQixFQUFFLElBQUk7WUFDckIsS0FBSztnQkFDSCxPQUFPLEdBQXlELE9BQXREbkIsTUFBTW9CLFNBQVMsS0FBSyxZQUFZLFlBQVksV0FBVTtZQUNsRSxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLElBQUlwQixNQUFNZ0IsSUFBSSxLQUFLLFNBQVM7UUFDMUIscUJBQ0UsOERBQUNsQyxxREFBSUE7WUFBQ21DLFdBQVU7c0JBQ2QsNEVBQUNsQyw0REFBV0E7Z0JBQUNrQyxXQUFVOzBCQUNyQiw0RUFBQ0k7b0JBQUlKLFdBQVU7O3dCQUNaakIsTUFBTXNCLFFBQVEsaUJBQ2IsOERBQUNEOzRCQUFJSixXQUFVOzs4Q0FDYiw4REFBQ007b0NBQ0NDLEtBQUt4QixNQUFNc0IsUUFBUTtvQ0FDbkJWLEtBQUtaLE1BQU1ZLEdBQUcsSUFBSTtvQ0FDbEJLLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQ0k7O3NEQUNDLDhEQUFDSTs0Q0FBR1IsV0FBVTtzREFBNkJDOzs7Ozs7c0RBQzNDLDhEQUFDUTs0Q0FBRVQsV0FBVTtzREFBOEJqQixNQUFNUyxXQUFXLElBQUlULE1BQU1ZLEdBQUc7Ozs7Ozt3Q0FDeEVaLE1BQU0yQixjQUFjLGtCQUNuQiw4REFBQ0Q7NENBQUVULFdBQVU7O2dEQUE2QjtnREFBWWpCLE1BQU0yQixjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7aURBS2hGOzs4Q0FDRSw4REFBQ047b0NBQUlKLFdBQVU7OENBQ2IsNEVBQUN4Qiw0SEFBU0E7d0NBQUN3QixXQUFVOzs7Ozs7Ozs7Ozs4Q0FHdkIsOERBQUNJOztzREFDQyw4REFBQ0k7NENBQUdSLFdBQVU7c0RBQTZCQzs7Ozs7O3NEQUMzQyw4REFBQ1E7NENBQUVULFdBQVU7c0RBQTZCOzs7Ozs7Ozs7Ozs7OztzQ0FLaEQsOERBQUNJOzRCQUFJSixXQUFVOzs4Q0FDYiw4REFBQzdCLHVEQUFLQTtvQ0FDSndDLGFBQVk7b0NBQ1pDLE9BQU9uQjtvQ0FDUG9CLFVBQVUsQ0FBQ0MsSUFBTXBCLFdBQVdvQixFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQzFDWixXQUFVOzs7Ozs7OENBRVosOERBQUM3Qix1REFBS0E7b0NBQ0p3QyxhQUFZO29DQUNaQyxPQUFPdkI7b0NBQ1B3QixVQUFVLENBQUNDLElBQU14QixvQkFBb0J3QixFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQ25EWixXQUFVOzs7Ozs7Ozs7Ozs7c0NBSWQsOERBQUNJOzRCQUFJSixXQUFVOzs4Q0FDYiw4REFBQy9CLHlEQUFNQTtvQ0FDTCtDLFNBQVE7b0NBQ1JDLE1BQUs7b0NBQ0xDLFNBQVM7d0NBQ1AsTUFBTUMsUUFBUUMsU0FBU0MsYUFBYSxDQUFDO3dDQUNyQ0YsTUFBTXBCLElBQUksR0FBRzt3Q0FDYm9CLE1BQU1HLE1BQU0sR0FBRzt3Q0FDZkgsTUFBTUksUUFBUSxHQUFHLENBQUNUO2dEQUNIOzRDQUFiLE1BQU1VLFFBQU8sb0JBQUdULE1BQU0sQ0FBc0JVLEtBQUssY0FBcEMscURBQXNDLENBQUMsRUFBRTs0Q0FDdEQsSUFBSUQsUUFBUXBDLGVBQWU7Z0RBQ3pCQSxjQUFjTCxNQUFNMkMsRUFBRSxFQUFFRjs0Q0FDMUI7d0NBQ0Y7d0NBQ0FMLE1BQU1RLEtBQUs7b0NBQ2I7O3NEQUVBLDhEQUFDbEQsNEhBQU1BOzRDQUFDdUIsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs4Q0FHckMsOERBQUMvQix5REFBTUE7b0NBQ0wrQyxTQUFRO29DQUNSQyxNQUFLO29DQUNMVyxVQUFVLENBQUN2QyxvQkFBb0JPO29DQUMvQnNCLFNBQVM7d0NBQ1AsSUFBSTdCLG9CQUFvQkYsaUJBQWlCOzRDQUN2Q1UsZ0JBQWdCOzRDQUNoQlYsZ0JBQWdCSixNQUFNMkMsRUFBRSxFQUFFckM7NENBQzFCLG1FQUFtRTs0Q0FDbkV3QyxXQUFXLElBQU1oQyxnQkFBZ0IsUUFBUTt3Q0FDM0M7b0NBQ0Y7OENBRUNELGVBQWUsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT2hEO0lBRUEscUJBQ0UsOERBQUMvQixxREFBSUE7UUFBQ21DLFdBQVU7OzBCQUNkLDhEQUFDakMsMkRBQVVBO2dCQUFDaUMsV0FBVTswQkFDcEIsNEVBQUNJO29CQUFJSixXQUFVOztzQ0FDYiw4REFBQ0k7NEJBQUlKLFdBQVU7O2dDQUNaRjs4Q0FDRCw4REFBQzlCLDBEQUFTQTtvQ0FBQ2dDLFdBQVU7OENBQVdDOzs7Ozs7Z0NBQy9CbEIsTUFBTStDLFNBQVMsa0JBQ2QsOERBQUM1RCx1REFBS0E7b0NBQUM4QyxTQUFRO29DQUFVaEIsV0FBVTs7d0NBQ2hDakIsTUFBTStDLFNBQVM7d0NBQUM7Ozs7Ozs7Ozs7Ozs7c0NBS3ZCLDhEQUFDMUI7NEJBQUlKLFdBQVU7OzhDQUNiLDhEQUFDL0IseURBQU1BO29DQUFDaUQsU0FBU2xDO29DQUFRZ0MsU0FBUTtvQ0FBUUMsTUFBSzs4Q0FDNUMsNEVBQUM3Qyw0SEFBS0E7d0NBQUM0QixXQUFVOzs7Ozs7Ozs7Ozs4Q0FHbkIsOERBQUMvQix5REFBTUE7b0NBQUNpRCxTQUFTakM7b0NBQWMrQixTQUFRO29DQUFRQyxNQUFLOzhDQUNsRCw0RUFBQzVDLDZIQUFTQTt3Q0FBQzJCLFdBQVU7Ozs7Ozs7Ozs7O2dDQUd0QmQsMEJBQ0MsOERBQUNqQix5REFBTUE7b0NBQUNpRCxTQUFTaEM7b0NBQVU4QixTQUFRO29DQUFRQyxNQUFLO29DQUFLakIsV0FBVTs4Q0FDN0QsNEVBQUMxQiw2SEFBTUE7d0NBQUMwQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU81Qiw4REFBQ2xDLDREQUFXQTswQkFDViw0RUFBQ3NDO29CQUFJSixXQUFVOzt3QkFDWmpCLE1BQU1tQixFQUFFLGtCQUNQLDhEQUFDQTs0QkFBR0YsV0FBVTtzQ0FDWGpCLE1BQU1tQixFQUFFOzs7Ozs7c0NBR2IsOERBQUNFOzRCQUNDSixXQUFVOzRCQUNWK0IseUJBQXlCO2dDQUN2QkMsUUFBUXRELGNBQWNLLE1BQU1KLE9BQU8sSUFBSTs0QkFDekM7NEJBQ0FzRCxPQUFPO2dDQUNMQyxZQUFZO2dDQUNaQyxVQUFVOzRCQUNaOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1aO0dBN0tnQnJEO0tBQUFBIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxjb21wb25lbnRzXFxjb250ZW50LWJsb2NrLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCJcbmltcG9ydCB7IEVkaXQzLCBSZWZyZXNoQ3csIFRyYXNoMiwgRmlsZVRleHQsIEltYWdlSWNvbiwgVXBsb2FkIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgdHlwZSB7IEJsb2dCbG9jayB9IGZyb20gXCJAL3R5cGVzL2FwaVwiXG5cbi8vIEZ1bmN0aW9uIHRvIGZvcm1hdCBjb250ZW50IGFuZCByZW1vdmUgbWFya2Rvd24gZm9ybWF0dGluZ1xuZnVuY3Rpb24gZm9ybWF0Q29udGVudChjb250ZW50OiBzdHJpbmcpOiBzdHJpbmcge1xuICBpZiAoIWNvbnRlbnQpIHJldHVybiAnJztcblxuICByZXR1cm4gY29udGVudFxuICAgIC8vIFJlbW92ZSBtYXJrZG93biBib2xkICgqKnRleHQqKilcbiAgICAucmVwbGFjZSgvXFwqXFwqKC4qPylcXCpcXCovZywgJzxzdHJvbmc+JDE8L3N0cm9uZz4nKVxuICAgIC8vIFJlbW92ZSBtYXJrZG93biBpdGFsaWMgKCp0ZXh0KilcbiAgICAucmVwbGFjZSgvXFwqKC4qPylcXCovZywgJzxlbT4kMTwvZW0+JylcbiAgICAvLyBDb252ZXJ0IGRvdWJsZSBsaW5lIGJyZWFrcyB0byBwYXJhZ3JhcGhzXG4gICAgLnJlcGxhY2UoL1xcblxcbi9nLCAnPC9wPjxwPicpXG4gICAgLy8gQ29udmVydCBzaW5nbGUgbGluZSBicmVha3MgdG8gPGJyPiB0YWdzXG4gICAgLnJlcGxhY2UoL1xcbi9nLCAnPGJyPicpXG4gICAgLy8gUmVtb3ZlIGFueSByZW1haW5pbmcgYXN0ZXJpc2tzXG4gICAgLnJlcGxhY2UoL1xcKi9nLCAnJylcbiAgICAvLyBDbGVhbiB1cCBleHRyYSBzcGFjZXNcbiAgICAucmVwbGFjZSgvXFxzKy9nLCAnICcpXG4gICAgLy8gV3JhcCBpbiBwYXJhZ3JhcGggdGFncyBpZiBub3QgYWxyZWFkeSB3cmFwcGVkXG4gICAgLnJlcGxhY2UoL14oPyE8cD4pLywgJzxwPicpXG4gICAgLnJlcGxhY2UoLyg/ITxcXC9wPikkLywgJzwvcD4nKVxuICAgIC8vIEZpeCBlbXB0eSBwYXJhZ3JhcGhzXG4gICAgLnJlcGxhY2UoLzxwPjxcXC9wPi9nLCAnJylcbiAgICAudHJpbSgpO1xufVxuXG5pbnRlcmZhY2UgQ29udGVudEJsb2NrUHJvcHMge1xuICBibG9jazogQmxvZ0Jsb2NrXG4gIG9uRWRpdDogKCkgPT4gdm9pZFxuICBvblJlZ2VuZXJhdGU6ICgpID0+IHZvaWRcbiAgb25EZWxldGU/OiAoKSA9PiB2b2lkXG4gIG9uSW1hZ2VHZW5lcmF0ZT86IChibG9ja0lkOiBzdHJpbmcsIGRlc2NyaXB0aW9uOiBzdHJpbmcpID0+IHZvaWRcbiAgb25JbWFnZVVwbG9hZD86IChibG9ja0lkOiBzdHJpbmcsIGZpbGU6IEZpbGUpID0+IHZvaWRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENvbnRlbnRCbG9jayh7IGJsb2NrLCBvbkVkaXQsIG9uUmVnZW5lcmF0ZSwgb25EZWxldGUsIG9uSW1hZ2VHZW5lcmF0ZSwgb25JbWFnZVVwbG9hZCB9OiBDb250ZW50QmxvY2tQcm9wcykge1xuICBjb25zdCBbaW1hZ2VEZXNjcmlwdGlvbiwgc2V0SW1hZ2VEZXNjcmlwdGlvbl0gPSBSZWFjdC51c2VTdGF0ZShibG9jay5kZXNjcmlwdGlvbiB8fCAnJylcbiAgY29uc3QgW2FsdFRleHQsIHNldEFsdFRleHRdID0gUmVhY3QudXNlU3RhdGUoYmxvY2suYWx0IHx8ICcnKVxuICBjb25zdCBbaXNHZW5lcmF0aW5nLCBzZXRJc0dlbmVyYXRpbmddID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IGdldEJsb2NrSWNvbiA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKGJsb2NrLnR5cGUpIHtcbiAgICAgIGNhc2UgXCJpbWFnZVwiOlxuICAgICAgICByZXR1cm4gPEltYWdlSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0QmxvY2tUaXRsZSA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKGJsb2NrLnR5cGUpIHtcbiAgICAgIGNhc2UgXCJpbnRyb2R1Y3Rpb25cIjpcbiAgICAgICAgcmV0dXJuIFwiSW50cm9kdWN0aW9uXCJcbiAgICAgIGNhc2UgXCJzZWN0aW9uXCI6XG4gICAgICAgIHJldHVybiBibG9jay5oMiB8fCBcIlNlY3Rpb25cIlxuICAgICAgY2FzZSBcImltYWdlXCI6XG4gICAgICAgIHJldHVybiBgJHtibG9jay5pbWFnZVR5cGUgPT09IFwiZmVhdHVyZVwiID8gXCJGZWF0dXJlXCIgOiBcIkluLWJsb2dcIn0gSW1hZ2VgXG4gICAgICBjYXNlIFwiY29uY2x1c2lvblwiOlxuICAgICAgICByZXR1cm4gXCJDb25jbHVzaW9uXCJcbiAgICAgIGNhc2UgXCJyZWZlcmVuY2VzXCI6XG4gICAgICAgIHJldHVybiBcIlJlZmVyZW5jZXNcIlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIFwiQ29udGVudCBCbG9ja1wiXG4gICAgfVxuICB9XG5cbiAgaWYgKGJsb2NrLnR5cGUgPT09IFwiaW1hZ2VcIikge1xuICAgIHJldHVybiAoXG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJib3JkZXItZGFzaGVkIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB5LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNFwiPlxuICAgICAgICAgICAge2Jsb2NrLmltYWdlVXJsID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgIHNyYz17YmxvY2suaW1hZ2VVcmx9XG4gICAgICAgICAgICAgICAgICBhbHQ9e2Jsb2NrLmFsdCB8fCAnR2VuZXJhdGVkIGltYWdlJ31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm14LWF1dG8gbWF4LXctZnVsbCBoLTQ4IG9iamVjdC1jb3ZlciByb3VuZGVkLWxnIGJvcmRlclwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57Z2V0QmxvY2tUaXRsZSgpfTwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPntibG9jay5kZXNjcmlwdGlvbiB8fCBibG9jay5hbHR9PC9wPlxuICAgICAgICAgICAgICAgICAge2Jsb2NrLmVuaGFuY2VkUHJvbXB0ICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG10LTJcIj5BSSBQcm9tcHQ6IHtibG9jay5lbmhhbmNlZFByb21wdH08L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIHctMTYgaC0xNiBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VJY29uIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57Z2V0QmxvY2tUaXRsZSgpfTwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlVwbG9hZCBhbiBpbWFnZSBvciBnZW5lcmF0ZSB3aXRoIEFJPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQWx0IHRleHQgKHJlcXVpcmVkKVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2FsdFRleHR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBbHRUZXh0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJJbWFnZSBkZXNjcmlwdGlvbiBmb3IgQUkgZ2VuZXJhdGlvblwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2ltYWdlRGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRJbWFnZURlc2NyaXB0aW9uKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1tZCBteC1hdXRvXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICAgICAgICAgICAgICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgICAgICAgICAgICAgICBpbnB1dC5hY2NlcHQgPSAnaW1hZ2UvKic7XG4gICAgICAgICAgICAgICAgICBpbnB1dC5vbmNoYW5nZSA9IChlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSAoZS50YXJnZXQgYXMgSFRNTElucHV0RWxlbWVudCkuZmlsZXM/LlswXTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUgJiYgb25JbWFnZVVwbG9hZCkge1xuICAgICAgICAgICAgICAgICAgICAgIG9uSW1hZ2VVcGxvYWQoYmxvY2suaWQsIGZpbGUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgaW5wdXQuY2xpY2soKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgIFVwbG9hZCBJbWFnZVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshaW1hZ2VEZXNjcmlwdGlvbiB8fCBpc0dlbmVyYXRpbmd9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKGltYWdlRGVzY3JpcHRpb24gJiYgb25JbWFnZUdlbmVyYXRlKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldElzR2VuZXJhdGluZyh0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgb25JbWFnZUdlbmVyYXRlKGJsb2NrLmlkLCBpbWFnZURlc2NyaXB0aW9uKTtcbiAgICAgICAgICAgICAgICAgICAgLy8gUmVzZXQgZ2VuZXJhdGluZyBzdGF0ZSBhZnRlciBhIGRlbGF5ICh3aWxsIGJlIGhhbmRsZWQgYnkgcGFyZW50KVxuICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHNldElzR2VuZXJhdGluZyhmYWxzZSksIDMwMDApO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aXNHZW5lcmF0aW5nID8gXCJHZW5lcmF0aW5nLi4uXCIgOiBcIkdlbmVyYXRlIHdpdGggQUlcIn1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxDYXJkIGNsYXNzTmFtZT1cImhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvd1wiPlxuICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItM1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIHtnZXRCbG9ja0ljb24oKX1cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPntnZXRCbG9ja1RpdGxlKCl9PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICB7YmxvY2sud29yZENvdW50ICYmIChcbiAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgIHtibG9jay53b3JkQ291bnR9IHdvcmRzXG4gICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtvbkVkaXR9IHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICA8RWRpdDMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtvblJlZ2VuZXJhdGV9IHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIHtvbkRlbGV0ZSAmJiAoXG4gICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17b25EZWxldGV9IHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9DYXJkSGVhZGVyPlxuXG4gICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvc2UgcHJvc2UtbGcgbWF4LXctbm9uZVwiPlxuICAgICAgICAgIHtibG9jay5oMiAmJiAoXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNCBsZWFkaW5nLXRpZ2h0IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBwYi0yXCI+XG4gICAgICAgICAgICAgIHtibG9jay5oMn1cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwIGxlYWRpbmctcmVsYXhlZCB0ZXh0LWJhc2UgYmxvZy1jb250ZW50XCJcbiAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XG4gICAgICAgICAgICAgIF9faHRtbDogZm9ybWF0Q29udGVudChibG9jay5jb250ZW50IHx8ICcnKVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjcnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJCYWRnZSIsIklucHV0IiwiRWRpdDMiLCJSZWZyZXNoQ3ciLCJUcmFzaDIiLCJGaWxlVGV4dCIsIkltYWdlSWNvbiIsIlVwbG9hZCIsImZvcm1hdENvbnRlbnQiLCJjb250ZW50IiwicmVwbGFjZSIsInRyaW0iLCJDb250ZW50QmxvY2siLCJibG9jayIsIm9uRWRpdCIsIm9uUmVnZW5lcmF0ZSIsIm9uRGVsZXRlIiwib25JbWFnZUdlbmVyYXRlIiwib25JbWFnZVVwbG9hZCIsImltYWdlRGVzY3JpcHRpb24iLCJzZXRJbWFnZURlc2NyaXB0aW9uIiwidXNlU3RhdGUiLCJkZXNjcmlwdGlvbiIsImFsdFRleHQiLCJzZXRBbHRUZXh0IiwiYWx0IiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwiZ2V0QmxvY2tJY29uIiwidHlwZSIsImNsYXNzTmFtZSIsImdldEJsb2NrVGl0bGUiLCJoMiIsImltYWdlVHlwZSIsImRpdiIsImltYWdlVXJsIiwiaW1nIiwic3JjIiwiaDMiLCJwIiwiZW5oYW5jZWRQcm9tcHQiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwiaW5wdXQiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJhY2NlcHQiLCJvbmNoYW5nZSIsImZpbGUiLCJmaWxlcyIsImlkIiwiY2xpY2siLCJkaXNhYmxlZCIsInNldFRpbWVvdXQiLCJ3b3JkQ291bnQiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsInN0eWxlIiwibGluZUhlaWdodCIsImZvbnRTaXplIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/content-block.tsx\n"));

/***/ })

});