{"name": "Workflow 2 (company sync)", "nodes": [{"parameters": {"documentId": {"__rl": true, "value": "1F6afV2T3QxBQHrLfrbwqmR0a8Aag3YqCDZkvwwQf1R4", "mode": "list", "cachedResultName": "Company KT Sheet"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Wattmonk KT"}, "options": {}}, "id": "company-reader-001", "name": "Read Company Data", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [200, 0], "credentials": {"googleSheetsOAuth2Api": {"id": "TXBfVPVDwrhp999t", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Transform company data for ArticleScribe API\nconst items = $input.all();\nconst companies = [];\n\nconsole.log(`Processing ${items.length} company records`);\n\nfor (let i = 0; i < items.length; i++) {\n  const item = items[i].json;\n  \n  // Skip header row or empty rows\n  if (!item['Company Name'] || item['Company Name'] === 'Company Name') {\n    continue;\n  }\n  \n  const company = {\n    id: `company_${i + 1}`,\n    companyName: item['Company Name'] || '',\n    servicesOffered: item['Services Offered '] || '',\n    serviceOverview: item['Service Overview'] || '',\n    aboutTheCompany: item['About The Company'] || '',\n    tone: item['Tone'] || 'Professional',\n    brandVoice: item['Brand Voice'] || 'Expert',\n    targetMarket: item['Target Market'] || 'Solar Industry',\n    keyStrengths: item['Key Strengths'] || '',\n    competitiveAdvantage: item['Competitive Advantage'] || '',\n    \n    // Metadata\n    lastUpdated: new Date().toISOString(),\n    syncSource: 'n8n_workflow_2',\n    sheetRowIndex: i + 1\n  };\n  \n  companies.push(company);\n}\n\nconsole.log(`Transformed ${companies.length} companies for sync`);\n\nreturn [{ json: { companies, totalCount: companies.length, syncTimestamp: new Date().toISOString() } }];"}, "id": "transform-companies-001", "name": "Transform Company Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 0]}, {"parameters": {"url": "http://localhost:5000/api/n8n/sync/companies", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify($json) }}", "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}, "timeout": 30000}}, "id": "sync-to-articlescribe-001", "name": "Sync to ArticleScribe", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 0]}, {"parameters": {"jsCode": "// Log sync results and handle any errors\nconst items = $input.all();\nconst results = [];\n\nfor (const item of items) {\n  const response = item.json;\n  \n  console.log('ArticleScribe Sync Response:', JSON.stringify(response, null, 2));\n  \n  const result = {\n    syncStatus: response.success ? 'success' : 'failed',\n    companiesSynced: response.synced || 0,\n    message: response.message || 'No message',\n    timestamp: new Date().toISOString(),\n    workflowName: 'Company Sync Workflow 2'\n  };\n  \n  if (!response.success) {\n    console.error('Sync failed:', response.error || 'Unknown error');\n    result.error = response.error || 'Unknown error';\n  } else {\n    console.log(`✓ Successfully synced ${response.synced} companies`);\n  }\n  \n  results.push(result);\n}\n\nreturn results.map(r => ({ json: r }));"}, "id": "log-results-001", "name": "Log Sync Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 0]}, {"parameters": {"path": "company-sync-webhook", "options": {}}, "id": "webhook-trigger-001", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "webhookId": "company-sync-webhook-001"}, {"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "schedule-trigger-001", "name": "Schedule Every 6 Hours", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 200]}], "connections": {"Read Company Data": {"main": [[{"node": "Transform Company Data", "type": "main", "index": 0}]]}, "Transform Company Data": {"main": [[{"node": "Sync to ArticleScribe", "type": "main", "index": 0}]]}, "Sync to ArticleScribe": {"main": [[{"node": "Log Sync Results", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Read Company Data", "type": "main", "index": 0}]]}, "Schedule Every 6 Hours": {"main": [[{"node": "Read Company Data", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "meta": {"instanceId": "bd13a791ef83e2ae6563714d8025574c31f1bfadf3ea50d267483e93fe42f56c"}, "tags": ["articlescribe", "company-sync", "real-time"]}