class MetaService {
  async generateScoredMetaOptions(keyword, keywordCluster, companyData, trends) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    const prompt = `
      Generate 3 SEO-optimized H1 + Meta Title + Meta Description options for:
      Primary Keyword: ${keyword}
      Secondary Keywords: ${keywordCluster.secondaryKeywords.map(k => k.keyword).join(', ')}
      LSI Keywords: ${keywordCluster.lsiKeywords.join(', ')}
      Company: ${companyData.companyName}
      Current Trends: ${trends.currentTrends.join(', ')}
      Year: ${currentYear}
      
      For each option, calculate SEO scores based on:
      - Keyword inclusion (40 points)
      - Length optimization (20 points)
      - Readability (20 points)
      - Trend relevance (20 points)
      
      Return in JSON format:
      {
        "options": [
          {
            "h1Title": "title here",
            "metaTitle": "meta title here",
            "metaDescription": "description here",
            "scores": {
              "keywordScore": 0-40,
              "lengthScore": 0-20,
              "readabilityScore": 0-20,
              "trendScore": 0-20,
              "totalScore": 0-100
            },
            "keywordsIncluded": ["keyword1", "keyword2"]
          }
        ]
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed.options;
      }
    } catch (parseError) {
      console.error('Meta options parsing error:', parseError);
    }

    // Fallback meta options with scores
    return [
      {
        h1Title: `${keyword} - Complete Guide ${currentYear}`,
        metaTitle: `${keyword} Guide ${currentYear} | ${companyData.companyName}`,
        metaDescription: `Master ${keyword} with our expert guide. Learn best practices, cost analysis, and implementation strategies from ${companyData.companyName} professionals.`,
        scores: {
          keywordScore: 35,
          lengthScore: 18,
          readabilityScore: 17,
          trendScore: 15,
          totalScore: 85
        },
        keywordsIncluded: [keyword, "guide", currentYear.toString()]
      },
      {
        h1Title: `Professional ${keyword} Services & Solutions`,
        metaTitle: `${keyword} Services | Expert Solar Solutions`,
        metaDescription: `Professional ${keyword} services by ${companyData.companyName}. Trusted by installers nationwide for reliable, efficient solar solutions and expert support.`,
        scores: {
          keywordScore: 38,
          lengthScore: 19,
          readabilityScore: 18,
          trendScore: 17,
          totalScore: 92
        },
        keywordsIncluded: [keyword, "services", "solutions", "professional"]
      },
      {
        h1Title: `${keyword}: Essential Tips & Best Practices`,
        metaTitle: `${keyword} Tips & Best Practices ${currentYear}`,
        metaDescription: `Discover essential ${keyword} tips and industry best practices. ${companyData.companyName} shares proven strategies for successful solar projects in ${currentYear}.`,
        scores: {
          keywordScore: 36,
          lengthScore: 17,
          readabilityScore: 19,
          trendScore: 16,
          totalScore: 88
        },
        keywordsIncluded: [keyword, "tips", "best practices", currentYear.toString()]
      }
    ];
  }

  async generateMetaOptions(keyword, companyData, competitorAnalysis) {
    // For now, use the existing method with mock data
    const mockKeywordCluster = {
      secondaryKeywords: [
        { keyword: 'solar installation', relevance: 0.8 },
        { keyword: 'solar design', relevance: 0.7 }
      ],
      lsiKeywords: ['solar panels', 'renewable energy', 'solar system']
    };

    const mockTrends = {
      currentTrends: ['sustainability', 'clean energy', 'cost savings']
    };

    return await this.generateScoredMetaOptions(keyword, mockKeywordCluster, companyData, mockTrends);
  }

  async regenerateMetaOptions(keyword, companyData, competitorAnalysis, optionIndex) {
    // Generate new meta options
    const newOptions = await this.generateMetaOptions(keyword, companyData, competitorAnalysis);

    // If optionIndex is provided, return only that option, otherwise return all
    if (optionIndex !== undefined) {
      return [newOptions[0]]; // Return the first new option to replace the specified index
    }

    return newOptions;
  }
}

module.exports = new MetaService();
