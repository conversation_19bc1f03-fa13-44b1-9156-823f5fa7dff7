"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { ChevronDown, Target, Users, FileText, TrendingUp, Search } from "lucide-react"
import type { Keyword, CompetitorAnalysis, KeywordCluster, TrendAnalysis, Draft } from "@/types/api"
import { StepperHeader } from "@/components/stepper-header"
import { api } from "@/lib/api"

export default function KeywordsPage() {
  const [draft, setDraft] = useState<Draft | null>(null)
  const [keywords, setKeywords] = useState<Keyword[]>([])
  const [selectedKeyword, setSelectedKeyword] = useState<string>("")
  const [analysis, setAnalysis] = useState<{
    competitors: CompetitorAnalysis[]
    cluster: KeywordCluster[]
    trends: TrendAnalysis[]
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [analyzing, setAnalyzing] = useState(false)
  const [showAnalysis, setShowAnalysis] = useState(false)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  const draftId = params.draftId as string

  useEffect(() => {
    loadDraftData()
  }, [draftId])

  const loadDraftData = async () => {
    try {
      // Load draft data from backend
      const draftResponse = await api.getDraft(draftId)

      if (draftResponse.success && draftResponse.data) {
        const draftData = draftResponse.data
        setDraft(draftData)

        // Set keywords from draft's keyword suggestions (2 manual + 2 automated)
        if (draftData.keywordSuggestions && draftData.keywordSuggestions.total) {
          setKeywords(draftData.keywordSuggestions.total)
        }

        // If keyword is already selected and analysis exists, show it
        if (draftData.selectedKeyword) {
          setSelectedKeyword(draftData.selectedKeyword)
          if (draftData.competitorAnalysis && draftData.keywordCluster && draftData.trends) {
            setAnalysis({
              competitors: draftData.competitorAnalysis,
              cluster: draftData.keywordCluster,
              trends: draftData.trends
            })
            setShowAnalysis(true)
          }
        }
      }
    } catch (error) {
      toast({
        title: "Error loading draft",
        description: "Failed to load draft data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAnalyzeKeyword = async () => {
    if (!selectedKeyword) return

    setAnalyzing(true)
    try {
      // Call backend API to analyze keyword
      const response = await api.selectKeywordAnalyze(draftId, selectedKeyword)

      if (response.success) {
        setAnalysis({
          competitors: response.competitorAnalysis,
          cluster: response.keywordCluster,
          trends: response.trends
        })
        setShowAnalysis(true)
        toast({
          title: "Analysis complete",
          description: "Keyword analysis has been generated successfully.",
        })
      }
    } catch (error) {
      toast({
        title: "Analysis failed",
        description: "Failed to analyze keyword. Please try again.",
        variant: "destructive",
      })
    } finally {
      setAnalyzing(false)
    }
  }

  const handleContinue = async () => {
    if (selectedKeyword && analysis) {
      try {
        // Save the current state before navigating
        await api.saveDraft(draftId, {
          selectedKeyword,
          competitorAnalysis: analysis.competitors,
          keywordCluster: analysis.cluster,
          trends: analysis.trends,
          status: 'meta_generation'
        })

        router.push(`/blog/${draftId}/meta`)
      } catch (error) {
        toast({
          title: "Error saving progress",
          description: "Failed to save keyword selection. Please try again.",
          variant: "destructive",
        })
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={1} draftId={draftId} />
        <main className="max-w-7xl mx-auto px-6 py-8">
          <div className="space-y-6">
            <Skeleton className="h-8 w-64" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-48" />
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StepperHeader currentStep={1} draftId={draftId} />

      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Select Focus Keyword</h1>
            <p className="text-gray-600">Choose the primary keyword for your blog post</p>
          </div>

          <RadioGroup value={selectedKeyword} onValueChange={setSelectedKeyword}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {keywords.map((keyword, index) => (
                <div key={index} className="relative">
                  <Label htmlFor={`keyword-${index}`} className="cursor-pointer">
                    <Card
                      className={`hover:shadow-md transition-all duration-200 ${
                        selectedKeyword === keyword.focusKeyword
                          ? "ring-2 ring-[#0066cc] border-[#0066cc]"
                          : "hover:border-gray-300"
                      }`}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg font-semibold text-gray-900">
                              {keyword.focusKeyword}
                            </CardTitle>
                            <div className="flex gap-2 mt-2">
                              <Badge variant="outline">{keyword.articleFormat}</Badge>
                              <Badge variant={keyword.source === "ai" ? "default" : "secondary"}>
                                {keyword.source === "manual" ? "Manual" : "AI Generated"}
                              </Badge>
                            </div>
                          </div>
                          <RadioGroupItem value={keyword.focusKeyword} id={`keyword-${index}`} className="mt-1" />
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-gray-500" />
                            <span>{keyword.wordCount} words</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-gray-500" />
                            <span>{keyword.targetAudience}</span>
                          </div>
                        </div>
                        <div className="flex items-start gap-2">
                          <Target className="h-4 w-4 text-gray-500 mt-0.5" />
                          <span className="text-sm text-gray-600">{keyword.objective}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>

          <div className="flex gap-4">
            <Button
              onClick={handleAnalyzeKeyword}
              disabled={!selectedKeyword || analyzing}
              className="bg-[#0066cc] hover:bg-blue-700"
            >
              {analyzing ? "Analyzing..." : "Analyze Keyword"}
            </Button>

            {analysis && (
              <Button onClick={handleContinue} className="bg-[#00aa66] hover:bg-green-700">
                Continue to Meta Generation
              </Button>
            )}
          </div>

          {/* Analysis Results */}
          {showAnalysis && analysis && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Keyword Analysis Results</h2>

              {/* Competitor Analysis */}
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" className="w-full justify-between bg-transparent">
                    <span className="flex items-center gap-2">
                      <Search className="h-4 w-4" />
                      Competitor Analysis
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <Card>
                    <CardContent className="pt-4">
                      <div className="space-y-4">
                        {analysis.competitors.map((competitor, index) => (
                          <div key={index} className="border-b pb-3 last:border-b-0">
                            <h4 className="font-medium">{competitor.domain}</h4>
                            <p className="text-sm text-gray-600 mt-1">{competitor.title}</p>
                            <div className="flex gap-4 mt-2 text-xs text-gray-500">
                              <span>DA: {competitor.domainAuthority}</span>
                              <span>Words: {competitor.wordCount}</span>
                              <span>Score: {competitor.seoScore}/100</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </CollapsibleContent>
              </Collapsible>

              {/* Keyword Cluster */}
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" className="w-full justify-between bg-transparent">
                    <span className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Keyword Cluster
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <Card>
                    <CardContent className="pt-4">
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-2">Keyword</th>
                              <th className="text-left py-2">Volume</th>
                              <th className="text-left py-2">Difficulty</th>
                              <th className="text-left py-2">Relevance</th>
                            </tr>
                          </thead>
                          <tbody>
                            {analysis.cluster.map((item, index) => (
                              <tr key={index} className="border-b">
                                <td className="py-2">{item.keyword}</td>
                                <td className="py-2">{item.searchVolume}</td>
                                <td className="py-2">
                                  <Badge
                                    variant={
                                      item.difficulty < 30
                                        ? "default"
                                        : item.difficulty < 60
                                          ? "secondary"
                                          : "destructive"
                                    }
                                  >
                                    {item.difficulty}
                                  </Badge>
                                </td>
                                <td className="py-2">{item.relevanceScore}/100</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </CollapsibleContent>
              </Collapsible>

              {/* Trends Analysis */}
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" className="w-full justify-between bg-transparent">
                    <span className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Trends Analysis
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {analysis.trends.map((trend, index) => (
                      <Card key={index}>
                        <CardContent className="pt-4">
                          <div className="flex items-center gap-2 mb-2">
                            <TrendingUp
                              className={`h-4 w-4 ${trend.direction === "up" ? "text-green-500" : "text-red-500"}`}
                            />
                            <span className="font-medium">{trend.topic}</span>
                          </div>
                          <p className="text-sm text-gray-600">{trend.description}</p>
                          <Badge variant="outline" className="mt-2">
                            {trend.confidence}% confidence
                          </Badge>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
