{"name": "Workflow 4 (content generation)", "nodes": [{"parameters": {"path": "generate-content", "options": {}}, "id": "content-webhook-001", "name": "Content Generation Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "webhookId": "content-generation-webhook-001"}, {"parameters": {"jsCode": "// Process content generation request from ArticleScribe\nconst items = $input.all();\nconst requests = [];\n\nfor (const item of items) {\n  const data = item.json;\n  \n  console.log('Content generation request:', JSON.stringify(data, null, 2));\n  \n  const request = {\n    draftId: data.draftId || '',\n    keyword: data.keyword || '',\n    companyData: data.companyData || {},\n    contentType: data.contentType || 'full_blog',\n    \n    // Extract company info\n    companyName: data.companyData?.companyName || 'WattMonk',\n    services: data.companyData?.servicesOffered || 'Solar Services',\n    tone: data.companyData?.tone || 'Professional',\n    brandVoice: data.companyData?.brandVoice || 'Expert',\n    \n    // Generation parameters\n    wordCount: data.wordCount || 2000,\n    targetAudience: data.targetAudience || 'Solar Professionals',\n    articleFormat: data.articleFormat || 'Complete Guide',\n    \n    // Metadata\n    requestTimestamp: new Date().toISOString(),\n    requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`\n  };\n  \n  requests.push(request);\n}\n\nconsole.log(`Processing ${requests.length} content generation requests`);\n\nreturn requests.map(r => ({ json: r }));"}, "id": "process-request-001", "name": "Process Request", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [200, 0]}, {"parameters": {"url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "authentication": "predefinedCredentialType", "nodeCredentialType": "googlePalmApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"You are a professional solar industry content writer. Generate a complete, detailed blog post based on these requirements:\\n\\nKeyword: {{ $json.keyword }}\\nCompany: {{ $json.companyName }}\\nServices: {{ $json.services }}\\nTone: {{ $json.tone }}\\nBrand Voice: {{ $json.brandVoice }}\\nWord Count: {{ $json.wordCount }}\\nTarget Audience: {{ $json.targetAudience }}\\nArticle Format: {{ $json.articleFormat }}\\n\\nGenerate a comprehensive blog post that includes:\\n\\n1. SEO-optimized H1 title with the focus keyword\\n2. Meta title (60 chars max)\\n3. Meta description (160 chars max)\\n4. Introduction paragraph\\n5. 5-7 H2 sections with detailed content\\n6. Feature image description\\n7. 2 in-blog image descriptions\\n8. Conclusion\\n9. Call-to-action\\n\\nReturn the response in this exact JSON format:\\n\\n{\\n  \\\"h1Title\\\": \\\"[SEO title with keyword]\\\",\\n  \\\"metaTitle\\\": \\\"[60 char meta title]\\\",\\n  \\\"metaDescription\\\": \\\"[160 char meta description]\\\",\\n  \\\"introduction\\\": \\\"[Full introduction paragraph]\\\",\\n  \\\"sections\\\": [\\n    {\\n      \\\"h2\\\": \\\"[H2 heading]\\\",\\n      \\\"content\\\": \\\"[Full section content]\\\",\\n      \\\"targetKeyword\\\": \\\"[section keyword]\\\"\\n    }\\n  ],\\n  \\\"featureImage\\\": {\\n    \\\"alt\\\": \\\"[Alt text]\\\",\\n    \\\"title\\\": \\\"[Image title]\\\",\\n    \\\"description\\\": \\\"[What image should show]\\\"\\n  },\\n  \\\"inBlogImages\\\": [\\n    {\\n      \\\"alt\\\": \\\"[Alt text 1]\\\",\\n      \\\"title\\\": \\\"[Image title 1]\\\",\\n      \\\"description\\\": \\\"[Image description 1]\\\"\\n    },\\n    {\\n      \\\"alt\\\": \\\"[Alt text 2]\\\",\\n      \\\"title\\\": \\\"[Image title 2]\\\",\\n      \\\"description\\\": \\\"[Image description 2]\\\"\\n    }\\n  ],\\n  \\\"conclusion\\\": \\\"[Full conclusion paragraph]\\\",\\n  \\\"callToAction\\\": \\\"[CTA for {{ $json.companyName }}]\\\"\\n}\\n\\nEnsure all content is professional, informative, and optimized for the target audience. Include specific technical details and actionable insights.\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.7,\n    \"topK\": 40,\n    \"topP\": 0.95,\n    \"maxOutputTokens\": 4096\n  }\n}", "options": {"response": {"response": {"neverError": true}}}}, "id": "generate-content-001", "name": "Generate Content with Gemini", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 0], "credentials": {"googlePalmApi": {"id": "mfmYziwwRaDXEvbP", "name": "Google Gemini(PaLM) Api account 2"}}}, {"parameters": {"jsCode": "// Process Gemini response and structure content into blocks\nconst items = $input.all();\nconst processedContent = [];\n\nfor (let i = 0; i < items.length; i++) {\n  const item = items[i];\n  const originalRequest = $('Process Request').item(i).json;\n  \n  console.log(`Processing content response ${i + 1}`);\n  \n  let content = null;\n  let blocks = [];\n  \n  try {\n    // Extract content from Gemini response\n    const geminiResponse = item.json;\n    \n    if (geminiResponse?.candidates?.[0]?.content?.parts?.[0]?.text) {\n      let responseText = geminiResponse.candidates[0].content.parts[0].text;\n      \n      // Clean and parse JSON\n      responseText = responseText\n        .replace(/```json\\s*/gi, '')\n        .replace(/```\\s*/g, '')\n        .replace(/^[^{]*/, '')\n        .replace(/[^}]*$/, '}')\n        .trim();\n      \n      try {\n        content = JSON.parse(responseText);\n        console.log(`✓ Successfully parsed content for ${originalRequest.keyword}`);\n      } catch (parseError) {\n        console.error('JSON parsing failed:', parseError.message);\n        content = createFallbackContent(originalRequest);\n      }\n    } else {\n      console.warn('No valid Gemini response, creating fallback');\n      content = createFallbackContent(originalRequest);\n    }\n    \n    // Structure content into blocks for ArticleScribe\n    blocks = structureContentAsBlocks(content, originalRequest);\n    \n  } catch (error) {\n    console.error(`Error processing content ${i + 1}:`, error);\n    content = createFallbackContent(originalRequest);\n    blocks = structureContentAsBlocks(content, originalRequest);\n  }\n  \n  const result = {\n    draftId: originalRequest.draftId,\n    content: content,\n    blocks: blocks,\n    images: [\n      content.featureImage,\n      ...content.inBlogImages\n    ],\n    citations: generateCitations(originalRequest.keyword),\n    \n    // Metadata\n    generatedAt: new Date().toISOString(),\n    requestId: originalRequest.requestId,\n    keyword: originalRequest.keyword,\n    companyName: originalRequest.companyName,\n    wordCount: calculateWordCount(content),\n    blockCount: blocks.length\n  };\n  \n  processedContent.push(result);\n}\n\n// Helper functions\nfunction createFallbackContent(request) {\n  return {\n    h1Title: `Complete Guide to ${request.keyword}`,\n    metaTitle: `${request.keyword} | ${request.companyName}`,\n    metaDescription: `Learn about ${request.keyword} with ${request.companyName}. Professional insights and expert guidance.`,\n    introduction: `Understanding ${request.keyword} is crucial for solar professionals. This comprehensive guide covers everything you need to know.`,\n    sections: [\n      {\n        h2: `Understanding ${request.keyword}`,\n        content: `This section provides a comprehensive overview of ${request.keyword} and its importance in the solar industry.`,\n        targetKeyword: request.keyword\n      },\n      {\n        h2: `Best Practices for ${request.keyword}`,\n        content: `Learn the industry best practices and proven strategies for implementing ${request.keyword} effectively.`,\n        targetKeyword: request.keyword\n      }\n    ],\n    featureImage: {\n      alt: `${request.keyword} guide`,\n      title: `Professional ${request.keyword} Guide`,\n      description: `Comprehensive visual guide showing ${request.keyword} implementation`\n    },\n    inBlogImages: [\n      {\n        alt: `${request.keyword} example 1`,\n        title: `${request.keyword} Case Study`,\n        description: `Real-world example of ${request.keyword} implementation`\n      },\n      {\n        alt: `${request.keyword} example 2`,\n        title: `${request.keyword} Best Practices`,\n        description: `Visual representation of ${request.keyword} best practices`\n      }\n    ],\n    conclusion: `In conclusion, ${request.keyword} is essential for modern solar operations. Contact ${request.companyName} for expert guidance.`,\n    callToAction: `Ready to implement ${request.keyword}? Contact ${request.companyName} today for professional consultation.`\n  };\n}\n\nfunction structureContentAsBlocks(content, request) {\n  const blocks = [];\n  let blockIndex = 0;\n  \n  // H1 and Meta blocks\n  blocks.push({\n    id: `block_${blockIndex++}`,\n    type: 'h1',\n    content: content.h1Title,\n    editable: true\n  });\n  \n  blocks.push({\n    id: `block_${blockIndex++}`,\n    type: 'meta',\n    metaTitle: content.metaTitle,\n    metaDescription: content.metaDescription,\n    editable: true\n  });\n  \n  // Introduction block\n  blocks.push({\n    id: `block_${blockIndex++}`,\n    type: 'introduction',\n    content: content.introduction,\n    editable: true,\n    wordCount: content.introduction.split(' ').length\n  });\n  \n  // Feature image block\n  blocks.push({\n    id: `block_${blockIndex++}`,\n    type: 'image',\n    imageType: 'feature',\n    ...content.featureImage,\n    editable: true\n  });\n  \n  // Section blocks with images\n  content.sections.forEach((section, sectionIndex) => {\n    blocks.push({\n      id: `block_${blockIndex++}`,\n      type: 'section',\n      h2: section.h2,\n      content: section.content,\n      targetKeyword: section.targetKeyword,\n      editable: true,\n      sectionNumber: sectionIndex + 1,\n      wordCount: section.content.split(' ').length\n    });\n    \n    // Add in-blog images after specific sections\n    if (sectionIndex === 1 && content.inBlogImages[0]) {\n      blocks.push({\n        id: `block_${blockIndex++}`,\n        type: 'image',\n        imageType: 'in-blog',\n        ...content.inBlogImages[0],\n        editable: true\n      });\n    } else if (sectionIndex === 3 && content.inBlogImages[1]) {\n      blocks.push({\n        id: `block_${blockIndex++}`,\n        type: 'image',\n        imageType: 'in-blog',\n        ...content.inBlogImages[1],\n        editable: true\n      });\n    }\n  });\n  \n  // Conclusion block\n  blocks.push({\n    id: `block_${blockIndex++}`,\n    type: 'conclusion',\n    content: content.conclusion,\n    editable: true,\n    wordCount: content.conclusion.split(' ').length\n  });\n  \n  // Call to action block\n  blocks.push({\n    id: `block_${blockIndex++}`,\n    type: 'cta',\n    content: content.callToAction,\n    editable: true\n  });\n  \n  return blocks;\n}\n\nfunction generateCitations(keyword) {\n  return [\n    {\n      id: 'citation_1',\n      title: `Solar Industry Report on ${keyword}`,\n      url: 'https://www.seia.org/research-resources',\n      source: 'Solar Energy Industries Association'\n    },\n    {\n      id: 'citation_2',\n      title: `Technical Standards for ${keyword}`,\n      url: 'https://www.nrel.gov/',\n      source: 'National Renewable Energy Laboratory'\n    }\n  ];\n}\n\nfunction calculateWordCount(content) {\n  let totalWords = 0;\n  totalWords += content.introduction.split(' ').length;\n  content.sections.forEach(section => {\n    totalWords += section.content.split(' ').length;\n  });\n  totalWords += content.conclusion.split(' ').length;\n  return totalWords;\n}\n\nconsole.log(`✓ Processed ${processedContent.length} content pieces`);\n\nreturn processedContent.map(c => ({ json: c }));"}, "id": "structure-content-001", "name": "Structure Content into Blocks", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 0]}, {"parameters": {"url": "http://localhost:5000/api/n8n/content-callback", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify($json) }}", "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}, "timeout": 30000}}, "id": "callback-articlescribe-001", "name": "Callback to ArticleScribe", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 0]}], "connections": {"Content Generation Webhook": {"main": [[{"node": "Process Request", "type": "main", "index": 0}]]}, "Process Request": {"main": [[{"node": "Generate Content with Gemini", "type": "main", "index": 0}]]}, "Generate Content with Gemini": {"main": [[{"node": "Structure Content into Blocks", "type": "main", "index": 0}]]}, "Structure Content into Blocks": {"main": [[{"node": "Callback to ArticleScribe", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "meta": {"instanceId": "bd13a791ef83e2ae6563714d8025574c31f1bfadf3ea50d267483e93fe42f56c"}, "tags": ["articlescribe", "content-generation", "webhook"]}