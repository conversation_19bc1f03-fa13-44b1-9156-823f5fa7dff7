// Draft Controller - Handles draft management operations
const draftService = require('../services/draft.service');

class DraftController {
  // Get draft by ID
  async getDraft(req, res) {
    try {
      const { draftId } = req.params;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      res.json({
        success: true,
        draft
      });

    } catch (error) {
      console.error('Error getting draft:', error);
      res.status(500).json({ error: 'Failed to get draft' });
    }
  }

  // List all drafts for a user
  async listDrafts(req, res) {
    try {
      const { userId, status, limit, offset } = req.query;
      
      const drafts = await draftService.listDrafts(userId || 'anonymous', {
        status,
        limit: limit ? parseInt(limit) : 50,
        offset: offset ? parseInt(offset) : 0
      });

      res.json({
        success: true,
        drafts
      });

    } catch (error) {
      console.error('Error listing drafts:', error);
      res.status(500).json({ error: 'Failed to list drafts' });
    }
  }

  // Save draft manually
  async saveDraft(req, res) {
    try {
      const { draftId, updates } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      await draftService.updateDraft(draftId, updates);

      res.json({
        success: true,
        message: 'Draft saved successfully'
      });

    } catch (error) {
      console.error('Error saving draft:', error);
      res.status(500).json({ error: 'Failed to save draft' });
    }
  }

  // Create a new draft
  async createDraft(req, res) {
    try {
      const { userId, companyData, keywordSuggestions, initialData } = req.body;
      
      if (!companyData) {
        return res.status(400).json({ error: 'Company data is required' });
      }

      const draft = await draftService.createDraft({
        userId: userId || 'anonymous',
        companyData,
        keywordSuggestions,
        status: 'keyword_selection',
        ...initialData
      });

      res.json({
        success: true,
        draft,
        message: 'Draft created successfully'
      });

    } catch (error) {
      console.error('Error creating draft:', error);
      res.status(500).json({ error: 'Failed to create draft' });
    }
  }

  // Delete a draft
  async deleteDraft(req, res) {
    try {
      const { draftId } = req.params;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      await draftService.deleteDraft(draftId);

      res.json({
        success: true,
        message: 'Draft deleted successfully'
      });

    } catch (error) {
      console.error('Error deleting draft:', error);
      res.status(500).json({ error: 'Failed to delete draft' });
    }
  }

  // Duplicate a draft
  async duplicateDraft(req, res) {
    try {
      const { draftId } = req.params;
      const { userId } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const originalDraft = await draftService.getDraft(draftId);
      if (!originalDraft) {
        return res.status(404).json({ error: 'Original draft not found' });
      }

      // Create a copy of the draft
      const duplicatedDraft = await draftService.createDraft({
        ...originalDraft,
        userId: userId || originalDraft.userId,
        status: 'keyword_selection', // Reset status for new workflow
        createdAt: new Date(),
        updatedAt: new Date()
      });

      res.json({
        success: true,
        draft: duplicatedDraft,
        message: 'Draft duplicated successfully'
      });

    } catch (error) {
      console.error('Error duplicating draft:', error);
      res.status(500).json({ error: 'Failed to duplicate draft' });
    }
  }

  // Get draft statistics
  async getDraftStats(req, res) {
    try {
      const { userId } = req.query;
      
      const stats = await draftService.getDraftStats(userId || 'anonymous');

      res.json({
        success: true,
        stats
      });

    } catch (error) {
      console.error('Error getting draft stats:', error);
      res.status(500).json({ error: 'Failed to get draft statistics' });
    }
  }

  // Archive a draft
  async archiveDraft(req, res) {
    try {
      const { draftId } = req.params;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      await draftService.updateDraft(draftId, {
        status: 'archived',
        archivedAt: new Date()
      });

      res.json({
        success: true,
        message: 'Draft archived successfully'
      });

    } catch (error) {
      console.error('Error archiving draft:', error);
      res.status(500).json({ error: 'Failed to archive draft' });
    }
  }
}

module.exports = new DraftController();
