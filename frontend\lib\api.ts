import type { Company, Draft, StartBlogResponse, KeywordAnalysisResponse, MetaGenerationResponse, ContentGenerationResponse, ApiResponse } from "@/types/api"

const API_BASE_URL = "http://localhost:5000/api"

class ApiClient {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }

    return response.json()
  }

  // Companies
  async getCompanies(): Promise<Company[]> {
    return this.request<Company[]>("/company")
  }

  // Blog workflow
  async startBlog(companyId: string, userId?: string): Promise<StartBlogResponse> {
    return this.request<StartBlogResponse>("/blog/start", {
      method: "POST",
      body: JSON.stringify({ companyId, userId }),
    })
  }

  async selectKeywordAnalyze(draftId: string, selectedKeyword: string, alternativeKeywords?: string[]): Promise<KeywordAnalysisResponse> {
    return this.request<KeywordAnalysisResponse>("/blog/select-keyword-analyze", {
      method: "POST",
      body: JSON.stringify({ draftId, selectedKeyword, alternativeKeywords }),
    })
  }

  async generateMetaScores(draftId: string): Promise<MetaGenerationResponse> {
    return this.request<MetaGenerationResponse>("/blog/generate-meta-scores", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async selectMeta(draftId: string, selectedMetaIndex: number) {
    return this.request("/blog/select-meta", {
      method: "POST",
      body: JSON.stringify({ draftId, selectedMetaIndex }),
    })
  }

  async generateStructuredContent(draftId: string): Promise<ContentGenerationResponse> {
    return this.request<ContentGenerationResponse>("/blog/generate-structured-content", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async regenerateBlock(
    draftId: string,
    blockId: string,
    regenerationType: "ai" | "manual",
    customPrompt?: string,
    newContent?: string,
  ) {
    return this.request("/blog/regenerate-block", {
      method: "POST",
      body: JSON.stringify({ draftId, blockId, regenerationType, customPrompt, newContent }),
    })
  }

  async generateLinks(draftId: string) {
    return this.request("/blog/generate-links", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async deployWordPress(draftId: string, wordpressConfig?: any) {
    return this.request("/blog/deploy-wordpress", {
      method: "POST",
      body: JSON.stringify({ draftId, wordpressConfig }),
    })
  }

  // Draft management
  async getDraft(draftId: string): Promise<ApiResponse<Draft>> {
    return this.request<ApiResponse<Draft>>(`/blog/draft/${draftId}`)
  }

  async listDrafts(userId?: string): Promise<ApiResponse<Draft[]>> {
    const params = userId ? `?userId=${userId}` : ""
    return this.request<ApiResponse<Draft[]>>(`/blog/drafts${params}`)
  }

  // WordPress
  async testWordPress() {
    return this.request("/blog/test-wordpress", {
      method: "POST",
    })
  }

  async previewWordPress(draftId: string) {
    return this.request("/blog/preview-wordpress", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  // Draft management - additional methods
  async deleteDraft(draftId: string) {
    return this.request(`/blog/draft/${draftId}`, {
      method: "DELETE",
    })
  }

  async saveDraft(draftId: string, updates: any) {
    return this.request("/blog/save-draft", {
      method: "POST",
      body: JSON.stringify({ draftId, updates }),
    })
  }

  // WordPress deployment
  async deployToWordPress(draftId: string, credentials: any): Promise<any> {
    return this.request(`/blog/${draftId}/deploy`, {
      method: "POST",
      body: JSON.stringify(credentials),
    })
  }
}

export const api = new ApiClient()
