const citationService = require('./citation.service');

class BlockService {
  structureContentAsBlocks(content, citations) {
    const blocks = [];
    let blockIndex = 0;
    
    // Introduction block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'introduction',
      content: content.introduction,
      editable: true,
      wordCount: content.introduction.split(' ').length
    });
    
    // Feature image block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'image',
      imageType: 'feature',
      ...content.featureImage,
      editable: true
    });
    
    // Process sections with images
    content.sections.forEach((section, sectionIndex) => {
      // Section block
      blocks.push({
        id: `block_${blockIndex++}`,
        type: 'section',
        h2: section.h2,
        content: section.content,
        targetKeyword: section.targetKeyword,
        editable: true,
        sectionNumber: sectionIndex + 1,
        wordCount: section.content.split(' ').length
      });
      
      // Add in-blog images at specified positions
      if (sectionIndex === 1) { // After 2nd section
        blocks.push({
          id: `block_${blockIndex++}`,
          type: 'image',
          imageType: 'in-blog',
          ...content.inBlogImages[0],
          editable: true
        });
      } else if (sectionIndex === 3) { // After 4th section
        blocks.push({
          id: `block_${blockIndex++}`,
          type: 'image',
          imageType: 'in-blog',
          ...content.inBlogImages[1],
          editable: true
        });
      }
    });
    
    // Conclusion block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'conclusion',
      content: content.conclusion,
      editable: true,
      wordCount: content.conclusion.split(' ').length
    });
    
    // References block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'references',
      content: citationService.generateReferenceSection(citations.citations),
      citations: citations.citations,
      editable: false
    });
    
    return blocks;
  }

  async regenerateBlockContent(block, draft, customPrompt) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    let prompt;
    
    switch (block.type) {
      case 'introduction':
        prompt = customPrompt || `Rewrite the introduction for "${draft.finalMeta.h1Title}" about ${draft.selectedKeyword}. Make it engaging, ${block.wordCount || 150}-200 words, include the keyword naturally. Target: ${draft.alignedData.targetAudience}. Year: ${currentYear}`;
        break;
        
      case 'section':
        prompt = customPrompt || `Rewrite this section with H2: "${block.h2}". Topic: ${draft.selectedKeyword}. Target keyword: ${block.targetKeyword}. Make it informative, ${block.wordCount || 300}-400 words. Include data/statistics. Year: ${currentYear}`;
        break;
        
      case 'conclusion':
        prompt = customPrompt || `Rewrite the conclusion for "${draft.finalMeta.h1Title}". Summarize key points, include strong CTA for ${draft.companyData.companyName}. ${block.wordCount || 150}-200 words. Year: ${currentYear}`;
        break;
        
      case 'image':
        prompt = customPrompt || `Generate a new description for a ${block.imageType} image in a blog about ${draft.selectedKeyword}. Make it specific, relevant to solar industry, and SEO-friendly.`;
        const imageResult = await model.generateContent(prompt);
        return {
          ...block,
          description: imageResult.response.text(),
          alt: `${draft.selectedKeyword} - ${block.imageType} image`,
          regeneratedAt: new Date().toISOString()
        };
        
      default:
        prompt = customPrompt || `Improve this content about ${draft.selectedKeyword}`;
    }
    
    const result = await model.generateContent(prompt);
    return result.response.text();
  }

  updateBlockOrder(blocks, blockId, newOrder) {
    const blockIndex = blocks.findIndex(b => b.id === blockId);
    if (blockIndex === -1) return blocks;

    const [movedBlock] = blocks.splice(blockIndex, 1);
    blocks.splice(newOrder, 0, movedBlock);

    // Update order numbers
    return blocks.map((block, index) => ({
      ...block,
      order: index
    }));
  }

  validateBlockStructure(blocks) {
    const errors = [];
    
    // Check for required blocks
    const hasIntroduction = blocks.some(b => b.type === 'introduction');
    const hasConclusion = blocks.some(b => b.type === 'conclusion');
    const hasSections = blocks.some(b => b.type === 'section');
    
    if (!hasIntroduction) errors.push('Missing introduction block');
    if (!hasConclusion) errors.push('Missing conclusion block');
    if (!hasSections) errors.push('Missing content sections');
    
    // Check word counts
    blocks.forEach((block, index) => {
      if (block.type === 'introduction' && block.wordCount < 100) {
        errors.push(`Introduction too short (${block.wordCount} words)`);
      }
      if (block.type === 'section' && block.wordCount < 200) {
        errors.push(`Section ${index + 1} too short (${block.wordCount} words)`);
      }
      if (block.type === 'conclusion' && block.wordCount < 100) {
        errors.push(`Conclusion too short (${block.wordCount} words)`);
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  calculateTotalWordCount(blocks) {
    return blocks
      .filter(b => ['introduction', 'section', 'conclusion'].includes(b.type))
      .reduce((total, block) => total + (block.wordCount || 0), 0);
  }

  getBlocksByType(blocks, type) {
    return blocks.filter(b => b.type === type);
  }

  findBlockById(blocks, blockId) {
    return blocks.find(b => b.id === blockId);
  }

  duplicateBlock(blocks, blockId) {
    const block = this.findBlockById(blocks, blockId);
    if (!block) return blocks;

    const newBlock = {
      ...block,
      id: `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      duplicatedFrom: blockId,
      duplicatedAt: new Date().toISOString()
    };

    const blockIndex = blocks.findIndex(b => b.id === blockId);
    blocks.splice(blockIndex + 1, 0, newBlock);

    return blocks;
  }

  deleteBlock(blocks, blockId) {
    return blocks.filter(b => b.id !== blockId);
  }

  async convertToBlocks(structuredContent) {
    // Convert structured content to block format
    const blocks = [];
    let blockIndex = 0;

    // Introduction block
    if (structuredContent.introduction) {
      blocks.push({
        id: `block_${blockIndex++}`,
        type: 'introduction',
        content: structuredContent.introduction,
        editable: true,
        wordCount: structuredContent.introduction.split(' ').length
      });
    }

    // Feature image block
    if (structuredContent.featureImage) {
      blocks.push({
        id: `block_${blockIndex++}`,
        type: 'image',
        imageType: 'feature',
        ...structuredContent.featureImage,
        editable: true
      });
    }

    // Process sections with images
    if (structuredContent.sections) {
      structuredContent.sections.forEach((section, sectionIndex) => {
        // Section block
        blocks.push({
          id: `block_${blockIndex++}`,
          type: 'section',
          h2: section.h2,
          content: section.content,
          targetKeyword: section.targetKeyword,
          editable: true,
          sectionNumber: sectionIndex + 1,
          wordCount: section.content.split(' ').length
        });

        // Add in-blog images at specified positions
        if (structuredContent.inBlogImages) {
          if (sectionIndex === 1 && structuredContent.inBlogImages[0]) { // After 2nd section
            blocks.push({
              id: `block_${blockIndex++}`,
              type: 'image',
              imageType: 'in-blog',
              ...structuredContent.inBlogImages[0],
              editable: true
            });
          } else if (sectionIndex === 3 && structuredContent.inBlogImages[1]) { // After 4th section
            blocks.push({
              id: `block_${blockIndex++}`,
              type: 'image',
              imageType: 'in-blog',
              ...structuredContent.inBlogImages[1],
              editable: true
            });
          }
        }
      });
    }

    // Conclusion block
    if (structuredContent.conclusion) {
      blocks.push({
        id: `block_${blockIndex++}`,
        type: 'conclusion',
        content: structuredContent.conclusion,
        editable: true,
        wordCount: structuredContent.conclusion.split(' ').length
      });
    }

    return blocks;
  }

  async regenerateBlock(blockType, blockIndex, keyword, companyData, blogBlocks, customPrompt) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();

    const existingBlock = blogBlocks[blockIndex];
    let prompt;

    console.log(`Regenerating block type: ${blockType}, index: ${blockIndex}, customPrompt: ${customPrompt}`);

    // If custom prompt is provided, use it with context
    if (customPrompt) {
      prompt = `${customPrompt}

      Context:
      - Topic: ${keyword}
      - Company: ${companyData.companyName}
      - Block type: ${blockType}
      - Current content: "${existingBlock.content || existingBlock.description || ''}"
      - Year: ${currentYear}

      IMPORTANT FORMATTING RULES:
      - DO NOT use markdown formatting (no **, *, #, etc.)
      - Write content in plain text with proper punctuation
      - Use natural language without any special formatting symbols
      - Content should be ready for direct display in HTML

      Please regenerate the content based on the custom instruction above while maintaining relevance to the topic and company.`;
    } else {
      // Use default prompts
      switch (blockType) {
        case 'introduction':
          prompt = `Rewrite the introduction for a blog about ${keyword}. Make it engaging, 150-200 words, include the keyword naturally. Company: ${companyData.companyName}. Year: ${currentYear}.

          IMPORTANT: DO NOT use markdown formatting (no **, *, #, etc.). Write in plain text with proper punctuation.`;
          break;

        case 'section':
          prompt = `Rewrite this section with H2: "${existingBlock.h2}". Topic: ${keyword}. Make it informative, 300-400 words. Include data/statistics. Year: ${currentYear}.

          IMPORTANT: DO NOT use markdown formatting (no **, *, #, etc.). Write in plain text with proper punctuation.`;
          break;

        case 'conclusion':
          prompt = `Rewrite the conclusion for a blog about ${keyword}. Summarize key points, include strong CTA for ${companyData.companyName}. 150-200 words. Year: ${currentYear}.

          IMPORTANT: DO NOT use markdown formatting (no **, *, #, etc.). Write in plain text with proper punctuation.`;
          break;

        case 'image':
          prompt = `Generate a new description for a ${existingBlock.imageType} image in a blog about ${keyword}. Make it specific, relevant to solar industry, and SEO-friendly.`;
          const imageResult = await model.generateContent(prompt);
          return {
            ...existingBlock,
            description: imageResult.response.text(),
            alt: `${keyword} - ${existingBlock.imageType} image`,
            regeneratedAt: new Date().toISOString()
          };

        default:
          prompt = `Improve this content about ${keyword}`;
      }
    }

    const result = await model.generateContent(prompt);
    const newContent = result.response.text();

    return {
      ...existingBlock,
      content: newContent,
      wordCount: newContent.split(' ').length,
      regeneratedAt: new Date().toISOString()
    };
  }
}

module.exports = new BlockService();
