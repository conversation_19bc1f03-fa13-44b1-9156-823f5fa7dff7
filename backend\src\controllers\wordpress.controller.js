// WordPress Controller - Handles WordPress publishing and integration
const draftService = require('../services/draft.service');
const contentService = require('../services/content.service');
const wordpressService = require('../services/wordpress.service');

class WordPressController {
  // Deploy blog to WordPress
  async deployToWordPress(req, res) {
    try {
      const { draftId, postStatus = 'draft' } = req.body;

      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Convert to WordPress HTML format
      const wordpressHTML = contentService.convertToWordPressHTML(draft);

      // Deploy to WordPress using environment variables
      const deployResult = await wordpressService.deployPost({
        title: draft.finalMeta.h1Title,
        content: wordpressHTML,
        metaDescription: draft.finalMeta.metaDescription,
        metaTitle: draft.finalMeta.metaTitle,
        featuredImage: draft.structuredContent.featureImage,
        config: { status: postStatus } // Use environment variables from service
      });

      // Update draft status
      await draftService.updateDraft(draftId, {
        wordpressPostId: deployResult.postId,
        wordpressUrl: deployResult.url,
        status: 'published',
        publishedAt: new Date()
      });

      res.json({
        success: true,
        postId: deployResult.postId,
        url: deployResult.url,
        message: 'Blog published to WordPress successfully'
      });

    } catch (error) {
      console.error('Error deploying to WordPress:', error);
      res.status(500).json({ error: 'Failed to deploy to WordPress' });
    }
  }

  // Test WordPress connection
  async testWordPressConnection(req, res) {
    try {
      const { wordpressConfig } = req.body;
      
      if (!wordpressConfig) {
        return res.status(400).json({ error: 'WordPress configuration is required' });
      }

      const testResult = await wordpressService.testConnection(wordpressConfig);

      res.json({
        success: true,
        connected: testResult.connected,
        message: testResult.message,
        details: testResult.details
      });

    } catch (error) {
      console.error('Error testing WordPress connection:', error);
      res.status(500).json({ error: 'Failed to test WordPress connection' });
    }
  }

  // Preview WordPress post
  async previewWordPressPost(req, res) {
    try {
      const { draftId } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      // Convert to WordPress HTML format for preview
      const wordpressHTML = contentService.convertToWordPressHTML(draft);

      res.json({
        success: true,
        preview: {
          title: draft.finalMeta.h1Title,
          content: wordpressHTML,
          metaDescription: draft.finalMeta.metaDescription,
          metaTitle: draft.finalMeta.metaTitle,
          featuredImage: draft.structuredContent.featureImage
        },
        message: 'WordPress preview generated successfully'
      });

    } catch (error) {
      console.error('Error generating WordPress preview:', error);
      res.status(500).json({ error: 'Failed to generate WordPress preview' });
    }
  }

  // Update WordPress post
  async updateWordPressPost(req, res) {
    try {
      const { draftId, wordpressConfig, updateFields } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      if (!draft.wordpressPostId) {
        return res.status(400).json({ error: 'Draft has not been published to WordPress yet' });
      }

      // Convert to WordPress HTML format
      const wordpressHTML = contentService.convertToWordPressHTML(draft);

      // Update WordPress post
      const updateResult = await wordpressService.updatePost({
        postId: draft.wordpressPostId,
        title: draft.finalMeta.h1Title,
        content: wordpressHTML,
        metaDescription: draft.finalMeta.metaDescription,
        metaTitle: draft.finalMeta.metaTitle,
        featuredImage: draft.structuredContent.featureImage,
        config: wordpressConfig,
        updateFields
      });

      // Update draft with new information
      await draftService.updateDraft(draftId, {
        lastUpdatedAt: new Date()
      });

      res.json({
        success: true,
        postId: updateResult.postId,
        url: updateResult.url,
        message: 'WordPress post updated successfully'
      });

    } catch (error) {
      console.error('Error updating WordPress post:', error);
      res.status(500).json({ error: 'Failed to update WordPress post' });
    }
  }

  // Delete WordPress post
  async deleteWordPressPost(req, res) {
    try {
      const { draftId, wordpressConfig } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      if (!draft.wordpressPostId) {
        return res.status(400).json({ error: 'Draft has not been published to WordPress yet' });
      }

      // Delete WordPress post
      await wordpressService.deletePost({
        postId: draft.wordpressPostId,
        config: wordpressConfig
      });

      // Update draft status
      await draftService.updateDraft(draftId, {
        wordpressPostId: null,
        wordpressUrl: null,
        status: 'content_review',
        deletedFromWordPressAt: new Date()
      });

      res.json({
        success: true,
        message: 'WordPress post deleted successfully'
      });

    } catch (error) {
      console.error('Error deleting WordPress post:', error);
      res.status(500).json({ error: 'Failed to delete WordPress post' });
    }
  }

  // Get WordPress post status
  async getWordPressPostStatus(req, res) {
    try {
      const { draftId, wordpressConfig } = req.body;
      
      if (!draftId) {
        return res.status(400).json({ error: 'Draft ID is required' });
      }

      const draft = await draftService.getDraft(draftId);
      if (!draft) {
        return res.status(404).json({ error: 'Draft not found' });
      }

      if (!draft.wordpressPostId) {
        return res.status(400).json({ error: 'Draft has not been published to WordPress yet' });
      }

      // Get WordPress post status
      const postStatus = await wordpressService.getPostStatus({
        postId: draft.wordpressPostId,
        config: wordpressConfig
      });

      res.json({
        success: true,
        status: postStatus,
        message: 'WordPress post status retrieved successfully'
      });

    } catch (error) {
      console.error('Error getting WordPress post status:', error);
      res.status(500).json({ error: 'Failed to get WordPress post status' });
    }
  }
}

module.exports = new WordPressController();
