const axios = require('axios');

class NewsService {
  constructor() {
    this.gnewsApiKey = process.env.GNEWS_API_KEY;
    this.newsdataApiKey = process.env.NEWSDATA_API_KEY;
    this.rapidApiKey = process.env.RAPID_API_KEY;
  }

  async fetchGoogleNews(query, limit = 10) {
    try {
      console.log(`Fetching Google News for query: ${query}`);
      
      const response = await axios.get('https://gnews.io/api/v4/search', {
        params: {
          q: query,
          token: this.gnewsApiKey,
          lang: 'en',
          country: 'us',
          max: limit,
          sortby: 'publishedAt'
        }
      });

      console.log(`Found ${response.data.articles?.length || 0} articles from Google News`);
      
      return response.data.articles || [];
    } catch (error) {
      console.error('Error fetching Google News:', error.response?.data || error.message);
      return [];
    }
  }

  async fetchNewsData(query, limit = 10) {
    try {
      console.log(`Fetching NewsData for query: ${query}`);
      
      const response = await axios.get('https://newsdata.io/api/1/news', {
        params: {
          apikey: this.newsdataApiKey,
          q: query,
          language: 'en',
          country: 'us',
          size: limit,
          category: 'business,technology'
        }
      });

      console.log(`Found ${response.data.results?.length || 0} articles from NewsData`);
      
      return response.data.results || [];
    } catch (error) {
      console.error('Error fetching NewsData:', error.response?.data || error.message);
      return [];
    }
  }

  async fetchCompetitorNews(competitors, focusKeyword) {
    try {
      console.log(`Fetching competitor news for: ${competitors.join(', ')} with keyword: ${focusKeyword}`);
      
      const allNews = [];
      
      // Fetch news for each competitor
      for (const competitor of competitors) {
        const competitorQuery = `${competitor} ${focusKeyword}`;
        
        // Try Google News first
        const gnewsArticles = await this.fetchGoogleNews(competitorQuery, 5);
        allNews.push(...gnewsArticles.map(article => ({
          ...article,
          source: 'gnews',
          competitor: competitor
        })));
        
        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Also fetch general industry news
      const industryQuery = `solar energy ${focusKeyword} 2025`;
      const industryNews = await this.fetchGoogleNews(industryQuery, 10);
      allNews.push(...industryNews.map(article => ({
        ...article,
        source: 'gnews',
        competitor: 'industry'
      })));
      
      console.log(`Total news articles collected: ${allNews.length}`);
      
      return allNews;
    } catch (error) {
      console.error('Error fetching competitor news:', error);
      return [];
    }
  }

  async fetchTrendingTopics(focusKeyword) {
    try {
      console.log(`Fetching trending topics for: ${focusKeyword}`);
      
      const queries = [
        `${focusKeyword} trends 2025`,
        `solar industry news 2025`,
        `renewable energy ${focusKeyword}`,
        `${focusKeyword} regulations 2025`
      ];
      
      const allTrends = [];
      
      for (const query of queries) {
        const articles = await this.fetchGoogleNews(query, 5);
        allTrends.push(...articles.map(article => ({
          ...article,
          source: 'gnews',
          category: this.categorizeArticle(article.title, article.description)
        })));
        
        // Add small delay
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log(`Total trending articles collected: ${allTrends.length}`);
      
      return allTrends;
    } catch (error) {
      console.error('Error fetching trending topics:', error);
      return [];
    }
  }

  categorizeArticle(title, description) {
    const text = `${title} ${description}`.toLowerCase();
    
    if (text.includes('regulation') || text.includes('policy') || text.includes('law')) {
      return 'regulatory';
    } else if (text.includes('trend') || text.includes('market') || text.includes('growth')) {
      return 'market_trends';
    } else if (text.includes('technology') || text.includes('innovation') || text.includes('new')) {
      return 'technology';
    } else if (text.includes('cost') || text.includes('price') || text.includes('finance')) {
      return 'financial';
    } else {
      return 'general';
    }
  }

  async getRecentIndustryInsights(focusKeyword) {
    try {
      console.log(`Getting recent industry insights for: ${focusKeyword}`);
      
      // Fetch recent news from multiple sources
      const [gnewsArticles, newsdataArticles] = await Promise.all([
        this.fetchGoogleNews(`solar industry ${focusKeyword}`, 15),
        this.fetchNewsData(`solar energy ${focusKeyword}`, 10)
      ]);
      
      const allArticles = [
        ...gnewsArticles.map(article => ({ ...article, source: 'gnews' })),
        ...newsdataArticles.map(article => ({ ...article, source: 'newsdata' }))
      ];
      
      // Sort by publication date (most recent first)
      allArticles.sort((a, b) => {
        const dateA = new Date(a.publishedAt || a.pubDate);
        const dateB = new Date(b.publishedAt || b.pubDate);
        return dateB - dateA;
      });
      
      // Extract key insights
      const insights = this.extractInsights(allArticles);
      
      console.log(`Extracted ${insights.length} key insights from ${allArticles.length} articles`);
      
      return {
        articles: allArticles.slice(0, 20), // Return top 20 most recent
        insights: insights,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting industry insights:', error);
      return {
        articles: [],
        insights: [],
        lastUpdated: new Date().toISOString()
      };
    }
  }

  extractInsights(articles) {
    const insights = [];
    const keywordCounts = {};
    
    articles.forEach(article => {
      const text = `${article.title} ${article.description || ''}`.toLowerCase();
      
      // Extract common themes
      const keywords = text.match(/\b\w{4,}\b/g) || [];
      keywords.forEach(keyword => {
        if (this.isRelevantKeyword(keyword)) {
          keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
        }
      });
      
      // Extract specific insights
      if (text.includes('increase') || text.includes('growth') || text.includes('rise')) {
        insights.push({
          type: 'growth_trend',
          title: article.title,
          insight: 'Market growth indicator',
          source: article.source?.name || 'News Source',
          date: article.publishedAt || article.pubDate
        });
      }
      
      if (text.includes('regulation') || text.includes('policy') || text.includes('incentive')) {
        insights.push({
          type: 'regulatory',
          title: article.title,
          insight: 'Regulatory update',
          source: article.source?.name || 'News Source',
          date: article.publishedAt || article.pubDate
        });
      }
      
      if (text.includes('technology') || text.includes('innovation') || text.includes('breakthrough')) {
        insights.push({
          type: 'technology',
          title: article.title,
          insight: 'Technology advancement',
          source: article.source?.name || 'News Source',
          date: article.publishedAt || article.pubDate
        });
      }
    });
    
    // Add trending keywords
    const trendingKeywords = Object.entries(keywordCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([keyword, count]) => ({ keyword, mentions: count }));
    
    if (trendingKeywords.length > 0) {
      insights.push({
        type: 'trending_keywords',
        insight: 'Most mentioned terms in recent news',
        keywords: trendingKeywords
      });
    }
    
    return insights.slice(0, 15); // Return top 15 insights
  }

  isRelevantKeyword(keyword) {
    const relevantTerms = [
      'solar', 'energy', 'renewable', 'installation', 'panel', 'battery',
      'storage', 'grid', 'efficiency', 'cost', 'savings', 'incentive',
      'tax', 'credit', 'financing', 'technology', 'innovation', 'market',
      'growth', 'regulation', 'policy', 'residential', 'commercial',
      'utility', 'power', 'electricity', 'clean', 'sustainable'
    ];
    
    return relevantTerms.some(term => keyword.includes(term)) && keyword.length >= 4;
  }
}

module.exports = new NewsService();
