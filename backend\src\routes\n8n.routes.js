const router = require('express').Router();
const n8nController = require('../controllers/n8n.controller');

// Health check
router.get('/health', n8nController.healthCheck);

// Data status
router.get('/status', n8nController.getDataStatus);

// Sync endpoints for N8N workflows
router.post('/sync/companies', n8nController.syncCompanies);
router.post('/sync/keywords', n8nController.syncKeywords);

// Content generation
router.post('/generate/content', n8nController.generateContent);
router.post('/content-callback', n8nController.contentCallback);

module.exports = router;
