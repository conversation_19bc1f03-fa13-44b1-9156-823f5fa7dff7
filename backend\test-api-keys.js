require('dotenv').config();
const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testApiKeys() {
  console.log('🔑 TESTING API KEYS');
  console.log('==================');
  
  console.log('Environment variables:');
  console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? `${process.env.GEMINI_API_KEY.substring(0, 10)}...` : 'NOT SET');
  console.log('GOOGLE_API_KEY:', process.env.GOOGLE_API_KEY ? `${process.env.GOOGLE_API_KEY.substring(0, 10)}...` : 'NOT SET');
  
  // Test GEMINI_API_KEY
  if (process.env.GEMINI_API_KEY) {
    console.log('\n🧪 Testing GEMINI_API_KEY...');
    try {
      const genAI1 = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      const model1 = genAI1.getGenerativeModel({ model: 'gemini-pro' });
      const result1 = await model1.generateContent('Say hello');
      const response1 = await result1.response;
      console.log('✅ GEMINI_API_KEY works!', response1.text().substring(0, 50) + '...');
    } catch (error) {
      console.log('❌ GEMINI_API_KEY failed:', error.message);
    }
  }
  
  // Test GOOGLE_API_KEY
  if (process.env.GOOGLE_API_KEY) {
    console.log('\n🧪 Testing GOOGLE_API_KEY...');
    try {
      const genAI2 = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
      const model2 = genAI2.getGenerativeModel({ model: 'gemini-pro' });
      const result2 = await model2.generateContent('Say hello');
      const response2 = await result2.response;
      console.log('✅ GOOGLE_API_KEY works!', response2.text().substring(0, 50) + '...');
    } catch (error) {
      console.log('❌ GOOGLE_API_KEY failed:', error.message);
    }
  }
  
  // Test different models
  const workingKey = process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY;
  if (workingKey) {
    console.log('\n🧪 Testing different models...');
    const genAI = new GoogleGenerativeAI(workingKey);
    
    const models = [
      'gemini-pro',
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      'gemini-2.0-flash'
    ];
    
    for (const modelName of models) {
      try {
        console.log(`Testing ${modelName}...`);
        const model = genAI.getGenerativeModel({ model: modelName });
        const result = await model.generateContent('Hello');
        const response = await result.response;
        console.log(`✅ ${modelName} works!`);
        break; // Use the first working model
      } catch (error) {
        console.log(`❌ ${modelName} failed:`, error.message.substring(0, 100));
      }
    }
  }
}

testApiKeys().catch(console.error);
