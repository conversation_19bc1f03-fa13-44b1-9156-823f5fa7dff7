const { sheets } = require('../config/google.config');

class GoogleSheetsService {
  async getCompanyData() {
    try {
      console.log('Fetching company data from sheet:', process.env.COMPANY_KT_SHEET_ID);

      // Try to fetch from Google Sheets with public access
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: process.env.COMPANY_KT_SHEET_ID,
        range: 'Wattmonk KT!A:H', // Extended range to include tone and brand voice
        key: process.env.GOOGLE_SHEETS_API_KEY
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) {
        console.log('No data found in sheet, using fallback data');
        return this.getFallbackCompanyData();
      }

      console.log(`Found ${rows.length} rows from Google Sheets`);

      // Skip header row and map data with extended fields
      const companies = rows.slice(1).map((row, index) => ({
        id: `company_${index + 1}`,
        companyName: row[0] || '',
        servicesOffered: row[1] || '',
        serviceOverview: row[2] || '',
        aboutTheCompany: row[3] || '',
        brandVoice: row[4] || 'Professional and informative',
        tone: row[5] || 'Expert and trustworthy',
        targetAudience: row[6] || 'Solar installers and businesses',
        contentObjective: row[7] || 'Generate leads and establish authority'
      }));

      // Filter out empty rows
      const validCompanies = companies.filter(company => company.companyName);
      console.log(`Returning ${validCompanies.length} valid companies`);
      return validCompanies;
    } catch (error) {
      console.error('Error fetching company data:', error.message);
      console.log('Using fallback company data due to Google Sheets error');
      return this.getFallbackCompanyData();
    }
  }

  getFallbackCompanyData() {
    return [
      {
        id: 'company_1',
        companyName: 'Wattmonk',
        servicesOffered: 'Solar Sales Proposal, Site Survey, Plan Set, PTO Services',
        serviceOverview: 'Professional solar engineering and design services for installers',
        aboutTheCompany: 'Leading solar service provider offering comprehensive engineering solutions for solar installers worldwide',
        brandVoice: 'Professional, technical, and solution-oriented',
        tone: 'Expert, trustworthy, and educational',
        targetAudience: 'Solar installers, contractors, and renewable energy businesses',
        contentObjective: 'Generate qualified leads and establish thought leadership in solar engineering'
      },
      {
        id: 'company_2',
        companyName: 'SolarTech Solutions',
        servicesOffered: 'Solar Installation, Maintenance, Consulting',
        serviceOverview: 'End-to-end solar solutions for residential and commercial clients',
        aboutTheCompany: 'Innovative solar technology company focused on sustainable energy solutions',
        brandVoice: 'Innovative, customer-focused, and reliable',
        tone: 'Friendly, professional, and solution-oriented',
        targetAudience: 'Homeowners and commercial property owners',
        contentObjective: 'Drive installation inquiries and build brand awareness'
      },
      {
        id: 'company_3',
        companyName: 'GreenEnergy Pro',
        servicesOffered: 'Solar Design, Energy Storage, Grid Integration',
        serviceOverview: 'Advanced solar and energy storage solutions',
        aboutTheCompany: 'Professional energy solutions provider specializing in solar and battery systems',
        brandVoice: 'Technical, innovative, and forward-thinking',
        tone: 'Expert, cutting-edge, and informative',
        targetAudience: 'Energy professionals and tech-savvy consumers',
        contentObjective: 'Establish thought leadership and generate qualified leads'
      }
    ];
  }

  async getManualKeywords() {
    try {
      console.log('Fetching keywords from sheet:', process.env.WATTMONK_BLOG_SHEET_ID);

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: process.env.WATTMONK_BLOG_SHEET_ID,
        range: 'Manual Keywords!A:F', // Extended range for more data
        key: process.env.GOOGLE_SHEETS_API_KEY
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) {
        console.log('No manual keywords found in sheet, using fallback data');
        return this.getFallbackManualKeywords();
      }

      console.log(`Found ${rows.length} manual keyword rows from Google Sheets`);

      // Skip header and map data with extended fields
      const keywords = rows.slice(1).map((row, index) => ({
        id: `manual_keyword_${index + 1}`,
        focusKeyword: row[0] || '',
        articleFormat: row[1] || '',
        wordCount: row[2] || '',
        targetAudience: row[3] || '',
        objective: row[4] || '',
        tone: row[5] || 'Professional',
        source: 'manual'
      }));

      const validKeywords = keywords.filter(keyword => keyword.focusKeyword);
      console.log(`Returning ${validKeywords.length} valid manual keywords`);
      return validKeywords;
    } catch (error) {
      console.error('Error fetching manual keywords:', error.message);
      console.log('Using fallback manual keywords due to Google Sheets error');
      return this.getFallbackManualKeywords();
    }
  }

  getFallbackManualKeywords() {
    return [
      {
        id: 'manual_keyword_1',
        focusKeyword: 'solar PTO process',
        articleFormat: 'How-to Guide',
        wordCount: '2000-2500',
        targetAudience: 'Solar Installers',
        objective: 'Educate installers on PTO procedures and promote Wattmonk PTO services',
        tone: 'Educational and authoritative',
        source: 'manual'
      },
      {
        id: 'manual_keyword_2',
        focusKeyword: 'solar permit design requirements',
        articleFormat: 'Technical Guide',
        wordCount: '1800-2200',
        targetAudience: 'Solar Engineers & Installers',
        objective: 'Establish authority in solar permitting and drive permit design service leads',
        tone: 'Technical and professional',
        source: 'manual'
      },
      {
        id: 'manual_keyword_3',
        focusKeyword: 'solar site survey best practices',
        articleFormat: 'Best Practices Guide',
        wordCount: '1500-2000',
        targetAudience: 'Solar Sales Teams',
        objective: 'Position Wattmonk as site survey experts and generate service inquiries',
        tone: 'Practical and informative',
        source: 'manual'
      },
      {
        id: 'manual_keyword_4',
        focusKeyword: 'solar proposal software comparison',
        articleFormat: 'Comparison Review',
        wordCount: '2500-3000',
        targetAudience: 'Solar Business Owners',
        objective: 'Drive traffic and establish thought leadership in solar sales tools',
        tone: 'Analytical and objective',
        source: 'manual'
      }
    ];
  }

  async getCompanyByName(companyName) {
    const companies = await this.getCompanyData();
    return companies.find(company =>
      company.companyName.toLowerCase() === companyName.toLowerCase()
    );
  }

  async getCompanyById(companyId) {
    const companies = await this.getCompanyData();
    return companies.find(company => company.id === companyId);
  }

  async getKeywordSuggestions(companyId) {
    console.log('Getting keyword suggestions for company:', companyId);

    try {
      // Get company data to generate relevant auto keywords
      const company = await this.getCompanyById(companyId);

      // Get 2 manual keywords from Google Sheets
      const allManualKeywords = await this.getManualKeywords();
      const manualKeywords = allManualKeywords.slice(0, 2); // Take first 2

      // Generate 2 automated keywords based on company data
      const autoKeywords = this.generateAutoKeywords(company);

      console.log(`Returning ${manualKeywords.length} manual and ${autoKeywords.length} auto keywords`);

      return {
        manual: manualKeywords,
        auto: autoKeywords,
        total: [...manualKeywords, ...autoKeywords]
      };
    } catch (error) {
      console.error('Error getting keyword suggestions:', error);
      // Return fallback data if there's an error
      return this.getFallbackKeywordSuggestions();
    }
  }

  getFallbackKeywordSuggestions() {
    const fallbackManual = [
      {
        id: 'manual_keyword_1',
        focusKeyword: 'solar PTO process',
        articleFormat: 'How-to Guide',
        wordCount: '2000-2500',
        targetAudience: 'Solar Installers',
        objective: 'Educate installers on PTO procedures and promote Wattmonk PTO services',
        tone: 'Educational and authoritative',
        source: 'manual'
      },
      {
        id: 'manual_keyword_2',
        focusKeyword: 'solar permit design requirements',
        articleFormat: 'Technical Guide',
        wordCount: '1800-2200',
        targetAudience: 'Solar Engineers & Installers',
        objective: 'Establish authority in solar permitting and drive permit design service leads',
        tone: 'Technical and professional',
        source: 'manual'
      }
    ];

    const fallbackAuto = [
      {
        id: 'auto_keyword_1',
        focusKeyword: 'solar installation best practices',
        articleFormat: 'Best Practices Guide',
        wordCount: '2200-2800',
        targetAudience: 'Solar Installers',
        objective: 'Generate leads for installation services and establish expertise',
        tone: 'Practical and informative',
        source: 'ai'
      },
      {
        id: 'auto_keyword_2',
        focusKeyword: 'commercial solar ROI calculator',
        articleFormat: 'Tool & Calculator Guide',
        wordCount: '1800-2400',
        targetAudience: 'Business Owners',
        objective: 'Drive commercial solar leads and showcase financial benefits',
        tone: 'Analytical and persuasive',
        source: 'ai'
      }
    ];

    return {
      manual: fallbackManual,
      auto: fallbackAuto,
      total: [...fallbackManual, ...fallbackAuto]
    };
  }

  generateAutoKeywords(company) {
    if (!company) {
      return [
        {
          id: 'auto_keyword_1',
          focusKeyword: 'solar installation best practices',
          articleFormat: 'Best Practices Guide',
          wordCount: '2200-2800',
          targetAudience: 'Solar Installers',
          objective: 'Generate leads for installation services and establish expertise',
          tone: 'Practical and informative',
          source: 'ai'
        },
        {
          id: 'auto_keyword_2',
          focusKeyword: 'commercial solar ROI calculator',
          articleFormat: 'Tool & Calculator Guide',
          wordCount: '1800-2400',
          targetAudience: 'Business Owners',
          objective: 'Drive commercial solar leads and showcase financial benefits',
          tone: 'Analytical and persuasive',
          source: 'ai'
        }
      ];
    }

    // Generate keywords based on company services
    const services = company.servicesOffered.toLowerCase();
    const companyName = company.companyName;
    const autoKeywords = [];

    if (services.includes('proposal')) {
      autoKeywords.push({
        id: 'auto_keyword_1',
        focusKeyword: 'solar proposal automation tools',
        articleFormat: 'Technology Review',
        wordCount: '2000-2500',
        targetAudience: 'Solar Sales Teams',
        objective: `Promote ${companyName} proposal services and establish thought leadership`,
        tone: company.tone || 'Professional and informative',
        source: 'ai'
      });
    }

    if (services.includes('survey') || services.includes('site')) {
      autoKeywords.push({
        id: 'auto_keyword_2',
        focusKeyword: 'solar site assessment technology',
        articleFormat: 'Technical Guide',
        wordCount: '1800-2300',
        targetAudience: 'Solar Engineers',
        objective: `Showcase ${companyName} survey capabilities and generate service leads`,
        tone: company.tone || 'Technical and professional',
        source: 'ai'
      });
    }

    // Fill remaining slots with service-specific keywords
    while (autoKeywords.length < 2) {
      if (services.includes('pto')) {
        autoKeywords.push({
          id: `auto_keyword_${autoKeywords.length + 1}`,
          focusKeyword: 'solar interconnection process',
          articleFormat: 'Process Guide',
          wordCount: '1900-2400',
          targetAudience: 'Solar Installers',
          objective: `Promote ${companyName} PTO services and educate market`,
          tone: company.tone || 'Educational and authoritative',
          source: 'ai'
        });
      } else {
        autoKeywords.push({
          id: `auto_keyword_${autoKeywords.length + 1}`,
          focusKeyword: 'solar energy system optimization',
          articleFormat: 'Technical Guide',
          wordCount: '2100-2600',
          targetAudience: 'Solar Professionals',
          objective: `Establish ${companyName} as technical authority and generate leads`,
          source: 'ai'
        });
      }
    }

    return autoKeywords.slice(0, 2);
  }
   async getAlignedBlogData(focusKeyword) {
    try {
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: process.env.WATTMONK_BLOG_SHEET_ID,
        range: 'Manual Keywords!A:E',
        key: process.env.GOOGLE_SHEETS_API_KEY
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) return null;

      // Find the row with matching focus keyword
      const keywordRow = rows.find(row =>
        row[0]?.toLowerCase() === focusKeyword.toLowerCase()
      );

      if (keywordRow) {
        return {
          focusKeyword: keywordRow[0],
          articleFormat: keywordRow[1],
          wordCount: keywordRow[2],
          targetAudience: keywordRow[3],
          objective: keywordRow[4]
        };
      }

      return null;
    } catch (error) {
      console.error('Error fetching aligned blog data:', error);
      // Return fallback data instead of throwing error
      return {
        focusKeyword: focusKeyword,
        articleFormat: 'How To',
        wordCount: '2000',
        targetAudience: 'Solar Installers',
        objective: 'Promote Solar Services'
      };
    }
  }
}

module.exports = new GoogleSheetsService();
