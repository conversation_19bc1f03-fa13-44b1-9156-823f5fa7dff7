const { google } = require('googleapis');

// Try OAuth2 first, fallback to API key
let sheets;

try {
  // OAuth2 configuration (if credentials are available)
  if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI || 'http://localhost:5000/auth/google/callback'
    );

    // Set refresh token if available
    if (process.env.GOOGLE_REFRESH_TOKEN) {
      auth.setCredentials({
        refresh_token: process.env.GOOGLE_REFRESH_TOKEN
      });
    }

    sheets = google.sheets({ version: 'v4', auth });
  } else {
    // Fallback to API key for public sheets
    sheets = google.sheets({
      version: 'v4',
      auth: process.env.GOOGLE_SHEETS_API_KEY
    });
  }
} catch (error) {
  console.error('Google Sheets auth error:', error);
  // Final fallback to API key
  sheets = google.sheets({
    version: 'v4',
    auth: process.env.GOOGLE_SHEETS_API_KEY
  });
}

module.exports = { sheets };