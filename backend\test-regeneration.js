require('dotenv').config();
const blockService = require('./src/services/block.service');

async function testRegeneration() {
  console.log('🔄 TESTING BLOCK REGENERATION');
  console.log('==============================');
  
  // Sample blog blocks
  const sampleBlocks = [
    {
      id: 'block_0',
      type: 'introduction',
      content: 'Solar energy is becoming increasingly popular among homeowners. This comprehensive guide will walk you through everything you need to know about solar panel installation.',
      editable: true,
      wordCount: 25
    },
    {
      id: 'block_1',
      type: 'section',
      h2: 'Understanding Solar Panel Types',
      content: 'There are several types of solar panels available in the market. Monocrystalline panels are the most efficient, while polycrystalline panels offer good value for money.',
      editable: true,
      sectionNumber: 1,
      wordCount: 30
    },
    {
      id: 'block_2',
      type: 'conclusion',
      content: 'Solar panel installation is a smart investment for your home. Contact our experts today to get started with your solar journey.',
      editable: true,
      wordCount: 22
    }
  ];
  
  const sampleCompanyData = {
    companyName: 'SolarTech Solutions',
    serviceOverview: 'Professional solar installation services'
  };
  
  const keyword = 'solar panel installation';
  
  // Test regenerating each block type
  for (let i = 0; i < sampleBlocks.length; i++) {
    const block = sampleBlocks[i];
    console.log(`\n🧪 Test ${i + 1}: Regenerating ${block.type} block`);
    console.log(`📝 Original content: ${block.content.substring(0, 100)}...`);
    
    try {
      // Test 1: Default regeneration
      console.log('\n🔄 Default regeneration...');
      const regenerated1 = await blockService.regenerateBlock(
        block.type,
        i,
        keyword,
        sampleCompanyData,
        sampleBlocks,
        null // No custom prompt
      );
      
      console.log('✅ Default regeneration successful!');
      console.log(`📝 New content: ${regenerated1.content ? regenerated1.content.substring(0, 100) : regenerated1.description?.substring(0, 100)}...`);
      
      // Test 2: Custom prompt regeneration
      console.log('\n🎨 Custom prompt regeneration...');
      const customPrompt = `Make this content more engaging and include specific statistics about ${keyword}`;
      const regenerated2 = await blockService.regenerateBlock(
        block.type,
        i,
        keyword,
        sampleCompanyData,
        sampleBlocks,
        customPrompt
      );
      
      console.log('✅ Custom regeneration successful!');
      console.log(`📝 Custom content: ${regenerated2.content ? regenerated2.content.substring(0, 100) : regenerated2.description?.substring(0, 100)}...`);
      
      // Verify uniqueness
      const original = block.content || block.description || '';
      const default_new = regenerated1.content || regenerated1.description || '';
      const custom_new = regenerated2.content || regenerated2.description || '';
      
      const isUnique1 = original !== default_new;
      const isUnique2 = default_new !== custom_new;
      
      console.log(`🔍 Uniqueness check: Default different from original: ${isUnique1 ? '✅' : '❌'}`);
      console.log(`🔍 Uniqueness check: Custom different from default: ${isUnique2 ? '✅' : '❌'}`);
      
    } catch (error) {
      console.error(`❌ Regeneration failed for ${block.type}:`, error.message);
    }
  }
  
  console.log('\n🎉 REGENERATION TEST COMPLETED!');
}

testRegeneration().catch(console.error);
