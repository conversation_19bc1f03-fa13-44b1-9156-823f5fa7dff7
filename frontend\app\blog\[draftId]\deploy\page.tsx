"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { StepperHeader } from "@/components/stepper-header"
import { Skeleton } from "@/components/ui/skeleton"
import { CheckCircle, Globe, FileText } from "lucide-react"
import { api } from "@/lib/api"

interface DeployPageProps {
  params: { draftId: string }
}

export default function DeployPage({ params }: DeployPageProps) {
  const [loading, setLoading] = useState(true)
  const [deploying, setDeploying] = useState(false)
  const [deployed, setDeployed] = useState(false)
  const [draft, setDraft] = useState<any>(null)
  const [postStatus, setPostStatus] = useState("draft")
  
  const router = useRouter()
  const { toast } = useToast()
  const draftId = params.draftId

  useEffect(() => {
    loadDraft()
  }, [draftId])

  const loadDraft = async () => {
    try {
      const response = await api.getDraft(draftId)
      setDraft(response)
    } catch (error) {
      toast({
        title: "Error loading draft",
        description: "Failed to load draft data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDeploy = async () => {
    setDeploying(true)
    try {
      const response = await api.deployToWordPress(draftId, { postStatus })

      if (response.success) {
        setDeployed(true)
        toast({
          title: "Deployment successful!",
          description: `Blog post deployed to WordPress as ${postStatus}.`,
        })
      }
    } catch (error) {
      console.error('Deployment error:', error);
      toast({
        title: "Deployment failed",
        description: "Failed to deploy to WordPress. Please check server logs.",
        variant: "destructive",
      })
    } finally {
      setDeploying(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={3} draftId={draftId} />
        <main className="max-w-4xl mx-auto px-6 py-8">
          <div className="space-y-6">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-64 w-full" />
          </div>
        </main>
      </div>
    )
  }

  if (deployed) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={3} draftId={draftId} />
        <main className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Successfully Deployed!</h1>
            <p className="text-gray-600">Your blog post has been deployed to WordPress.</p>
            
            <div className="flex justify-center gap-4">
              <Button onClick={() => router.push("/")} variant="outline">
                Create New Blog
              </Button>
              <Button onClick={() => window.open(process.env.NEXT_PUBLIC_WORDPRESS_URL || "#", "_blank")}>
                <Globe className="h-4 w-4 mr-2" />
                View WordPress Site
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StepperHeader currentStep={3} draftId={draftId} />

      <main className="max-w-4xl mx-auto px-6 py-8">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Deploy to WordPress</h1>
            <p className="text-gray-600">Deploy your completed blog post to your WordPress site</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Blog Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Blog Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {draft?.metaOptions?.[0] && (
                  <div>
                    <h3 className="font-semibold text-lg">{draft.metaOptions[0].h1Title}</h3>
                    <p className="text-sm text-gray-600 mt-1">{draft.metaOptions[0].metaDescription}</p>
                  </div>
                )}
                
                <div className="space-y-2">
                  <Badge variant="outline">
                    Word Count: {draft?.blocks?.reduce((acc: number, block: any) => 
                      acc + (block.wordCount || 0), 0) || 0}
                  </Badge>
                  <Badge variant="outline">
                    Blocks: {draft?.blocks?.length || 0}
                  </Badge>
                  <Badge variant="outline">
                    Images: {draft?.blocks?.filter((b: any) => b.type === 'image').length || 0}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* WordPress Deployment */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Deploy to WordPress
                </CardTitle>
                <p className="text-sm text-gray-600">
                  WordPress credentials are configured on the server. Choose your post status and deploy.
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Post Status</Label>
                  <select
                    id="status"
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={postStatus}
                    onChange={(e) => setPostStatus(e.target.value)}
                  >
                    <option value="draft">Draft</option>
                    <option value="publish">Publish</option>
                    <option value="private">Private</option>
                  </select>
                </div>

                <Button
                  onClick={handleDeploy}
                  disabled={deploying}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  {deploying ? "Deploying..." : "Deploy to WordPress"}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
