require('dotenv').config();
const { model } = require('./src/config/gemini.config');

async function testAIOutput() {
  console.log('🧪 TESTING AI OUTPUT FORMAT');
  console.log('============================');
  
  const prompt = `Generate a simple JSON response for testing:

{
  "title": "Test Title",
  "sections": [
    {
      "h2": "First Section",
      "content": "This is test content"
    }
  ],
  "references": ["Reference 1", "Reference 2"]
}

Return ONLY the JSON, no markdown, no extra text.`;

  try {
    console.log('🚀 Sending prompt to AI...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('\n📝 RAW AI RESPONSE:');
    console.log('===================');
    console.log(text);
    console.log('===================');
    
    console.log('\n🔍 PARSING ATTEMPT:');
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      console.log('Cleaned text:', cleanedText);
      
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        console.log('JSON match found:', jsonMatch[0].substring(0, 200) + '...');
        const parsed = JSON.parse(jsonMatch[0]);
        console.log('✅ Successfully parsed JSON:', parsed);
      } else {
        console.log('❌ No JSON match found');
      }
    } catch (parseError) {
      console.error('❌ Parse error:', parseError.message);
    }
    
  } catch (error) {
    console.error('❌ AI request failed:', error.message);
  }
}

testAIOutput().catch(console.error);
