const { GoogleAuth } = require('google-auth-library');
const axios = require('axios');

class VertexAIImageService {
  constructor() {
    this.projectId = process.env.GOOGLE_PROJECT_ID || 'your-project-id';
    this.location = 'us-central1';
    this.auth = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS, // Path to service account key
      // If no key file, will use default credentials or API key
    });
  }

  async generateImage(prompt) {
    try {
      console.log('🎨 Generating image with Vertex AI for prompt:', prompt);

      // Get access token
      const authClient = await this.auth.getClient();
      const accessToken = await authClient.getAccessToken();

      if (!accessToken.token) {
        throw new Error('Failed to get access token');
      }

      // Vertex AI endpoint for image generation
      const endpoint = `https://${this.location}-aiplatform.googleapis.com/v1/projects/${this.projectId}/locations/${this.location}/publishers/google/models/imagegeneration@006:predict`;

      const requestBody = {
        instances: [
          {
            prompt: prompt,
          }
        ],
        parameters: {
          sampleCount: 1,
          aspectRatio: "16:9",
          safetyFilterLevel: "block_some",
          personGeneration: "allow_adult"
        }
      };

      console.log('🚀 Calling Vertex AI endpoint:', endpoint);

      const response = await axios.post(endpoint, requestBody, {
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 second timeout
      });

      if (response.data && response.data.predictions && response.data.predictions.length > 0) {
        const prediction = response.data.predictions[0];
        
        if (prediction.bytesBase64Encoded) {
          console.log('✅ Image generated successfully');
          return {
            success: true,
            imageBase64: prediction.bytesBase64Encoded,
            mimeType: prediction.mimeType || 'image/png',
            prompt: prompt
          };
        }
      }

      throw new Error('No image data in response');

    } catch (error) {
      console.error('❌ Vertex AI image generation failed:', error.message);
      
      // Fallback to a placeholder service
      console.log('🔄 Using fallback placeholder image...');
      return this.generateFallbackImage(prompt);
    }
  }

  generateFallbackImage(prompt) {
    // Generate a unique placeholder image
    const timestamp = Date.now();
    const imageId = Math.random().toString(36).substring(7);
    
    // Create a simple base64 placeholder (1x1 pixel PNG)
    const placeholderBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    return {
      success: true,
      imageBase64: placeholderBase64,
      mimeType: 'image/png',
      prompt: prompt,
      fallback: true,
      placeholderUrl: `https://picsum.photos/800/450?random=${timestamp}&sig=${imageId}`,
      message: 'Using placeholder image - configure Google Cloud credentials for AI generation'
    };
  }

  async enhancePrompt(basicPrompt, keyword) {
    try {
      const { model } = require('../config/gemini.config');
      
      const enhancementPrompt = `Enhance this image prompt for professional blog content about "${keyword}":

Original prompt: "${basicPrompt}"

Create a detailed, professional image generation prompt that includes:
- Specific visual elements and composition
- Professional photography style
- Appropriate lighting and colors
- Business/professional context
- High quality, modern aesthetic

Return only the enhanced prompt, no explanations.`;

      const result = await model.generateContent(enhancementPrompt);
      const response = await result.response;
      const enhancedPrompt = response.text().trim();
      
      console.log('✅ Prompt enhanced by AI');
      return enhancedPrompt;
      
    } catch (error) {
      console.error('❌ Prompt enhancement failed:', error.message);
      return `Professional, high-quality image: ${basicPrompt}. Modern business photography style, clean composition, professional lighting.`;
    }
  }
}

module.exports = new VertexAIImageService();
