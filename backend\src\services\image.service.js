const googleVisionService = require('./google-vision.service');

class ImageService {
  async generateImageDescription(prompt, keyword, companyData, h1Title = null) {
    try {
      // Use Google Vision service for better image descriptions
      const imageDescription = await googleVisionService.generateImageDescription(
        keyword,
        h1Title || `${keyword} - ${companyData.companyName}`,
        companyData,
        'feature'
      );

      // Enhance the prompt if user provided specific requirements
      let finalDescription = imageDescription.description;
      if (prompt && prompt.trim() !== '') {
        finalDescription = await googleVisionService.enhanceImagePrompt(
          imageDescription.description,
          keyword,
          `User request: ${prompt}`
        );
      }

      return {
        success: true,
        description: finalDescription,
        alt: imageDescription.alt,
        title: imageDescription.title,
        style: imageDescription.style,
        elements: imageDescription.elements,
        enhancedPrompt: imageDescription.prompt
      };
    } catch (error) {
      console.error('Error generating image description:', error);

      // Fallback to basic description
      return {
        success: true,
        description: `Professional image showing ${keyword} related to ${companyData.companyName} solar services`,
        alt: `${keyword} - ${companyData.companyName}`,
        title: `Professional ${keyword} Guide`,
        style: "professional, modern",
        elements: [keyword, "solar panels"],
        enhancedPrompt: `Professional ${keyword} image, modern solar installation, high quality photography`
      };
    }
  }

  async generateImageWithGemini(description, keyword) {
    // Use Google Vision API for enhanced image descriptions
    const googleVisionService = require('./google-vision.service');
    
    try {
      const prompt = `
        Create an extremely detailed image generation prompt for: "${description}"
        
        Context: This is for a professional blog about "${keyword}" in the solar industry.
        
        Requirements:
        - Make it a detailed prompt suitable for AI image generation (Stable Diffusion, DALL-E, etc.)
        - Include specific details about lighting, composition, style
        - Mention professional, high-quality, modern aesthetic
        - Include relevant solar industry elements
        - Specify image style (photorealistic, professional photography, etc.)
        - Keep it under 500 characters
        
        Return only the detailed image generation prompt, nothing else.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const enhancedPrompt = response.text().trim();
      
      // For now, return a placeholder image URL with the enhanced prompt
      // In a real implementation, you would call an image generation API here
      return {
        success: true,
        imageUrl: `https://via.placeholder.com/800x400/0066cc/ffffff?text=${encodeURIComponent(keyword)}`,
        enhancedPrompt,
        description,
        alt: `Professional ${keyword} image`,
        title: `${keyword} - Generated Image`
      };
    } catch (error) {
      console.error('Error generating image with Gemini:', error);
      throw new Error('Failed to generate image');
    }
  }

  async uploadImage(file, blockId) {
    try {
      if (!file) {
        throw new Error('No file provided');
      }

      // File is already saved by multer middleware
      const imageUrl = `/uploads/${file.filename}`;
      const fileName = file.filename;

      return {
        success: true,
        imageUrl,
        fileName,
        alt: file.originalname.replace(/\.[^/.]+$/, ""), // Remove extension for alt text
        title: file.originalname.replace(/\.[^/.]+$/, ""), // Remove extension for title
        size: file.size,
        mimetype: file.mimetype
      };
    } catch (error) {
      console.error('Error processing uploaded image:', error);
      throw new Error('Failed to process uploaded image');
    }
  }

  // Helper method to validate image descriptions
  validateImageDescription(description) {
    if (!description || description.trim().length < 10) {
      return {
        valid: false,
        message: 'Image description must be at least 10 characters long'
      };
    }
    
    if (description.length > 500) {
      return {
        valid: false,
        message: 'Image description must be less than 500 characters'
      };
    }
    
    return { valid: true };
  }

  // Generate alt text from description
  generateAltText(description, keyword) {
    const cleanDescription = description.replace(/[^\w\s]/g, '').trim();
    const words = cleanDescription.split(' ').slice(0, 10).join(' ');
    return `${keyword} - ${words}`;
  }
}

module.exports = new ImageService();
