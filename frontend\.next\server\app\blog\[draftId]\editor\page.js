/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/blog/[draftId]/editor/page";
exports.ids = ["app/blog/[draftId]/editor/page"];
exports.modules = {

/***/ "(rsc)/./app/blog/[draftId]/editor/page.tsx":
/*!********************************************!*\
  !*** ./app/blog/[draftId]/editor/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\blog-gen-ai\\frontend\\app\\blog\\[draftId]\\editor\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9131df547838\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxibG9nLWdlbi1haVxcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MTMxZGY1NDc4MzhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'ArticleScribe',\n    description: 'AI Blog Builder with WordPress Deployment',\n    generator: 'ArticleScribe'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFdBQVc7QUFDYixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsiRDpcXGJsb2ctZ2VuLWFpXFxmcm9udGVuZFxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0FydGljbGVTY3JpYmUnLFxuICBkZXNjcmlwdGlvbjogJ0FJIEJsb2cgQnVpbGRlciB3aXRoIFdvcmRQcmVzcyBEZXBsb3ltZW50JyxcbiAgZ2VuZXJhdG9yOiAnQXJ0aWNsZVNjcmliZScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZ2VuZXJhdG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&page=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&appPaths=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage.tsx&appDir=D%3A%5Cblog-gen-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cblog-gen-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&page=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&appPaths=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage.tsx&appDir=D%3A%5Cblog-gen-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cblog-gen-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/blog/[draftId]/editor/page.tsx */ \"(rsc)/./app/blog/[draftId]/editor/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'blog',\n        {\n        children: [\n        '[draftId]',\n        {\n        children: [\n        'editor',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/blog/[draftId]/editor/page\",\n        pathname: \"/blog/[draftId]/editor\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&page=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&appPaths=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage.tsx&appDir=D%3A%5Cblog-gen-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cblog-gen-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cblog%5C%5C%5BdraftId%5D%5C%5Ceditor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cblog%5C%5C%5BdraftId%5D%5C%5Ceditor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/blog/[draftId]/editor/page.tsx */ \"(rsc)/./app/blog/[draftId]/editor/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNibG9nLWdlbi1haSU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDYmxvZyU1QyU1QyU1QmRyYWZ0SWQlNUQlNUMlNUNlZGl0b3IlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxibG9nLWdlbi1haVxcXFxmcm9udGVuZFxcXFxhcHBcXFxcYmxvZ1xcXFxbZHJhZnRJZF1cXFxcZWRpdG9yXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cblog%5C%5C%5BdraftId%5D%5C%5Ceditor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/blog/[draftId]/editor/page.tsx":
/*!********************************************!*\
  !*** ./app/blog/[draftId]/editor/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Link,RefreshCw,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _components_stepper_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/stepper-header */ \"(ssr)/./components/stepper-header.tsx\");\n/* harmony import */ var _components_content_block__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/content-block */ \"(ssr)/./components/content-block.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditorPage() {\n    const [draft, setDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [blocks, setBlocks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatingLinks, setGeneratingLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingBlock, setEditingBlock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editContent, setEditContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [wordCount, setWordCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const draftId = params.draftId;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            loadDraftAndContent();\n        }\n    }[\"EditorPage.useEffect\"], [\n        draftId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // Calculate total word count\n            const total = blocks.reduce({\n                \"EditorPage.useEffect.total\": (sum, block)=>sum + (block.wordCount || 0)\n            }[\"EditorPage.useEffect.total\"], 0);\n            setWordCount(total);\n        }\n    }[\"EditorPage.useEffect\"], [\n        blocks\n    ]);\n    const loadDraftAndContent = async ()=>{\n        try {\n            // Load draft data first\n            const draftResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.getDraft(draftId);\n            if (draftResponse.success && draftResponse.data) {\n                const draftData = draftResponse.data;\n                setDraft(draftData);\n                // Check if structured content already exists\n                if (draftData.structuredContent && draftData.structuredContent.length > 0) {\n                    setBlocks(draftData.structuredContent);\n                } else {\n                    // Generate new structured content\n                    await generateContent();\n                }\n            }\n        } catch (error) {\n            toast({\n                title: \"Error loading draft\",\n                description: \"Failed to load draft data. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const generateContent = async ()=>{\n        try {\n            console.log('🚀 FRONTEND: Generating real AI content for draft:', draftId);\n            // Call backend API to generate real structured content\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateStructuredContent(draftId);\n            console.log('📡 FRONTEND: API Response:', response);\n            if (response.success && response.structuredContent) {\n                console.log('✅ FRONTEND: Using real AI-generated content with', response.structuredContent.length, 'blocks');\n                setBlocks(response.structuredContent);\n                toast({\n                    title: \"AI Content generated successfully!\",\n                    description: `Generated ${response.structuredContent.length} content blocks with Gemini 2.0 Flash.`\n                });\n                return;\n            }\n            console.log('⚠️ FRONTEND: API response invalid, using fallback content');\n            // Fallback: Mock blog content blocks if API fails\n            const mockBlocks = [\n                {\n                    id: \"intro-1\",\n                    type: \"introduction\",\n                    content: \"In today's fast-paced business environment, small business owners are constantly looking for ways to streamline operations, reduce manual tasks, and focus on what matters most - growing their business. AI automation tools have emerged as game-changers, offering sophisticated solutions that were once only available to large enterprises.\\n\\nThis comprehensive guide will walk you through the best AI automation tools specifically designed for small businesses, helping you understand how to implement them effectively and maximize your return on investment. Whether you're looking to automate customer service, marketing campaigns, or internal processes, we've got you covered.\",\n                    editable: true,\n                    wordCount: 124\n                },\n                {\n                    id: \"feature-img-1\",\n                    type: \"image\",\n                    imageType: \"feature\",\n                    alt: \"AI automation tools dashboard showing various business processes being automated\",\n                    editable: false\n                },\n                {\n                    id: \"section-1\",\n                    type: \"section\",\n                    h2: \"What Are AI Automation Tools and Why Do Small Businesses Need Them?\",\n                    content: \"AI automation tools are software solutions that use artificial intelligence to perform tasks that typically require human intervention. These tools can analyze data, make decisions, and execute actions based on predefined rules or learned patterns.\\n\\nFor small businesses, AI automation offers several key benefits:\\n\\n• **Cost Reduction**: Automate repetitive tasks to reduce labor costs\\n• **Improved Accuracy**: Minimize human errors in data processing and analysis\\n• **24/7 Operations**: Keep critical processes running around the clock\\n• **Scalability**: Handle increased workload without proportional staff increases\\n• **Competitive Advantage**: Access enterprise-level capabilities at affordable prices\\n\\nThe key is choosing the right tools that align with your specific business needs and budget constraints.\",\n                    editable: true,\n                    wordCount: 156\n                },\n                {\n                    id: \"section-2\",\n                    type: \"section\",\n                    h2: \"Top 5 AI Automation Tools for Small Business Operations\",\n                    content: \"After extensive research and testing, we've identified the top AI automation tools that deliver exceptional value for small businesses:\\n\\n**1. Zapier with AI Features**\\nZapier's AI-powered automation connects over 5,000 apps, making it easy to create complex workflows without coding. Recent AI enhancements include smart suggestions and natural language processing for trigger creation.\\n\\n**2. HubSpot's AI-Powered CRM**\\nHubSpot offers AI-driven lead scoring, predictive analytics, and automated email sequences. Their free tier makes it accessible for startups and growing businesses.\\n\\n**3. Chatfuel for Customer Service**\\nThis AI chatbot platform handles customer inquiries 24/7, reducing response times and freeing up your team for more complex tasks.\\n\\n**4. Calendly with Smart Scheduling**\\nAI-powered scheduling that learns from your preferences and automatically optimizes meeting times based on participant availability and preferences.\\n\\n**5. QuickBooks AI for Financial Management**\\nAutomated expense categorization, invoice processing, and financial forecasting powered by machine learning algorithms.\",\n                    editable: true,\n                    wordCount: 189\n                },\n                {\n                    id: \"inblog-img-1\",\n                    type: \"image\",\n                    imageType: \"in-blog\",\n                    alt: \"Comparison chart showing different AI automation tools and their features\",\n                    editable: false\n                },\n                {\n                    id: \"section-3\",\n                    type: \"section\",\n                    h2: \"Implementation Strategy: How to Successfully Deploy AI Automation\",\n                    content: \"Successfully implementing AI automation in your small business requires a strategic approach:\\n\\n**Phase 1: Assessment and Planning (Week 1-2)**\\n• Identify repetitive, time-consuming tasks\\n• Map current workflows and processes\\n• Set clear goals and success metrics\\n• Determine budget and resource allocation\\n\\n**Phase 2: Tool Selection and Setup (Week 3-4)**\\n• Research and compare automation tools\\n• Start with free trials or basic plans\\n• Configure initial automations for low-risk processes\\n• Train team members on new tools\\n\\n**Phase 3: Testing and Optimization (Week 5-8)**\\n• Monitor automation performance closely\\n• Gather feedback from team members\\n• Make adjustments and improvements\\n• Gradually expand to more complex processes\\n\\n**Phase 4: Scale and Advanced Features (Month 3+)**\\n• Implement more sophisticated automations\\n• Integrate multiple tools for comprehensive workflows\\n• Analyze ROI and adjust strategy as needed\\n• Explore advanced AI features and capabilities\",\n                    editable: true,\n                    wordCount: 178\n                },\n                {\n                    id: \"section-4\",\n                    type: \"section\",\n                    h2: \"Measuring ROI: Key Metrics for AI Automation Success\",\n                    content: \"To ensure your AI automation investment pays off, track these essential metrics:\\n\\n**Time Savings Metrics:**\\n• Hours saved per week on automated tasks\\n• Reduction in manual data entry time\\n• Faster response times to customer inquiries\\n\\n**Cost Efficiency Metrics:**\\n• Reduction in operational costs\\n• Decreased need for additional staff\\n• Lower error rates and associated costs\\n\\n**Business Growth Metrics:**\\n• Increased lead generation and conversion rates\\n• Improved customer satisfaction scores\\n• Enhanced team productivity and focus on strategic tasks\\n\\n**Quality Improvements:**\\n• Reduced error rates in data processing\\n• More consistent customer experiences\\n• Better compliance with business processes\\n\\nMost small businesses see a positive ROI within 3-6 months of implementing AI automation tools, with some reporting up to 300% return on investment within the first year.\",\n                    editable: true,\n                    wordCount: 145\n                },\n                {\n                    id: \"inblog-img-2\",\n                    type: \"image\",\n                    imageType: \"in-blog\",\n                    alt: \"ROI dashboard showing positive returns from AI automation implementation\",\n                    editable: false\n                },\n                {\n                    id: \"conclusion-1\",\n                    type: \"conclusion\",\n                    content: \"AI automation tools are no longer a luxury reserved for large corporations – they're essential for small businesses looking to compete and thrive in today's digital landscape. By starting with simple automations and gradually expanding your capabilities, you can transform your operations, reduce costs, and focus on what you do best: serving your customers and growing your business.\\n\\nRemember, the key to successful AI automation is starting small, measuring results, and continuously optimizing your processes. Choose tools that integrate well with your existing systems, provide excellent support, and offer room for growth as your business expands.\\n\\nReady to get started? Begin by identifying one repetitive task in your business and explore how AI automation can help you reclaim those valuable hours for more strategic activities.\",\n                    editable: true,\n                    wordCount: 142\n                },\n                {\n                    id: \"references-1\",\n                    type: \"references\",\n                    content: '1. McKinsey Global Institute. (2023). \"The Age of AI: Artificial Intelligence and the Future of Work.\"\\n2. Small Business Administration. (2023). \"Technology Adoption in Small Businesses: 2023 Report.\"\\n3. Zapier. (2023). \"State of Business Automation Report 2023.\"\\n4. HubSpot Research. (2023). \"AI in Small Business: Adoption and Impact Study.\"\\n5. Deloitte. (2023). \"AI and Automation in Small and Medium Enterprises.\"',\n                    editable: true,\n                    wordCount: 67\n                }\n            ];\n            console.log('📝 FRONTEND: Using fallback mock content');\n            setBlocks(mockBlocks);\n            toast({\n                title: \"Using template content\",\n                description: \"API content generation failed, using template. Check console for details.\",\n                variant: \"destructive\"\n            });\n        } catch (error) {\n            console.error('❌ FRONTEND: Content generation error:', error);\n            toast({\n                title: \"Error generating content\",\n                description: `Failed to generate blog content: ${error.message}`,\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        setSaving(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.saveDraft(draftId, {\n                structuredContent: blocks,\n                status: 'content_generation'\n            });\n            toast({\n                title: \"Draft saved\",\n                description: \"Your blog draft has been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Save failed\",\n                description: \"Failed to save draft. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleGenerateLinks = async ()=>{\n        setGeneratingLinks(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateLinks(draftId);\n            toast({\n                title: \"Links generated\",\n                description: \"Internal and external links have been generated.\"\n            });\n            router.push(`/blog/${draftId}/review`);\n        } catch (error) {\n            toast({\n                title: \"Link generation failed\",\n                description: \"Failed to generate links. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setGeneratingLinks(false);\n        }\n    };\n    const handleEditBlock = (block)=>{\n        setEditingBlock(block);\n        setEditContent(block.content || \"\");\n        setCustomPrompt(\"\");\n    };\n    const handleRegenerateBlock = async (blockId, type)=>{\n        console.log(`=== FRONTEND: Regenerating block ${blockId} with type ${type} ===`);\n        console.log('Draft ID:', draftId);\n        console.log('Custom prompt:', type === \"ai\" ? customPrompt : undefined);\n        console.log('Edit content:', type === \"manual\" ? editContent : undefined);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.regenerateBlock(draftId, blockId, type, type === \"ai\" ? customPrompt : undefined, type === \"manual\" ? editContent : undefined);\n            console.log('=== FRONTEND: Regeneration response ===', response);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                setEditingBlock(null);\n                toast({\n                    title: \"Block updated\",\n                    description: \"Content block has been successfully updated.\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error regenerating block:', error);\n            toast({\n                title: \"Update failed\",\n                description: \"Failed to update content block. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteBlock = (blockId)=>{\n        setBlocks(blocks.filter((block)=>block.id !== blockId));\n        toast({\n            title: \"Block deleted\",\n            description: \"Content block has been removed.\"\n        });\n    };\n    const handleImageGenerate = async (blockId, description)=>{\n        console.log(`Generating image for block ${blockId} with description: ${description}`);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.generateImage(draftId, blockId, description);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                toast({\n                    title: \"Image generated successfully!\",\n                    description: \"AI has generated an image description and placeholder for your content.\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error generating image:', error);\n            toast({\n                title: \"Image generation failed\",\n                description: \"Failed to generate image. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImageUpload = async (blockId, file)=>{\n        console.log(`Uploading image for block ${blockId}:`, file.name);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.api.uploadImage(draftId, blockId, file);\n            if (response.success && response.block) {\n                setBlocks(blocks.map((block)=>block.id === blockId ? {\n                        ...block,\n                        ...response.block\n                    } : block));\n                toast({\n                    title: \"Image uploaded\",\n                    description: \"Image has been uploaded successfully!\"\n                });\n            } else {\n                throw new Error('Invalid response format');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n            toast({\n                title: \"Upload failed\",\n                description: \"Failed to upload image. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                    currentStep: 3,\n                    draftId: draftId\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-5xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                className: \"h-16 w-full\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-32 w-full\"\n                                }, i, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_stepper_header__WEBPACK_IMPORTED_MODULE_11__.StepperHeader, {\n                currentStep: 3,\n                draftId: draftId\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-5xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Content Editor\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        wordCount,\n                                        \" words\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSaveDraft,\n                                    disabled: saving,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this),\n                                        saving ? \"Saving...\" : \"Save Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Preview\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleGenerateLinks,\n                                    disabled: generatingLinks,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        generatingLinks ? \"Generating...\" : \"Generate Links\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(`/blog/${draftId}/deploy`),\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Deploy to WordPress\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-5xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: blocks.map((block, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_content_block__WEBPACK_IMPORTED_MODULE_12__.ContentBlock, {\n                                block: block,\n                                onEdit: ()=>handleEditBlock(block),\n                                onRegenerate: ()=>handleRegenerateBlock(block.id, \"ai\"),\n                                onDelete: block.editable ? ()=>handleDeleteBlock(block.id) : undefined,\n                                onImageGenerate: handleImageGenerate,\n                                onImageUpload: handleImageUpload\n                            }, block.id, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.Dialog, {\n                        open: !!editingBlock,\n                        onOpenChange: ()=>setEditingBlock(null),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogContent, {\n                            className: \"max-w-4xl max-h-[80vh] overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogTitle, {\n                                            children: \"Edit Content Block\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_7__.DialogDescription, {\n                                            children: \"Modify the content manually or use AI to regenerate with a custom prompt\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"Content\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                    value: editContent,\n                                                    onChange: (e)=>setEditContent(e.target.value),\n                                                    rows: 8,\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium mb-2 block\",\n                                                    children: \"AI Regeneration Prompt (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    value: customPrompt,\n                                                    onChange: (e)=>setCustomPrompt(e.target.value),\n                                                    placeholder: \"e.g., Make it more engaging, Add statistics, Simplify for beginners...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleRegenerateBlock(editingBlock?.id || \"\", \"manual\"),\n                                                    className: \"flex-1\",\n                                                    children: \"Save Manual Changes\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>handleRegenerateBlock(editingBlock?.id || \"\", \"ai\"),\n                                                    disabled: !customPrompt,\n                                                    variant: \"outline\",\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Link_RefreshCw_Save_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Regenerate with AI\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\app\\\\blog\\\\[draftId]\\\\editor\\\\page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvYmxvZy9bZHJhZnRJZF0vZWRpdG9yL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDVztBQUNQO0FBQ0Y7QUFDQTtBQUNNO0FBQ3lEO0FBQ3pEO0FBQ1A7QUFDdUI7QUFFcEM7QUFDNEI7QUFDRjtBQUUxQyxTQUFTdUI7SUFDdEIsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUd6QiwrQ0FBUUEsQ0FBZTtJQUNqRCxNQUFNLENBQUMwQixRQUFRQyxVQUFVLEdBQUczQiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQ3BELE1BQU0sQ0FBQzRCLFNBQVNDLFdBQVcsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzhCLFFBQVFDLFVBQVUsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ2dDLGlCQUFpQkMsbUJBQW1CLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNrQyxjQUFjQyxnQkFBZ0IsR0FBR25DLCtDQUFRQSxDQUFtQjtJQUNuRSxNQUFNLENBQUNvQyxhQUFhQyxlQUFlLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNzQyxjQUFjQyxnQkFBZ0IsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3dDLFdBQVdDLGFBQWEsR0FBR3pDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0wQyxTQUFTeEMsMERBQVNBO0lBQ3hCLE1BQU15QyxTQUFTeEMsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRXlDLEtBQUssRUFBRSxHQUFHOUIsMERBQVFBO0lBRTFCLE1BQU0rQixVQUFVRixPQUFPRSxPQUFPO0lBRTlCNUMsZ0RBQVNBO2dDQUFDO1lBQ1I2QztRQUNGOytCQUFHO1FBQUNEO0tBQVE7SUFFWjVDLGdEQUFTQTtnQ0FBQztZQUNSLDZCQUE2QjtZQUM3QixNQUFNOEMsUUFBUXJCLE9BQU9zQixNQUFNOzhDQUFDLENBQUNDLEtBQUtDLFFBQVVELE1BQU9DLENBQUFBLE1BQU1WLFNBQVMsSUFBSTs2Q0FBSTtZQUMxRUMsYUFBYU07UUFDZjsrQkFBRztRQUFDckI7S0FBTztJQUVYLE1BQU1vQixzQkFBc0I7UUFDMUIsSUFBSTtZQUNGLHdCQUF3QjtZQUN4QixNQUFNSyxnQkFBZ0IsTUFBTS9CLDBDQUFHQSxDQUFDZ0MsUUFBUSxDQUFDUDtZQUN6QyxJQUFJTSxjQUFjRSxPQUFPLElBQUlGLGNBQWNHLElBQUksRUFBRTtnQkFDL0MsTUFBTUMsWUFBWUosY0FBY0csSUFBSTtnQkFDcEM3QixTQUFTOEI7Z0JBRVQsNkNBQTZDO2dCQUM3QyxJQUFJQSxVQUFVQyxpQkFBaUIsSUFBSUQsVUFBVUMsaUJBQWlCLENBQUNDLE1BQU0sR0FBRyxHQUFHO29CQUN6RTlCLFVBQVU0QixVQUFVQyxpQkFBaUI7Z0JBQ3ZDLE9BQU87b0JBQ0wsa0NBQWtDO29CQUNsQyxNQUFNRTtnQkFDUjtZQUNGO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RmLE1BQU07Z0JBQ0pnQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1JqQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU02QixrQkFBa0I7UUFDdEIsSUFBSTtZQUNGSyxRQUFRQyxHQUFHLENBQUMsc0RBQXNEbkI7WUFFbEUsdURBQXVEO1lBQ3ZELE1BQU1vQixXQUFXLE1BQU03QywwQ0FBR0EsQ0FBQzhDLHlCQUF5QixDQUFDckI7WUFDckRrQixRQUFRQyxHQUFHLENBQUMsOEJBQThCQztZQUUxQyxJQUFJQSxTQUFTWixPQUFPLElBQUlZLFNBQVNULGlCQUFpQixFQUFFO2dCQUNsRE8sUUFBUUMsR0FBRyxDQUFDLG9EQUFvREMsU0FBU1QsaUJBQWlCLENBQUNDLE1BQU0sRUFBRTtnQkFDbkc5QixVQUFVc0MsU0FBU1QsaUJBQWlCO2dCQUVwQ1osTUFBTTtvQkFDSmdCLE9BQU87b0JBQ1BDLGFBQWEsQ0FBQyxVQUFVLEVBQUVJLFNBQVNULGlCQUFpQixDQUFDQyxNQUFNLENBQUMsc0NBQXNDLENBQUM7Z0JBQ3JHO2dCQUNBO1lBQ0Y7WUFFQU0sUUFBUUMsR0FBRyxDQUFDO1lBQ1osa0RBQWtEO1lBQ2xELE1BQU1HLGFBQWE7Z0JBQ2pCO29CQUNFQyxJQUFJO29CQUNKQyxNQUFNO29CQUNOQyxTQUNFO29CQUNGQyxVQUFVO29CQUNWL0IsV0FBVztnQkFDYjtnQkFDQTtvQkFDRTRCLElBQUk7b0JBQ0pDLE1BQU07b0JBQ05HLFdBQVc7b0JBQ1hDLEtBQUs7b0JBQ0xGLFVBQVU7Z0JBQ1o7Z0JBQ0E7b0JBQ0VILElBQUk7b0JBQ0pDLE1BQU07b0JBQ05LLElBQUk7b0JBQ0pKLFNBQ0U7b0JBQ0ZDLFVBQVU7b0JBQ1YvQixXQUFXO2dCQUNiO2dCQUNBO29CQUNFNEIsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkssSUFBSTtvQkFDSkosU0FDRTtvQkFDRkMsVUFBVTtvQkFDVi9CLFdBQVc7Z0JBQ2I7Z0JBQ0E7b0JBQ0U0QixJQUFJO29CQUNKQyxNQUFNO29CQUNORyxXQUFXO29CQUNYQyxLQUFLO29CQUNMRixVQUFVO2dCQUNaO2dCQUNBO29CQUNFSCxJQUFJO29CQUNKQyxNQUFNO29CQUNOSyxJQUFJO29CQUNKSixTQUNFO29CQUNGQyxVQUFVO29CQUNWL0IsV0FBVztnQkFDYjtnQkFDQTtvQkFDRTRCLElBQUk7b0JBQ0pDLE1BQU07b0JBQ05LLElBQUk7b0JBQ0pKLFNBQ0U7b0JBQ0ZDLFVBQVU7b0JBQ1YvQixXQUFXO2dCQUNiO2dCQUNBO29CQUNFNEIsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkcsV0FBVztvQkFDWEMsS0FBSztvQkFDTEYsVUFBVTtnQkFDWjtnQkFDQTtvQkFDRUgsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkMsU0FDRTtvQkFDRkMsVUFBVTtvQkFDVi9CLFdBQVc7Z0JBQ2I7Z0JBQ0E7b0JBQ0U0QixJQUFJO29CQUNKQyxNQUFNO29CQUNOQyxTQUNFO29CQUNGQyxVQUFVO29CQUNWL0IsV0FBVztnQkFDYjthQUNEO1lBRUR1QixRQUFRQyxHQUFHLENBQUM7WUFDWnJDLFVBQVV3QztZQUVWdkIsTUFBTTtnQkFDSmdCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT0gsT0FBTztZQUNkSSxRQUFRSixLQUFLLENBQUMseUNBQXlDQTtZQUN2RGYsTUFBTTtnQkFDSmdCLE9BQU87Z0JBQ1BDLGFBQWEsQ0FBQyxpQ0FBaUMsRUFBRUYsTUFBTWdCLE9BQU8sRUFBRTtnQkFDaEViLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUmpDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTStDLGtCQUFrQjtRQUN0QjdDLFVBQVU7UUFDVixJQUFJO1lBQ0YsTUFBTVgsMENBQUdBLENBQUN5RCxTQUFTLENBQUNoQyxTQUFTO2dCQUMzQlcsbUJBQW1COUI7Z0JBQ25Cb0QsUUFBUTtZQUNWO1lBQ0FsQyxNQUFNO2dCQUNKZ0IsT0FBTztnQkFDUEMsYUFBYTtZQUNmO1FBQ0YsRUFBRSxPQUFPRixPQUFPO1lBQ2RmLE1BQU07Z0JBQ0pnQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1IvQixVQUFVO1FBQ1o7SUFDRjtJQUVBLE1BQU1nRCxzQkFBc0I7UUFDMUI5QyxtQkFBbUI7UUFDbkIsSUFBSTtZQUNGLE1BQU1iLDBDQUFHQSxDQUFDNEQsYUFBYSxDQUFDbkM7WUFDeEJELE1BQU07Z0JBQ0pnQixPQUFPO2dCQUNQQyxhQUFhO1lBQ2Y7WUFDQW5CLE9BQU91QyxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUVwQyxRQUFRLE9BQU8sQ0FBQztRQUN2QyxFQUFFLE9BQU9jLE9BQU87WUFDZGYsTUFBTTtnQkFDSmdCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUjdCLG1CQUFtQjtRQUNyQjtJQUNGO0lBRUEsTUFBTWlELGtCQUFrQixDQUFDaEM7UUFDdkJmLGdCQUFnQmU7UUFDaEJiLGVBQWVhLE1BQU1vQixPQUFPLElBQUk7UUFDaEMvQixnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNNEMsd0JBQXdCLE9BQU9DLFNBQWlCZjtRQUNwRE4sUUFBUUMsR0FBRyxDQUFDLENBQUMsaUNBQWlDLEVBQUVvQixRQUFRLFdBQVcsRUFBRWYsS0FBSyxJQUFJLENBQUM7UUFDL0VOLFFBQVFDLEdBQUcsQ0FBQyxhQUFhbkI7UUFDekJrQixRQUFRQyxHQUFHLENBQUMsa0JBQWtCSyxTQUFTLE9BQU8vQixlQUFlK0M7UUFDN0R0QixRQUFRQyxHQUFHLENBQUMsaUJBQWlCSyxTQUFTLFdBQVdqQyxjQUFjaUQ7UUFFL0QsSUFBSTtZQUNGLE1BQU1wQixXQUFXLE1BQU03QywwQ0FBR0EsQ0FBQ2tFLGVBQWUsQ0FDeEN6QyxTQUNBdUMsU0FDQWYsTUFDQUEsU0FBUyxPQUFPL0IsZUFBZStDLFdBQy9CaEIsU0FBUyxXQUFXakMsY0FBY2lEO1lBR3BDdEIsUUFBUUMsR0FBRyxDQUFDLDJDQUEyQ0M7WUFFdkQsSUFBSUEsU0FBU1osT0FBTyxJQUFJWSxTQUFTZixLQUFLLEVBQUU7Z0JBQ3RDdkIsVUFDRUQsT0FBTzZELEdBQUcsQ0FBQyxDQUFDckMsUUFDVkEsTUFBTWtCLEVBQUUsS0FBS2dCLFVBQVU7d0JBQUUsR0FBR2xDLEtBQUs7d0JBQUUsR0FBR2UsU0FBU2YsS0FBSztvQkFBQyxJQUFJQTtnQkFJN0RmLGdCQUFnQjtnQkFDaEJTLE1BQU07b0JBQ0pnQixPQUFPO29CQUNQQyxhQUFhO2dCQUNmO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUkyQixNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPN0IsT0FBTztZQUNkSSxRQUFRSixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQ2YsTUFBTTtnQkFDSmdCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNMkIsb0JBQW9CLENBQUNMO1FBQ3pCekQsVUFBVUQsT0FBT2dFLE1BQU0sQ0FBQyxDQUFDeEMsUUFBVUEsTUFBTWtCLEVBQUUsS0FBS2dCO1FBQ2hEeEMsTUFBTTtZQUNKZ0IsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU04QixzQkFBc0IsT0FBT1AsU0FBaUJ2QjtRQUNsREUsUUFBUUMsR0FBRyxDQUFDLENBQUMsMkJBQTJCLEVBQUVvQixRQUFRLG1CQUFtQixFQUFFdkIsYUFBYTtRQUNwRixJQUFJO1lBQ0YsTUFBTUksV0FBVyxNQUFNN0MsMENBQUdBLENBQUN3RSxhQUFhLENBQUMvQyxTQUFTdUMsU0FBU3ZCO1lBRTNELElBQUlJLFNBQVNaLE9BQU8sSUFBSVksU0FBU2YsS0FBSyxFQUFFO2dCQUN0Q3ZCLFVBQVVELE9BQU82RCxHQUFHLENBQUNyQyxDQUFBQSxRQUNuQkEsTUFBTWtCLEVBQUUsS0FBS2dCLFVBQVU7d0JBQUUsR0FBR2xDLEtBQUs7d0JBQUUsR0FBR2UsU0FBU2YsS0FBSztvQkFBQyxJQUFJQTtnQkFHM0ROLE1BQU07b0JBQ0pnQixPQUFPO29CQUNQQyxhQUFhO2dCQUNmO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUkyQixNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPN0IsT0FBTztZQUNkSSxRQUFRSixLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q2YsTUFBTTtnQkFDSmdCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNK0Isb0JBQW9CLE9BQU9ULFNBQWlCVTtRQUNoRC9CLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixFQUFFb0IsUUFBUSxDQUFDLENBQUMsRUFBRVUsS0FBS0MsSUFBSTtRQUM5RCxJQUFJO1lBQ0YsTUFBTTlCLFdBQVcsTUFBTTdDLDBDQUFHQSxDQUFDNEUsV0FBVyxDQUFDbkQsU0FBU3VDLFNBQVNVO1lBRXpELElBQUk3QixTQUFTWixPQUFPLElBQUlZLFNBQVNmLEtBQUssRUFBRTtnQkFDdEN2QixVQUFVRCxPQUFPNkQsR0FBRyxDQUFDckMsQ0FBQUEsUUFDbkJBLE1BQU1rQixFQUFFLEtBQUtnQixVQUFVO3dCQUFFLEdBQUdsQyxLQUFLO3dCQUFFLEdBQUdlLFNBQVNmLEtBQUs7b0JBQUMsSUFBSUE7Z0JBRzNETixNQUFNO29CQUNKZ0IsT0FBTztvQkFDUEMsYUFBYTtnQkFDZjtZQUNGLE9BQU87Z0JBQ0wsTUFBTSxJQUFJMkIsTUFBTTtZQUNsQjtRQUNGLEVBQUUsT0FBTzdCLE9BQU87WUFDZEksUUFBUUosS0FBSyxDQUFDLDBCQUEwQkE7WUFDeENmLE1BQU07Z0JBQ0pnQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsSUFBSWxDLFNBQVM7UUFDWCxxQkFDRSw4REFBQ3FFO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDN0Usc0VBQWFBO29CQUFDOEUsYUFBYTtvQkFBR3RELFNBQVNBOzs7Ozs7OEJBQ3hDLDhEQUFDdUQ7b0JBQUtGLFdBQVU7OEJBQ2QsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3JGLDZEQUFRQTtnQ0FBQ3FGLFdBQVU7Ozs7Ozs0QkFDbkI7Z0NBQUM7Z0NBQUc7Z0NBQUc7Z0NBQUc7Z0NBQUc7NkJBQUUsQ0FBQ1gsR0FBRyxDQUFDLENBQUNjLGtCQUNwQiw4REFBQ3hGLDZEQUFRQTtvQ0FBU3FGLFdBQVU7bUNBQWJHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTTNCO0lBRUEscUJBQ0UsOERBQUNKO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDN0Usc0VBQWFBO2dCQUFDOEUsYUFBYTtnQkFBR3RELFNBQVNBOzs7Ozs7MEJBR3hDLDhEQUFDb0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSTtvQ0FBR0osV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDdEMsOERBQUM3Rix1REFBS0E7b0NBQUN5RCxTQUFRO29DQUFVb0MsV0FBVTs7c0RBQ2pDLDhEQUFDL0UsNkdBQVFBOzRDQUFDK0UsV0FBVTs7Ozs7O3dDQUNuQjFEO3dDQUFVOzs7Ozs7Ozs7Ozs7O3NDQUlmLDhEQUFDeUQ7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDOUYseURBQU1BO29DQUFDbUcsU0FBUzNCO29DQUFpQjRCLFVBQVUxRTtvQ0FBUWdDLFNBQVE7b0NBQVUyQyxNQUFLOztzREFDekUsOERBQUMxRiw2R0FBSUE7NENBQUNtRixXQUFVOzs7Ozs7d0NBQ2ZwRSxTQUFTLGNBQWM7Ozs7Ozs7OENBRzFCLDhEQUFDMUIseURBQU1BO29DQUFDMEQsU0FBUTtvQ0FBVTJDLE1BQUs7O3NEQUM3Qiw4REFBQ3pGLDZHQUFHQTs0Q0FBQ2tGLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7OENBSWxDLDhEQUFDOUYseURBQU1BO29DQUNMbUcsU0FBU3hCO29DQUNUeUIsVUFBVXhFO29DQUNWOEIsU0FBUTtvQ0FDUjJDLE1BQUs7O3NEQUVMLDhEQUFDeEYsNkdBQUlBOzRDQUFDaUYsV0FBVTs7Ozs7O3dDQUNmbEUsa0JBQWtCLGtCQUFrQjs7Ozs7Ozs4Q0FHdkMsOERBQUM1Qix5REFBTUE7b0NBQ0xtRyxTQUFTLElBQU03RCxPQUFPdUMsSUFBSSxDQUFDLENBQUMsTUFBTSxFQUFFcEMsUUFBUSxPQUFPLENBQUM7b0NBQ3BEcUQsV0FBVTtvQ0FDVk8sTUFBSzs7c0RBRUwsOERBQUNDOzRDQUFJUixXQUFVOzRDQUFlUyxNQUFLOzRDQUFlQyxTQUFRO3NEQUN4RCw0RUFBQ0M7Z0RBQUtDLFVBQVM7Z0RBQVVDLEdBQUU7Z0RBQXdMQyxVQUFTOzs7Ozs7Ozs7Ozt3Q0FDeE47Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPZCw4REFBQ1o7Z0JBQUtGLFdBQVU7O2tDQUNkLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWnhFLE9BQU82RCxHQUFHLENBQUMsQ0FBQ3JDLE9BQU8rRCxzQkFDbEIsOERBQUMzRixvRUFBWUE7Z0NBRVg0QixPQUFPQTtnQ0FDUGdFLFFBQVEsSUFBTWhDLGdCQUFnQmhDO2dDQUM5QmlFLGNBQWMsSUFBTWhDLHNCQUFzQmpDLE1BQU1rQixFQUFFLEVBQUU7Z0NBQ3BEZ0QsVUFBVWxFLE1BQU1xQixRQUFRLEdBQUcsSUFBTWtCLGtCQUFrQnZDLE1BQU1rQixFQUFFLElBQUlpQjtnQ0FDL0RnQyxpQkFBaUIxQjtnQ0FDakIyQixlQUFlekI7K0JBTlYzQyxNQUFNa0IsRUFBRTs7Ozs7Ozs7OztrQ0FZbkIsOERBQUM1RCx5REFBTUE7d0JBQUMrRyxNQUFNLENBQUMsQ0FBQ3JGO3dCQUFjc0YsY0FBYyxJQUFNckYsZ0JBQWdCO2tDQUNoRSw0RUFBQzFCLGdFQUFhQTs0QkFBQ3lGLFdBQVU7OzhDQUN2Qiw4REFBQ3ZGLCtEQUFZQTs7c0RBQ1gsOERBQUNDLDhEQUFXQTtzREFBQzs7Ozs7O3NEQUNiLDhEQUFDRixvRUFBaUJBO3NEQUFDOzs7Ozs7Ozs7Ozs7OENBS3JCLDhEQUFDdUY7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUN3QjtvREFBTXZCLFdBQVU7OERBQWlDOzs7Ozs7OERBQ2xELDhEQUFDM0YsNkRBQVFBO29EQUNQbUgsT0FBT3RGO29EQUNQdUYsVUFBVSxDQUFDQyxJQUFNdkYsZUFBZXVGLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvREFDOUNJLE1BQU07b0RBQ041QixXQUFVOzs7Ozs7Ozs7Ozs7c0RBSWQsOERBQUNEOzs4REFDQyw4REFBQ3dCO29EQUFNdkIsV0FBVTs4REFBaUM7Ozs7Ozs4REFDbEQsOERBQUM1Rix1REFBS0E7b0RBQ0pvSCxPQUFPcEY7b0RBQ1BxRixVQUFVLENBQUNDLElBQU1yRixnQkFBZ0JxRixFQUFFQyxNQUFNLENBQUNILEtBQUs7b0RBQy9DSyxhQUFZOzs7Ozs7Ozs7Ozs7c0RBSWhCLDhEQUFDOUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDOUYseURBQU1BO29EQUFDbUcsU0FBUyxJQUFNcEIsc0JBQXNCakQsY0FBY2tDLE1BQU0sSUFBSTtvREFBVzhCLFdBQVU7OERBQVM7Ozs7Ozs4REFJbkcsOERBQUM5Rix5REFBTUE7b0RBQ0xtRyxTQUFTLElBQU1wQixzQkFBc0JqRCxjQUFja0MsTUFBTSxJQUFJO29EQUM3RG9DLFVBQVUsQ0FBQ2xFO29EQUNYd0IsU0FBUTtvREFDUm9DLFdBQVU7O3NFQUVWLDhEQUFDaEYsNkdBQVNBOzREQUFDZ0YsV0FBVTs7Ozs7O3dEQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXhEIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxhcHBcXGJsb2dcXFtkcmFmdElkXVxcZWRpdG9yXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlUGFyYW1zIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXG5pbXBvcnQgeyBEaWFsb2csIERpYWxvZ0NvbnRlbnQsIERpYWxvZ0Rlc2NyaXB0aW9uLCBEaWFsb2dIZWFkZXIsIERpYWxvZ1RpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIlxuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NrZWxldG9uXCJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCJcbmltcG9ydCB7IFNhdmUsIEV5ZSwgTGluaywgUmVmcmVzaEN3LCBGaWxlVGV4dCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHR5cGUgeyBCbG9nQmxvY2ssIERyYWZ0IH0gZnJvbSBcIkAvdHlwZXMvYXBpXCJcbmltcG9ydCB7IGFwaSB9IGZyb20gXCJAL2xpYi9hcGlcIlxuaW1wb3J0IHsgU3RlcHBlckhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvc3RlcHBlci1oZWFkZXJcIlxuaW1wb3J0IHsgQ29udGVudEJsb2NrIH0gZnJvbSBcIkAvY29tcG9uZW50cy9jb250ZW50LWJsb2NrXCJcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRWRpdG9yUGFnZSgpIHtcbiAgY29uc3QgW2RyYWZ0LCBzZXREcmFmdF0gPSB1c2VTdGF0ZTxEcmFmdCB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtibG9ja3MsIHNldEJsb2Nrc10gPSB1c2VTdGF0ZTxCbG9nQmxvY2tbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzYXZpbmcsIHNldFNhdmluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2dlbmVyYXRpbmdMaW5rcywgc2V0R2VuZXJhdGluZ0xpbmtzXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZWRpdGluZ0Jsb2NrLCBzZXRFZGl0aW5nQmxvY2tdID0gdXNlU3RhdGU8QmxvZ0Jsb2NrIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2VkaXRDb250ZW50LCBzZXRFZGl0Q29udGVudF0gPSB1c2VTdGF0ZShcIlwiKVxuICBjb25zdCBbY3VzdG9tUHJvbXB0LCBzZXRDdXN0b21Qcm9tcHRdID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW3dvcmRDb3VudCwgc2V0V29yZENvdW50XSA9IHVzZVN0YXRlKDApXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICBjb25zdCBkcmFmdElkID0gcGFyYW1zLmRyYWZ0SWQgYXMgc3RyaW5nXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkRHJhZnRBbmRDb250ZW50KClcbiAgfSwgW2RyYWZ0SWRdKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gQ2FsY3VsYXRlIHRvdGFsIHdvcmQgY291bnRcbiAgICBjb25zdCB0b3RhbCA9IGJsb2Nrcy5yZWR1Y2UoKHN1bSwgYmxvY2spID0+IHN1bSArIChibG9jay53b3JkQ291bnQgfHwgMCksIDApXG4gICAgc2V0V29yZENvdW50KHRvdGFsKVxuICB9LCBbYmxvY2tzXSlcblxuICBjb25zdCBsb2FkRHJhZnRBbmRDb250ZW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBMb2FkIGRyYWZ0IGRhdGEgZmlyc3RcbiAgICAgIGNvbnN0IGRyYWZ0UmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0RHJhZnQoZHJhZnRJZClcbiAgICAgIGlmIChkcmFmdFJlc3BvbnNlLnN1Y2Nlc3MgJiYgZHJhZnRSZXNwb25zZS5kYXRhKSB7XG4gICAgICAgIGNvbnN0IGRyYWZ0RGF0YSA9IGRyYWZ0UmVzcG9uc2UuZGF0YVxuICAgICAgICBzZXREcmFmdChkcmFmdERhdGEpXG5cbiAgICAgICAgLy8gQ2hlY2sgaWYgc3RydWN0dXJlZCBjb250ZW50IGFscmVhZHkgZXhpc3RzXG4gICAgICAgIGlmIChkcmFmdERhdGEuc3RydWN0dXJlZENvbnRlbnQgJiYgZHJhZnREYXRhLnN0cnVjdHVyZWRDb250ZW50Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBzZXRCbG9ja3MoZHJhZnREYXRhLnN0cnVjdHVyZWRDb250ZW50KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIEdlbmVyYXRlIG5ldyBzdHJ1Y3R1cmVkIGNvbnRlbnRcbiAgICAgICAgICBhd2FpdCBnZW5lcmF0ZUNvbnRlbnQoKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRXJyb3IgbG9hZGluZyBkcmFmdFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gbG9hZCBkcmFmdCBkYXRhLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdlbmVyYXRlQ29udGVudCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CfmoAgRlJPTlRFTkQ6IEdlbmVyYXRpbmcgcmVhbCBBSSBjb250ZW50IGZvciBkcmFmdDonLCBkcmFmdElkKVxuXG4gICAgICAvLyBDYWxsIGJhY2tlbmQgQVBJIHRvIGdlbmVyYXRlIHJlYWwgc3RydWN0dXJlZCBjb250ZW50XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZW5lcmF0ZVN0cnVjdHVyZWRDb250ZW50KGRyYWZ0SWQpXG4gICAgICBjb25zb2xlLmxvZygn8J+ToSBGUk9OVEVORDogQVBJIFJlc3BvbnNlOicsIHJlc3BvbnNlKVxuXG4gICAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5zdHJ1Y3R1cmVkQ29udGVudCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIEZST05URU5EOiBVc2luZyByZWFsIEFJLWdlbmVyYXRlZCBjb250ZW50IHdpdGgnLCByZXNwb25zZS5zdHJ1Y3R1cmVkQ29udGVudC5sZW5ndGgsICdibG9ja3MnKVxuICAgICAgICBzZXRCbG9ja3MocmVzcG9uc2Uuc3RydWN0dXJlZENvbnRlbnQpXG5cbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcIkFJIENvbnRlbnQgZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSFcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYEdlbmVyYXRlZCAke3Jlc3BvbnNlLnN0cnVjdHVyZWRDb250ZW50Lmxlbmd0aH0gY29udGVudCBibG9ja3Mgd2l0aCBHZW1pbmkgMi4wIEZsYXNoLmAsXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn4pqg77iPIEZST05URU5EOiBBUEkgcmVzcG9uc2UgaW52YWxpZCwgdXNpbmcgZmFsbGJhY2sgY29udGVudCcpXG4gICAgICAvLyBGYWxsYmFjazogTW9jayBibG9nIGNvbnRlbnQgYmxvY2tzIGlmIEFQSSBmYWlsc1xuICAgICAgY29uc3QgbW9ja0Jsb2NrcyA9IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcImludHJvLTFcIixcbiAgICAgICAgICB0eXBlOiBcImludHJvZHVjdGlvblwiIGFzIGNvbnN0LFxuICAgICAgICAgIGNvbnRlbnQ6XG4gICAgICAgICAgICBcIkluIHRvZGF5J3MgZmFzdC1wYWNlZCBidXNpbmVzcyBlbnZpcm9ubWVudCwgc21hbGwgYnVzaW5lc3Mgb3duZXJzIGFyZSBjb25zdGFudGx5IGxvb2tpbmcgZm9yIHdheXMgdG8gc3RyZWFtbGluZSBvcGVyYXRpb25zLCByZWR1Y2UgbWFudWFsIHRhc2tzLCBhbmQgZm9jdXMgb24gd2hhdCBtYXR0ZXJzIG1vc3QgLSBncm93aW5nIHRoZWlyIGJ1c2luZXNzLiBBSSBhdXRvbWF0aW9uIHRvb2xzIGhhdmUgZW1lcmdlZCBhcyBnYW1lLWNoYW5nZXJzLCBvZmZlcmluZyBzb3BoaXN0aWNhdGVkIHNvbHV0aW9ucyB0aGF0IHdlcmUgb25jZSBvbmx5IGF2YWlsYWJsZSB0byBsYXJnZSBlbnRlcnByaXNlcy5cXG5cXG5UaGlzIGNvbXByZWhlbnNpdmUgZ3VpZGUgd2lsbCB3YWxrIHlvdSB0aHJvdWdoIHRoZSBiZXN0IEFJIGF1dG9tYXRpb24gdG9vbHMgc3BlY2lmaWNhbGx5IGRlc2lnbmVkIGZvciBzbWFsbCBidXNpbmVzc2VzLCBoZWxwaW5nIHlvdSB1bmRlcnN0YW5kIGhvdyB0byBpbXBsZW1lbnQgdGhlbSBlZmZlY3RpdmVseSBhbmQgbWF4aW1pemUgeW91ciByZXR1cm4gb24gaW52ZXN0bWVudC4gV2hldGhlciB5b3UncmUgbG9va2luZyB0byBhdXRvbWF0ZSBjdXN0b21lciBzZXJ2aWNlLCBtYXJrZXRpbmcgY2FtcGFpZ25zLCBvciBpbnRlcm5hbCBwcm9jZXNzZXMsIHdlJ3ZlIGdvdCB5b3UgY292ZXJlZC5cIixcbiAgICAgICAgICBlZGl0YWJsZTogdHJ1ZSxcbiAgICAgICAgICB3b3JkQ291bnQ6IDEyNCxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcImZlYXR1cmUtaW1nLTFcIixcbiAgICAgICAgICB0eXBlOiBcImltYWdlXCIgYXMgY29uc3QsXG4gICAgICAgICAgaW1hZ2VUeXBlOiBcImZlYXR1cmVcIiBhcyBjb25zdCxcbiAgICAgICAgICBhbHQ6IFwiQUkgYXV0b21hdGlvbiB0b29scyBkYXNoYm9hcmQgc2hvd2luZyB2YXJpb3VzIGJ1c2luZXNzIHByb2Nlc3NlcyBiZWluZyBhdXRvbWF0ZWRcIixcbiAgICAgICAgICBlZGl0YWJsZTogZmFsc2UsXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogXCJzZWN0aW9uLTFcIixcbiAgICAgICAgICB0eXBlOiBcInNlY3Rpb25cIiBhcyBjb25zdCxcbiAgICAgICAgICBoMjogXCJXaGF0IEFyZSBBSSBBdXRvbWF0aW9uIFRvb2xzIGFuZCBXaHkgRG8gU21hbGwgQnVzaW5lc3NlcyBOZWVkIFRoZW0/XCIsXG4gICAgICAgICAgY29udGVudDpcbiAgICAgICAgICAgIFwiQUkgYXV0b21hdGlvbiB0b29scyBhcmUgc29mdHdhcmUgc29sdXRpb25zIHRoYXQgdXNlIGFydGlmaWNpYWwgaW50ZWxsaWdlbmNlIHRvIHBlcmZvcm0gdGFza3MgdGhhdCB0eXBpY2FsbHkgcmVxdWlyZSBodW1hbiBpbnRlcnZlbnRpb24uIFRoZXNlIHRvb2xzIGNhbiBhbmFseXplIGRhdGEsIG1ha2UgZGVjaXNpb25zLCBhbmQgZXhlY3V0ZSBhY3Rpb25zIGJhc2VkIG9uIHByZWRlZmluZWQgcnVsZXMgb3IgbGVhcm5lZCBwYXR0ZXJucy5cXG5cXG5Gb3Igc21hbGwgYnVzaW5lc3NlcywgQUkgYXV0b21hdGlvbiBvZmZlcnMgc2V2ZXJhbCBrZXkgYmVuZWZpdHM6XFxuXFxu4oCiICoqQ29zdCBSZWR1Y3Rpb24qKjogQXV0b21hdGUgcmVwZXRpdGl2ZSB0YXNrcyB0byByZWR1Y2UgbGFib3IgY29zdHNcXG7igKIgKipJbXByb3ZlZCBBY2N1cmFjeSoqOiBNaW5pbWl6ZSBodW1hbiBlcnJvcnMgaW4gZGF0YSBwcm9jZXNzaW5nIGFuZCBhbmFseXNpc1xcbuKAoiAqKjI0LzcgT3BlcmF0aW9ucyoqOiBLZWVwIGNyaXRpY2FsIHByb2Nlc3NlcyBydW5uaW5nIGFyb3VuZCB0aGUgY2xvY2tcXG7igKIgKipTY2FsYWJpbGl0eSoqOiBIYW5kbGUgaW5jcmVhc2VkIHdvcmtsb2FkIHdpdGhvdXQgcHJvcG9ydGlvbmFsIHN0YWZmIGluY3JlYXNlc1xcbuKAoiAqKkNvbXBldGl0aXZlIEFkdmFudGFnZSoqOiBBY2Nlc3MgZW50ZXJwcmlzZS1sZXZlbCBjYXBhYmlsaXRpZXMgYXQgYWZmb3JkYWJsZSBwcmljZXNcXG5cXG5UaGUga2V5IGlzIGNob29zaW5nIHRoZSByaWdodCB0b29scyB0aGF0IGFsaWduIHdpdGggeW91ciBzcGVjaWZpYyBidXNpbmVzcyBuZWVkcyBhbmQgYnVkZ2V0IGNvbnN0cmFpbnRzLlwiLFxuICAgICAgICAgIGVkaXRhYmxlOiB0cnVlLFxuICAgICAgICAgIHdvcmRDb3VudDogMTU2LFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6IFwic2VjdGlvbi0yXCIsXG4gICAgICAgICAgdHlwZTogXCJzZWN0aW9uXCIgYXMgY29uc3QsXG4gICAgICAgICAgaDI6IFwiVG9wIDUgQUkgQXV0b21hdGlvbiBUb29scyBmb3IgU21hbGwgQnVzaW5lc3MgT3BlcmF0aW9uc1wiLFxuICAgICAgICAgIGNvbnRlbnQ6XG4gICAgICAgICAgICBcIkFmdGVyIGV4dGVuc2l2ZSByZXNlYXJjaCBhbmQgdGVzdGluZywgd2UndmUgaWRlbnRpZmllZCB0aGUgdG9wIEFJIGF1dG9tYXRpb24gdG9vbHMgdGhhdCBkZWxpdmVyIGV4Y2VwdGlvbmFsIHZhbHVlIGZvciBzbWFsbCBidXNpbmVzc2VzOlxcblxcbioqMS4gWmFwaWVyIHdpdGggQUkgRmVhdHVyZXMqKlxcblphcGllcidzIEFJLXBvd2VyZWQgYXV0b21hdGlvbiBjb25uZWN0cyBvdmVyIDUsMDAwIGFwcHMsIG1ha2luZyBpdCBlYXN5IHRvIGNyZWF0ZSBjb21wbGV4IHdvcmtmbG93cyB3aXRob3V0IGNvZGluZy4gUmVjZW50IEFJIGVuaGFuY2VtZW50cyBpbmNsdWRlIHNtYXJ0IHN1Z2dlc3Rpb25zIGFuZCBuYXR1cmFsIGxhbmd1YWdlIHByb2Nlc3NpbmcgZm9yIHRyaWdnZXIgY3JlYXRpb24uXFxuXFxuKioyLiBIdWJTcG90J3MgQUktUG93ZXJlZCBDUk0qKlxcbkh1YlNwb3Qgb2ZmZXJzIEFJLWRyaXZlbiBsZWFkIHNjb3JpbmcsIHByZWRpY3RpdmUgYW5hbHl0aWNzLCBhbmQgYXV0b21hdGVkIGVtYWlsIHNlcXVlbmNlcy4gVGhlaXIgZnJlZSB0aWVyIG1ha2VzIGl0IGFjY2Vzc2libGUgZm9yIHN0YXJ0dXBzIGFuZCBncm93aW5nIGJ1c2luZXNzZXMuXFxuXFxuKiozLiBDaGF0ZnVlbCBmb3IgQ3VzdG9tZXIgU2VydmljZSoqXFxuVGhpcyBBSSBjaGF0Ym90IHBsYXRmb3JtIGhhbmRsZXMgY3VzdG9tZXIgaW5xdWlyaWVzIDI0LzcsIHJlZHVjaW5nIHJlc3BvbnNlIHRpbWVzIGFuZCBmcmVlaW5nIHVwIHlvdXIgdGVhbSBmb3IgbW9yZSBjb21wbGV4IHRhc2tzLlxcblxcbioqNC4gQ2FsZW5kbHkgd2l0aCBTbWFydCBTY2hlZHVsaW5nKipcXG5BSS1wb3dlcmVkIHNjaGVkdWxpbmcgdGhhdCBsZWFybnMgZnJvbSB5b3VyIHByZWZlcmVuY2VzIGFuZCBhdXRvbWF0aWNhbGx5IG9wdGltaXplcyBtZWV0aW5nIHRpbWVzIGJhc2VkIG9uIHBhcnRpY2lwYW50IGF2YWlsYWJpbGl0eSBhbmQgcHJlZmVyZW5jZXMuXFxuXFxuKio1LiBRdWlja0Jvb2tzIEFJIGZvciBGaW5hbmNpYWwgTWFuYWdlbWVudCoqXFxuQXV0b21hdGVkIGV4cGVuc2UgY2F0ZWdvcml6YXRpb24sIGludm9pY2UgcHJvY2Vzc2luZywgYW5kIGZpbmFuY2lhbCBmb3JlY2FzdGluZyBwb3dlcmVkIGJ5IG1hY2hpbmUgbGVhcm5pbmcgYWxnb3JpdGhtcy5cIixcbiAgICAgICAgICBlZGl0YWJsZTogdHJ1ZSxcbiAgICAgICAgICB3b3JkQ291bnQ6IDE4OSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcImluYmxvZy1pbWctMVwiLFxuICAgICAgICAgIHR5cGU6IFwiaW1hZ2VcIiBhcyBjb25zdCxcbiAgICAgICAgICBpbWFnZVR5cGU6IFwiaW4tYmxvZ1wiIGFzIGNvbnN0LFxuICAgICAgICAgIGFsdDogXCJDb21wYXJpc29uIGNoYXJ0IHNob3dpbmcgZGlmZmVyZW50IEFJIGF1dG9tYXRpb24gdG9vbHMgYW5kIHRoZWlyIGZlYXR1cmVzXCIsXG4gICAgICAgICAgZWRpdGFibGU6IGZhbHNlLFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6IFwic2VjdGlvbi0zXCIsXG4gICAgICAgICAgdHlwZTogXCJzZWN0aW9uXCIgYXMgY29uc3QsXG4gICAgICAgICAgaDI6IFwiSW1wbGVtZW50YXRpb24gU3RyYXRlZ3k6IEhvdyB0byBTdWNjZXNzZnVsbHkgRGVwbG95IEFJIEF1dG9tYXRpb25cIixcbiAgICAgICAgICBjb250ZW50OlxuICAgICAgICAgICAgXCJTdWNjZXNzZnVsbHkgaW1wbGVtZW50aW5nIEFJIGF1dG9tYXRpb24gaW4geW91ciBzbWFsbCBidXNpbmVzcyByZXF1aXJlcyBhIHN0cmF0ZWdpYyBhcHByb2FjaDpcXG5cXG4qKlBoYXNlIDE6IEFzc2Vzc21lbnQgYW5kIFBsYW5uaW5nIChXZWVrIDEtMikqKlxcbuKAoiBJZGVudGlmeSByZXBldGl0aXZlLCB0aW1lLWNvbnN1bWluZyB0YXNrc1xcbuKAoiBNYXAgY3VycmVudCB3b3JrZmxvd3MgYW5kIHByb2Nlc3Nlc1xcbuKAoiBTZXQgY2xlYXIgZ29hbHMgYW5kIHN1Y2Nlc3MgbWV0cmljc1xcbuKAoiBEZXRlcm1pbmUgYnVkZ2V0IGFuZCByZXNvdXJjZSBhbGxvY2F0aW9uXFxuXFxuKipQaGFzZSAyOiBUb29sIFNlbGVjdGlvbiBhbmQgU2V0dXAgKFdlZWsgMy00KSoqXFxu4oCiIFJlc2VhcmNoIGFuZCBjb21wYXJlIGF1dG9tYXRpb24gdG9vbHNcXG7igKIgU3RhcnQgd2l0aCBmcmVlIHRyaWFscyBvciBiYXNpYyBwbGFuc1xcbuKAoiBDb25maWd1cmUgaW5pdGlhbCBhdXRvbWF0aW9ucyBmb3IgbG93LXJpc2sgcHJvY2Vzc2VzXFxu4oCiIFRyYWluIHRlYW0gbWVtYmVycyBvbiBuZXcgdG9vbHNcXG5cXG4qKlBoYXNlIDM6IFRlc3RpbmcgYW5kIE9wdGltaXphdGlvbiAoV2VlayA1LTgpKipcXG7igKIgTW9uaXRvciBhdXRvbWF0aW9uIHBlcmZvcm1hbmNlIGNsb3NlbHlcXG7igKIgR2F0aGVyIGZlZWRiYWNrIGZyb20gdGVhbSBtZW1iZXJzXFxu4oCiIE1ha2UgYWRqdXN0bWVudHMgYW5kIGltcHJvdmVtZW50c1xcbuKAoiBHcmFkdWFsbHkgZXhwYW5kIHRvIG1vcmUgY29tcGxleCBwcm9jZXNzZXNcXG5cXG4qKlBoYXNlIDQ6IFNjYWxlIGFuZCBBZHZhbmNlZCBGZWF0dXJlcyAoTW9udGggMyspKipcXG7igKIgSW1wbGVtZW50IG1vcmUgc29waGlzdGljYXRlZCBhdXRvbWF0aW9uc1xcbuKAoiBJbnRlZ3JhdGUgbXVsdGlwbGUgdG9vbHMgZm9yIGNvbXByZWhlbnNpdmUgd29ya2Zsb3dzXFxu4oCiIEFuYWx5emUgUk9JIGFuZCBhZGp1c3Qgc3RyYXRlZ3kgYXMgbmVlZGVkXFxu4oCiIEV4cGxvcmUgYWR2YW5jZWQgQUkgZmVhdHVyZXMgYW5kIGNhcGFiaWxpdGllc1wiLFxuICAgICAgICAgIGVkaXRhYmxlOiB0cnVlLFxuICAgICAgICAgIHdvcmRDb3VudDogMTc4LFxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6IFwic2VjdGlvbi00XCIsXG4gICAgICAgICAgdHlwZTogXCJzZWN0aW9uXCIgYXMgY29uc3QsXG4gICAgICAgICAgaDI6IFwiTWVhc3VyaW5nIFJPSTogS2V5IE1ldHJpY3MgZm9yIEFJIEF1dG9tYXRpb24gU3VjY2Vzc1wiLFxuICAgICAgICAgIGNvbnRlbnQ6XG4gICAgICAgICAgICBcIlRvIGVuc3VyZSB5b3VyIEFJIGF1dG9tYXRpb24gaW52ZXN0bWVudCBwYXlzIG9mZiwgdHJhY2sgdGhlc2UgZXNzZW50aWFsIG1ldHJpY3M6XFxuXFxuKipUaW1lIFNhdmluZ3MgTWV0cmljczoqKlxcbuKAoiBIb3VycyBzYXZlZCBwZXIgd2VlayBvbiBhdXRvbWF0ZWQgdGFza3NcXG7igKIgUmVkdWN0aW9uIGluIG1hbnVhbCBkYXRhIGVudHJ5IHRpbWVcXG7igKIgRmFzdGVyIHJlc3BvbnNlIHRpbWVzIHRvIGN1c3RvbWVyIGlucXVpcmllc1xcblxcbioqQ29zdCBFZmZpY2llbmN5IE1ldHJpY3M6KipcXG7igKIgUmVkdWN0aW9uIGluIG9wZXJhdGlvbmFsIGNvc3RzXFxu4oCiIERlY3JlYXNlZCBuZWVkIGZvciBhZGRpdGlvbmFsIHN0YWZmXFxu4oCiIExvd2VyIGVycm9yIHJhdGVzIGFuZCBhc3NvY2lhdGVkIGNvc3RzXFxuXFxuKipCdXNpbmVzcyBHcm93dGggTWV0cmljczoqKlxcbuKAoiBJbmNyZWFzZWQgbGVhZCBnZW5lcmF0aW9uIGFuZCBjb252ZXJzaW9uIHJhdGVzXFxu4oCiIEltcHJvdmVkIGN1c3RvbWVyIHNhdGlzZmFjdGlvbiBzY29yZXNcXG7igKIgRW5oYW5jZWQgdGVhbSBwcm9kdWN0aXZpdHkgYW5kIGZvY3VzIG9uIHN0cmF0ZWdpYyB0YXNrc1xcblxcbioqUXVhbGl0eSBJbXByb3ZlbWVudHM6KipcXG7igKIgUmVkdWNlZCBlcnJvciByYXRlcyBpbiBkYXRhIHByb2Nlc3NpbmdcXG7igKIgTW9yZSBjb25zaXN0ZW50IGN1c3RvbWVyIGV4cGVyaWVuY2VzXFxu4oCiIEJldHRlciBjb21wbGlhbmNlIHdpdGggYnVzaW5lc3MgcHJvY2Vzc2VzXFxuXFxuTW9zdCBzbWFsbCBidXNpbmVzc2VzIHNlZSBhIHBvc2l0aXZlIFJPSSB3aXRoaW4gMy02IG1vbnRocyBvZiBpbXBsZW1lbnRpbmcgQUkgYXV0b21hdGlvbiB0b29scywgd2l0aCBzb21lIHJlcG9ydGluZyB1cCB0byAzMDAlIHJldHVybiBvbiBpbnZlc3RtZW50IHdpdGhpbiB0aGUgZmlyc3QgeWVhci5cIixcbiAgICAgICAgICBlZGl0YWJsZTogdHJ1ZSxcbiAgICAgICAgICB3b3JkQ291bnQ6IDE0NSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcImluYmxvZy1pbWctMlwiLFxuICAgICAgICAgIHR5cGU6IFwiaW1hZ2VcIiBhcyBjb25zdCxcbiAgICAgICAgICBpbWFnZVR5cGU6IFwiaW4tYmxvZ1wiIGFzIGNvbnN0LFxuICAgICAgICAgIGFsdDogXCJST0kgZGFzaGJvYXJkIHNob3dpbmcgcG9zaXRpdmUgcmV0dXJucyBmcm9tIEFJIGF1dG9tYXRpb24gaW1wbGVtZW50YXRpb25cIixcbiAgICAgICAgICBlZGl0YWJsZTogZmFsc2UsXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogXCJjb25jbHVzaW9uLTFcIixcbiAgICAgICAgICB0eXBlOiBcImNvbmNsdXNpb25cIiBhcyBjb25zdCxcbiAgICAgICAgICBjb250ZW50OlxuICAgICAgICAgICAgXCJBSSBhdXRvbWF0aW9uIHRvb2xzIGFyZSBubyBsb25nZXIgYSBsdXh1cnkgcmVzZXJ2ZWQgZm9yIGxhcmdlIGNvcnBvcmF0aW9ucyDigJMgdGhleSdyZSBlc3NlbnRpYWwgZm9yIHNtYWxsIGJ1c2luZXNzZXMgbG9va2luZyB0byBjb21wZXRlIGFuZCB0aHJpdmUgaW4gdG9kYXkncyBkaWdpdGFsIGxhbmRzY2FwZS4gQnkgc3RhcnRpbmcgd2l0aCBzaW1wbGUgYXV0b21hdGlvbnMgYW5kIGdyYWR1YWxseSBleHBhbmRpbmcgeW91ciBjYXBhYmlsaXRpZXMsIHlvdSBjYW4gdHJhbnNmb3JtIHlvdXIgb3BlcmF0aW9ucywgcmVkdWNlIGNvc3RzLCBhbmQgZm9jdXMgb24gd2hhdCB5b3UgZG8gYmVzdDogc2VydmluZyB5b3VyIGN1c3RvbWVycyBhbmQgZ3Jvd2luZyB5b3VyIGJ1c2luZXNzLlxcblxcblJlbWVtYmVyLCB0aGUga2V5IHRvIHN1Y2Nlc3NmdWwgQUkgYXV0b21hdGlvbiBpcyBzdGFydGluZyBzbWFsbCwgbWVhc3VyaW5nIHJlc3VsdHMsIGFuZCBjb250aW51b3VzbHkgb3B0aW1pemluZyB5b3VyIHByb2Nlc3Nlcy4gQ2hvb3NlIHRvb2xzIHRoYXQgaW50ZWdyYXRlIHdlbGwgd2l0aCB5b3VyIGV4aXN0aW5nIHN5c3RlbXMsIHByb3ZpZGUgZXhjZWxsZW50IHN1cHBvcnQsIGFuZCBvZmZlciByb29tIGZvciBncm93dGggYXMgeW91ciBidXNpbmVzcyBleHBhbmRzLlxcblxcblJlYWR5IHRvIGdldCBzdGFydGVkPyBCZWdpbiBieSBpZGVudGlmeWluZyBvbmUgcmVwZXRpdGl2ZSB0YXNrIGluIHlvdXIgYnVzaW5lc3MgYW5kIGV4cGxvcmUgaG93IEFJIGF1dG9tYXRpb24gY2FuIGhlbHAgeW91IHJlY2xhaW0gdGhvc2UgdmFsdWFibGUgaG91cnMgZm9yIG1vcmUgc3RyYXRlZ2ljIGFjdGl2aXRpZXMuXCIsXG4gICAgICAgICAgZWRpdGFibGU6IHRydWUsXG4gICAgICAgICAgd29yZENvdW50OiAxNDIsXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogXCJyZWZlcmVuY2VzLTFcIixcbiAgICAgICAgICB0eXBlOiBcInJlZmVyZW5jZXNcIiBhcyBjb25zdCxcbiAgICAgICAgICBjb250ZW50OlxuICAgICAgICAgICAgJzEuIE1jS2luc2V5IEdsb2JhbCBJbnN0aXR1dGUuICgyMDIzKS4gXCJUaGUgQWdlIG9mIEFJOiBBcnRpZmljaWFsIEludGVsbGlnZW5jZSBhbmQgdGhlIEZ1dHVyZSBvZiBXb3JrLlwiXFxuMi4gU21hbGwgQnVzaW5lc3MgQWRtaW5pc3RyYXRpb24uICgyMDIzKS4gXCJUZWNobm9sb2d5IEFkb3B0aW9uIGluIFNtYWxsIEJ1c2luZXNzZXM6IDIwMjMgUmVwb3J0LlwiXFxuMy4gWmFwaWVyLiAoMjAyMykuIFwiU3RhdGUgb2YgQnVzaW5lc3MgQXV0b21hdGlvbiBSZXBvcnQgMjAyMy5cIlxcbjQuIEh1YlNwb3QgUmVzZWFyY2guICgyMDIzKS4gXCJBSSBpbiBTbWFsbCBCdXNpbmVzczogQWRvcHRpb24gYW5kIEltcGFjdCBTdHVkeS5cIlxcbjUuIERlbG9pdHRlLiAoMjAyMykuIFwiQUkgYW5kIEF1dG9tYXRpb24gaW4gU21hbGwgYW5kIE1lZGl1bSBFbnRlcnByaXNlcy5cIicsXG4gICAgICAgICAgZWRpdGFibGU6IHRydWUsXG4gICAgICAgICAgd29yZENvdW50OiA2NyxcbiAgICAgICAgfSxcbiAgICAgIF1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfk50gRlJPTlRFTkQ6IFVzaW5nIGZhbGxiYWNrIG1vY2sgY29udGVudCcpXG4gICAgICBzZXRCbG9ja3MobW9ja0Jsb2NrcylcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJVc2luZyB0ZW1wbGF0ZSBjb250ZW50XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkFQSSBjb250ZW50IGdlbmVyYXRpb24gZmFpbGVkLCB1c2luZyB0ZW1wbGF0ZS4gQ2hlY2sgY29uc29sZSBmb3IgZGV0YWlscy5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEZST05URU5EOiBDb250ZW50IGdlbmVyYXRpb24gZXJyb3I6JywgZXJyb3IpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm9yIGdlbmVyYXRpbmcgY29udGVudFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogYEZhaWxlZCB0byBnZW5lcmF0ZSBibG9nIGNvbnRlbnQ6ICR7ZXJyb3IubWVzc2FnZX1gLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNhdmVEcmFmdCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRTYXZpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgYXdhaXQgYXBpLnNhdmVEcmFmdChkcmFmdElkLCB7XG4gICAgICAgIHN0cnVjdHVyZWRDb250ZW50OiBibG9ja3MsXG4gICAgICAgIHN0YXR1czogJ2NvbnRlbnRfZ2VuZXJhdGlvbidcbiAgICAgIH0pXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkRyYWZ0IHNhdmVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIllvdXIgYmxvZyBkcmFmdCBoYXMgYmVlbiBzYXZlZCBzdWNjZXNzZnVsbHkuXCIsXG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIlNhdmUgZmFpbGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBzYXZlIGRyYWZ0LiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTYXZpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlR2VuZXJhdGVMaW5rcyA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRHZW5lcmF0aW5nTGlua3ModHJ1ZSlcbiAgICB0cnkge1xuICAgICAgYXdhaXQgYXBpLmdlbmVyYXRlTGlua3MoZHJhZnRJZClcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiTGlua3MgZ2VuZXJhdGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkludGVybmFsIGFuZCBleHRlcm5hbCBsaW5rcyBoYXZlIGJlZW4gZ2VuZXJhdGVkLlwiLFxuICAgICAgfSlcbiAgICAgIHJvdXRlci5wdXNoKGAvYmxvZy8ke2RyYWZ0SWR9L3Jldmlld2ApXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiTGluayBnZW5lcmF0aW9uIGZhaWxlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gZ2VuZXJhdGUgbGlua3MuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEdlbmVyYXRpbmdMaW5rcyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0QmxvY2sgPSAoYmxvY2s6IEJsb2dCbG9jaykgPT4ge1xuICAgIHNldEVkaXRpbmdCbG9jayhibG9jaylcbiAgICBzZXRFZGl0Q29udGVudChibG9jay5jb250ZW50IHx8IFwiXCIpXG4gICAgc2V0Q3VzdG9tUHJvbXB0KFwiXCIpXG4gIH1cblxuICBjb25zdCBoYW5kbGVSZWdlbmVyYXRlQmxvY2sgPSBhc3luYyAoYmxvY2tJZDogc3RyaW5nLCB0eXBlOiBcImFpXCIgfCBcIm1hbnVhbFwiKSA9PiB7XG4gICAgY29uc29sZS5sb2coYD09PSBGUk9OVEVORDogUmVnZW5lcmF0aW5nIGJsb2NrICR7YmxvY2tJZH0gd2l0aCB0eXBlICR7dHlwZX0gPT09YCk7XG4gICAgY29uc29sZS5sb2coJ0RyYWZ0IElEOicsIGRyYWZ0SWQpO1xuICAgIGNvbnNvbGUubG9nKCdDdXN0b20gcHJvbXB0OicsIHR5cGUgPT09IFwiYWlcIiA/IGN1c3RvbVByb21wdCA6IHVuZGVmaW5lZCk7XG4gICAgY29uc29sZS5sb2coJ0VkaXQgY29udGVudDonLCB0eXBlID09PSBcIm1hbnVhbFwiID8gZWRpdENvbnRlbnQgOiB1bmRlZmluZWQpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnJlZ2VuZXJhdGVCbG9jayhcbiAgICAgICAgZHJhZnRJZCxcbiAgICAgICAgYmxvY2tJZCxcbiAgICAgICAgdHlwZSxcbiAgICAgICAgdHlwZSA9PT0gXCJhaVwiID8gY3VzdG9tUHJvbXB0IDogdW5kZWZpbmVkLFxuICAgICAgICB0eXBlID09PSBcIm1hbnVhbFwiID8gZWRpdENvbnRlbnQgOiB1bmRlZmluZWQsXG4gICAgICApXG5cbiAgICAgIGNvbnNvbGUubG9nKCc9PT0gRlJPTlRFTkQ6IFJlZ2VuZXJhdGlvbiByZXNwb25zZSA9PT0nLCByZXNwb25zZSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmJsb2NrKSB7XG4gICAgICAgIHNldEJsb2NrcyhcbiAgICAgICAgICBibG9ja3MubWFwKChibG9jaykgPT5cbiAgICAgICAgICAgIGJsb2NrLmlkID09PSBibG9ja0lkID8geyAuLi5ibG9jaywgLi4ucmVzcG9uc2UuYmxvY2sgfSA6IGJsb2NrLFxuICAgICAgICAgICksXG4gICAgICAgIClcblxuICAgICAgICBzZXRFZGl0aW5nQmxvY2sobnVsbClcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcIkJsb2NrIHVwZGF0ZWRcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCJDb250ZW50IGJsb2NrIGhhcyBiZWVuIHN1Y2Nlc3NmdWxseSB1cGRhdGVkLlwiLFxuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHJlc3BvbnNlIGZvcm1hdCcpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZWdlbmVyYXRpbmcgYmxvY2s6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJVcGRhdGUgZmFpbGVkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byB1cGRhdGUgY29udGVudCBibG9jay4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVEZWxldGVCbG9jayA9IChibG9ja0lkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRCbG9ja3MoYmxvY2tzLmZpbHRlcigoYmxvY2spID0+IGJsb2NrLmlkICE9PSBibG9ja0lkKSlcbiAgICB0b2FzdCh7XG4gICAgICB0aXRsZTogXCJCbG9jayBkZWxldGVkXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJDb250ZW50IGJsb2NrIGhhcyBiZWVuIHJlbW92ZWQuXCIsXG4gICAgfSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUltYWdlR2VuZXJhdGUgPSBhc3luYyAoYmxvY2tJZDogc3RyaW5nLCBkZXNjcmlwdGlvbjogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coYEdlbmVyYXRpbmcgaW1hZ2UgZm9yIGJsb2NrICR7YmxvY2tJZH0gd2l0aCBkZXNjcmlwdGlvbjogJHtkZXNjcmlwdGlvbn1gKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2VuZXJhdGVJbWFnZShkcmFmdElkLCBibG9ja0lkLCBkZXNjcmlwdGlvbik7XG5cbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmJsb2NrKSB7XG4gICAgICAgIHNldEJsb2NrcyhibG9ja3MubWFwKGJsb2NrID0+XG4gICAgICAgICAgYmxvY2suaWQgPT09IGJsb2NrSWQgPyB7IC4uLmJsb2NrLCAuLi5yZXNwb25zZS5ibG9jayB9IDogYmxvY2tcbiAgICAgICAgKSk7XG5cbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcIkltYWdlIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkhXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiQUkgaGFzIGdlbmVyYXRlZCBhbiBpbWFnZSBkZXNjcmlwdGlvbiBhbmQgcGxhY2Vob2xkZXIgZm9yIHlvdXIgY29udGVudC5cIixcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcmVzcG9uc2UgZm9ybWF0Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgaW1hZ2U6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJJbWFnZSBnZW5lcmF0aW9uIGZhaWxlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gZ2VuZXJhdGUgaW1hZ2UuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUltYWdlVXBsb2FkID0gYXN5bmMgKGJsb2NrSWQ6IHN0cmluZywgZmlsZTogRmlsZSkgPT4ge1xuICAgIGNvbnNvbGUubG9nKGBVcGxvYWRpbmcgaW1hZ2UgZm9yIGJsb2NrICR7YmxvY2tJZH06YCwgZmlsZS5uYW1lKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkudXBsb2FkSW1hZ2UoZHJhZnRJZCwgYmxvY2tJZCwgZmlsZSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmJsb2NrKSB7XG4gICAgICAgIHNldEJsb2NrcyhibG9ja3MubWFwKGJsb2NrID0+XG4gICAgICAgICAgYmxvY2suaWQgPT09IGJsb2NrSWQgPyB7IC4uLmJsb2NrLCAuLi5yZXNwb25zZS5ibG9jayB9IDogYmxvY2tcbiAgICAgICAgKSk7XG5cbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcIkltYWdlIHVwbG9hZGVkXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiSW1hZ2UgaGFzIGJlZW4gdXBsb2FkZWQgc3VjY2Vzc2Z1bGx5IVwiLFxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCByZXNwb25zZSBmb3JtYXQnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBsb2FkaW5nIGltYWdlOicsIGVycm9yKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiVXBsb2FkIGZhaWxlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gdXBsb2FkIGltYWdlLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KTtcbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxTdGVwcGVySGVhZGVyIGN1cnJlbnRTdGVwPXszfSBkcmFmdElkPXtkcmFmdElkfSAvPlxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYXgtdy01eGwgbXgtYXV0byBweC02IHB5LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTYgdy1mdWxsXCIgLz5cbiAgICAgICAgICAgIHtbMSwgMiwgMywgNCwgNV0ubWFwKChpKSA9PiAoXG4gICAgICAgICAgICAgIDxTa2VsZXRvbiBrZXk9e2l9IGNsYXNzTmFtZT1cImgtMzIgdy1mdWxsXCIgLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIDxTdGVwcGVySGVhZGVyIGN1cnJlbnRTdGVwPXszfSBkcmFmdElkPXtkcmFmdElkfSAvPlxuXG4gICAgICB7LyogVG9vbGJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTV4bCBteC1hdXRvIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGRcIj5Db250ZW50IEVkaXRvcjwvaDE+XG4gICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgIHt3b3JkQ291bnR9IHdvcmRzXG4gICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTYXZlRHJhZnR9IGRpc2FibGVkPXtzYXZpbmd9IHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgIHtzYXZpbmcgPyBcIlNhdmluZy4uLlwiIDogXCJTYXZlIERyYWZ0XCJ9XG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgIFByZXZpZXdcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUdlbmVyYXRlTGlua3N9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtnZW5lcmF0aW5nTGlua3N9XG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPExpbmsgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAge2dlbmVyYXRpbmdMaW5rcyA/IFwiR2VuZXJhdGluZy4uLlwiIDogXCJHZW5lcmF0ZSBMaW5rc1wifVxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goYC9ibG9nLyR7ZHJhZnRJZH0vZGVwbG95YCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDBcIlxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0zIDE3YTEgMSAwIDAxMS0xaDEyYTEgMSAwIDExMCAySDRhMSAxIDAgMDEtMS0xem0zLjI5My03LjcwN2ExIDEgMCAwMTEuNDE0IDBMOSAxMC41ODZWM2ExIDEgMCAxMTIgMHY3LjU4NmwxLjI5My0xLjI5M2ExIDEgMCAxMTEuNDE0IDEuNDE0bC0zIDNhMSAxIDAgMDEtMS40MTQgMGwtMy0zYTEgMSAwIDAxMC0xLjQxNHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgRGVwbG95IHRvIFdvcmRQcmVzc1xuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cIm1heC13LTV4bCBteC1hdXRvIHB4LTYgcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIHtibG9ja3MubWFwKChibG9jaywgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxDb250ZW50QmxvY2tcbiAgICAgICAgICAgICAga2V5PXtibG9jay5pZH1cbiAgICAgICAgICAgICAgYmxvY2s9e2Jsb2NrfVxuICAgICAgICAgICAgICBvbkVkaXQ9eygpID0+IGhhbmRsZUVkaXRCbG9jayhibG9jayl9XG4gICAgICAgICAgICAgIG9uUmVnZW5lcmF0ZT17KCkgPT4gaGFuZGxlUmVnZW5lcmF0ZUJsb2NrKGJsb2NrLmlkLCBcImFpXCIpfVxuICAgICAgICAgICAgICBvbkRlbGV0ZT17YmxvY2suZWRpdGFibGUgPyAoKSA9PiBoYW5kbGVEZWxldGVCbG9jayhibG9jay5pZCkgOiB1bmRlZmluZWR9XG4gICAgICAgICAgICAgIG9uSW1hZ2VHZW5lcmF0ZT17aGFuZGxlSW1hZ2VHZW5lcmF0ZX1cbiAgICAgICAgICAgICAgb25JbWFnZVVwbG9hZD17aGFuZGxlSW1hZ2VVcGxvYWR9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRWRpdCBNb2RhbCAqL31cbiAgICAgICAgPERpYWxvZyBvcGVuPXshIWVkaXRpbmdCbG9ja30gb25PcGVuQ2hhbmdlPXsoKSA9PiBzZXRFZGl0aW5nQmxvY2sobnVsbCl9PlxuICAgICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cIm1heC13LTR4bCBtYXgtaC1bODB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICA8RGlhbG9nVGl0bGU+RWRpdCBDb250ZW50IEJsb2NrPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIE1vZGlmeSB0aGUgY29udGVudCBtYW51YWxseSBvciB1c2UgQUkgdG8gcmVnZW5lcmF0ZSB3aXRoIGEgY3VzdG9tIHByb21wdFxuICAgICAgICAgICAgICA8L0RpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gbWItMiBibG9ja1wiPkNvbnRlbnQ8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRDb250ZW50fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0Q29udGVudChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICByb3dzPXs4fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTIgYmxvY2tcIj5BSSBSZWdlbmVyYXRpb24gUHJvbXB0IChPcHRpb25hbCk8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2N1c3RvbVByb21wdH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q3VzdG9tUHJvbXB0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgTWFrZSBpdCBtb3JlIGVuZ2FnaW5nLCBBZGQgc3RhdGlzdGljcywgU2ltcGxpZnkgZm9yIGJlZ2lubmVycy4uLlwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZWdlbmVyYXRlQmxvY2soZWRpdGluZ0Jsb2NrPy5pZCB8fCBcIlwiLCBcIm1hbnVhbFwiKX0gY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICBTYXZlIE1hbnVhbCBDaGFuZ2VzXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSZWdlbmVyYXRlQmxvY2soZWRpdGluZ0Jsb2NrPy5pZCB8fCBcIlwiLCBcImFpXCIpfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFjdXN0b21Qcm9tcHR9XG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgIFJlZ2VuZXJhdGUgd2l0aCBBSVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgPC9EaWFsb2c+XG4gICAgICA8L21haW4+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZVBhcmFtcyIsIkJ1dHRvbiIsIkJhZGdlIiwiSW5wdXQiLCJUZXh0YXJlYSIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiU2tlbGV0b24iLCJ1c2VUb2FzdCIsIlNhdmUiLCJFeWUiLCJMaW5rIiwiUmVmcmVzaEN3IiwiRmlsZVRleHQiLCJhcGkiLCJTdGVwcGVySGVhZGVyIiwiQ29udGVudEJsb2NrIiwiRWRpdG9yUGFnZSIsImRyYWZ0Iiwic2V0RHJhZnQiLCJibG9ja3MiLCJzZXRCbG9ja3MiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhdmluZyIsInNldFNhdmluZyIsImdlbmVyYXRpbmdMaW5rcyIsInNldEdlbmVyYXRpbmdMaW5rcyIsImVkaXRpbmdCbG9jayIsInNldEVkaXRpbmdCbG9jayIsImVkaXRDb250ZW50Iiwic2V0RWRpdENvbnRlbnQiLCJjdXN0b21Qcm9tcHQiLCJzZXRDdXN0b21Qcm9tcHQiLCJ3b3JkQ291bnQiLCJzZXRXb3JkQ291bnQiLCJyb3V0ZXIiLCJwYXJhbXMiLCJ0b2FzdCIsImRyYWZ0SWQiLCJsb2FkRHJhZnRBbmRDb250ZW50IiwidG90YWwiLCJyZWR1Y2UiLCJzdW0iLCJibG9jayIsImRyYWZ0UmVzcG9uc2UiLCJnZXREcmFmdCIsInN1Y2Nlc3MiLCJkYXRhIiwiZHJhZnREYXRhIiwic3RydWN0dXJlZENvbnRlbnQiLCJsZW5ndGgiLCJnZW5lcmF0ZUNvbnRlbnQiLCJlcnJvciIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiZ2VuZXJhdGVTdHJ1Y3R1cmVkQ29udGVudCIsIm1vY2tCbG9ja3MiLCJpZCIsInR5cGUiLCJjb250ZW50IiwiZWRpdGFibGUiLCJpbWFnZVR5cGUiLCJhbHQiLCJoMiIsIm1lc3NhZ2UiLCJoYW5kbGVTYXZlRHJhZnQiLCJzYXZlRHJhZnQiLCJzdGF0dXMiLCJoYW5kbGVHZW5lcmF0ZUxpbmtzIiwiZ2VuZXJhdGVMaW5rcyIsInB1c2giLCJoYW5kbGVFZGl0QmxvY2siLCJoYW5kbGVSZWdlbmVyYXRlQmxvY2siLCJibG9ja0lkIiwidW5kZWZpbmVkIiwicmVnZW5lcmF0ZUJsb2NrIiwibWFwIiwiRXJyb3IiLCJoYW5kbGVEZWxldGVCbG9jayIsImZpbHRlciIsImhhbmRsZUltYWdlR2VuZXJhdGUiLCJnZW5lcmF0ZUltYWdlIiwiaGFuZGxlSW1hZ2VVcGxvYWQiLCJmaWxlIiwibmFtZSIsInVwbG9hZEltYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiY3VycmVudFN0ZXAiLCJtYWluIiwiaSIsImgxIiwib25DbGljayIsImRpc2FibGVkIiwic2l6ZSIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwiaW5kZXgiLCJvbkVkaXQiLCJvblJlZ2VuZXJhdGUiLCJvbkRlbGV0ZSIsIm9uSW1hZ2VHZW5lcmF0ZSIsIm9uSW1hZ2VVcGxvYWQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwibGFiZWwiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInJvd3MiLCJwbGFjZWhvbGRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/blog/[draftId]/editor/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/content-block.tsx":
/*!**************************************!*\
  !*** ./components/content-block.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentBlock: () => (/* binding */ ContentBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit3,FileText,ImageIcon,RefreshCw,Trash2,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ ContentBlock auto */ \n\n\n\n\n\n\n// Function to format content and remove markdown formatting\nfunction formatContent(content) {\n    if (!content) return '';\n    return content// Remove markdown bold (**text**)\n    .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')// Remove markdown italic (*text*)\n    .replace(/\\*(.*?)\\*/g, '<em>$1</em>')// Convert double line breaks to paragraphs\n    .replace(/\\n\\n/g, '</p><p>')// Convert single line breaks to <br> tags\n    .replace(/\\n/g, '<br>')// Remove any remaining asterisks\n    .replace(/\\*/g, '')// Clean up extra spaces\n    .replace(/\\s+/g, ' ')// Wrap in paragraph tags if not already wrapped\n    .replace(/^(?!<p>)/, '<p>').replace(/(?!<\\/p>)$/, '</p>')// Fix empty paragraphs\n    .replace(/<p><\\/p>/g, '').trim();\n}\nfunction ContentBlock({ block, onEdit, onRegenerate, onDelete, onImageGenerate, onImageUpload }) {\n    const [imageDescription, setImageDescription] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(block.description || '');\n    const [altText, setAltText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(block.alt || '');\n    const [isGenerating, setIsGenerating] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const getBlockIcon = ()=>{\n        switch(block.type){\n            case \"image\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBlockTitle = ()=>{\n        switch(block.type){\n            case \"introduction\":\n                return \"Introduction\";\n            case \"section\":\n                return block.h2 || \"Section\";\n            case \"image\":\n                return `${block.imageType === \"feature\" ? \"Feature\" : \"In-blog\"} Image`;\n            case \"conclusion\":\n                return \"Conclusion\";\n            case \"references\":\n                return \"References\";\n            default:\n                return \"Content Block\";\n        }\n    };\n    if (block.type === \"image\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        block.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: block.imageUrl,\n                                    alt: block.alt || 'Generated image',\n                                    className: \"mx-auto max-w-full h-48 object-cover rounded-lg border\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: getBlockTitle()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-1\",\n                                            children: block.description || block.alt\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this),\n                                        block.enhancedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mt-2\",\n                                            children: [\n                                                \"AI Prompt: \",\n                                                block.enhancedPrompt\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: getBlockTitle()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-1\",\n                                            children: \"Upload an image or generate with AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    placeholder: \"Alt text (required)\",\n                                    value: altText,\n                                    onChange: (e)=>setAltText(e.target.value),\n                                    className: \"max-w-md mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    placeholder: \"Image description for AI generation\",\n                                    value: imageDescription,\n                                    onChange: (e)=>setImageDescription(e.target.value),\n                                    className: \"max-w-md mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        const input = document.createElement('input');\n                                        input.type = 'file';\n                                        input.accept = 'image/*';\n                                        input.onchange = (e)=>{\n                                            const file = e.target.files?.[0];\n                                            if (file && onImageUpload) {\n                                                onImageUpload(block.id, file);\n                                            }\n                                        };\n                                        input.click();\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upload Image\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    disabled: !imageDescription || isGenerating,\n                                    onClick: ()=>{\n                                        if (imageDescription && onImageGenerate) {\n                                            setIsGenerating(true);\n                                            onImageGenerate(block.id, imageDescription);\n                                            // Reset generating state after a delay (will be handled by parent)\n                                            setTimeout(()=>setIsGenerating(false), 3000);\n                                        }\n                                    },\n                                    children: isGenerating ? \"Generating...\" : \"Generate with AI\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"hover:shadow-md transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                getBlockIcon(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: getBlockTitle()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                block.wordCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        block.wordCount,\n                                        \" words\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onEdit,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onRegenerate,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onDelete,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit3_FileText_ImageIcon_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-lg max-w-none\",\n                    children: [\n                        block.h2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4 leading-tight border-b border-gray-200 pb-2\",\n                            children: block.h2\n                        }, void 0, false, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-800 leading-relaxed text-base blog-content\",\n                            dangerouslySetInnerHTML: {\n                                __html: formatContent(block.content || '')\n                            },\n                            style: {\n                                lineHeight: '1.7',\n                                fontSize: '16px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\content-block.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/content-block.tsx\n");

/***/ }),

/***/ "(ssr)/./components/stepper-header.tsx":
/*!***************************************!*\
  !*** ./components/stepper-header.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StepperHeader: () => (/* binding */ StepperHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Save!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ StepperHeader auto */ \n\n\n\n\n\n\nconst steps = [\n    {\n        id: 1,\n        name: \"Company & Keywords\",\n        path: \"/keywords\"\n    },\n    {\n        id: 2,\n        name: \"H1 & Meta\",\n        path: \"/meta\"\n    },\n    {\n        id: 3,\n        name: \"Content & Deploy\",\n        path: \"/editor\"\n    }\n];\nfunction StepperHeader({ currentStep, draftId, companyName }) {\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const handleSaveExit = async ()=>{\n        setSaving(true);\n        try {\n            // Save current progress\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            toast({\n                title: \"Progress saved\",\n                description: \"Your work has been saved successfully.\"\n            });\n            router.push(\"/\");\n        } catch (error) {\n            toast({\n                title: \"Save failed\",\n                description: \"Failed to save progress. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-[#0066cc]\",\n                                    children: \"ArticleScribe\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                companyName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: companyName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    children: [\n                                        \"Step \",\n                                        currentStep,\n                                        \" of \",\n                                        steps.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSaveExit,\n                                    disabled: saving,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Saving...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Save & Exit\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `\n                  flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium\n                  ${step.id < currentStep ? \"bg-[#00aa66] text-white\" : step.id === currentStep ? \"bg-[#0066cc] text-white\" : \"bg-gray-200 text-gray-500\"}\n                `,\n                                            children: step.id < currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 44\n                                            }, this) : step.id\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `\n                  ml-2 text-sm font-medium\n                  ${step.id <= currentStep ? \"text-gray-900\" : \"text-gray-500\"}\n                `,\n                                            children: step.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `\n                  w-12 h-0.5 mx-4\n                  ${step.id < currentStep ? \"bg-[#00aa66]\" : \"bg-gray-200\"}\n                `\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, step.id, true, {\n                            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\stepper-header.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3N0ZXBwZXItaGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDZTtBQUNGO0FBQ0g7QUFDQztBQUNDO0FBUTVDLE1BQU1PLFFBQVE7SUFDWjtRQUFFQyxJQUFJO1FBQUdDLE1BQU07UUFBc0JDLE1BQU07SUFBWTtJQUN2RDtRQUFFRixJQUFJO1FBQUdDLE1BQU07UUFBYUMsTUFBTTtJQUFRO0lBQzFDO1FBQUVGLElBQUk7UUFBR0MsTUFBTTtRQUFvQkMsTUFBTTtJQUFVO0NBQ3BEO0FBRU0sU0FBU0MsY0FBYyxFQUFFQyxXQUFXLEVBQUVDLE9BQU8sRUFBRUMsV0FBVyxFQUFzQjtJQUNyRixNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU1pQixTQUFTWiwwREFBU0E7SUFDeEIsTUFBTSxFQUFFYSxLQUFLLEVBQUUsR0FBR1osMERBQVFBO0lBRTFCLE1BQU1hLGlCQUFpQjtRQUNyQkgsVUFBVTtRQUNWLElBQUk7WUFDRix3QkFBd0I7WUFDeEIsTUFBTSxJQUFJSSxRQUFRLENBQUNDLFVBQVlDLFdBQVdELFNBQVM7WUFDbkRILE1BQU07Z0JBQ0pLLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtZQUNBUCxPQUFPUSxJQUFJLENBQUM7UUFDZCxFQUFFLE9BQU9DLE9BQU87WUFDZFIsTUFBTTtnQkFDSkssT0FBTztnQkFDUEMsYUFBYTtnQkFDYkcsU0FBUztZQUNYO1FBQ0YsU0FBVTtZQUNSWCxVQUFVO1FBQ1o7SUFDRjtJQUVBLHFCQUNFLDhEQUFDWTtRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7OzhDQUNDLDhEQUFDQztvQ0FBR0YsV0FBVTs4Q0FBb0M7Ozs7OztnQ0FDakRmLDZCQUFlLDhEQUFDa0I7b0NBQUVILFdBQVU7OENBQXlCZjs7Ozs7Ozs7Ozs7O3NDQUd4RCw4REFBQ2dCOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQzNCLHVEQUFLQTtvQ0FBQ3lCLFNBQVE7O3dDQUFVO3dDQUNqQmY7d0NBQVk7d0NBQUtMLE1BQU0wQixNQUFNOzs7Ozs7OzhDQUdyQyw4REFBQ2hDLHlEQUFNQTtvQ0FBQ2lDLFNBQVNmO29DQUFnQmdCLFVBQVVwQjtvQ0FBUVksU0FBUTtvQ0FBVVMsTUFBSzs4Q0FDdkVyQix1QkFDQzs7MERBQ0UsOERBQUNYLHNGQUFJQTtnREFBQ3lCLFdBQVU7Ozs7Ozs0Q0FBK0I7O3FFQUlqRDs7MERBQ0UsOERBQUN6QixzRkFBSUE7Z0RBQUN5QixXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFTM0MsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNadEIsTUFBTThCLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDaEIsOERBQUNUOzRCQUFrQkQsV0FBVTs7OENBQzNCLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUNDRCxXQUFXLENBQUM7O2tCQUVaLEVBQ0VTLEtBQUs5QixFQUFFLEdBQUdJLGNBQ04sNEJBQ0EwQixLQUFLOUIsRUFBRSxLQUFLSSxjQUNWLDRCQUNBLDRCQUNQO2dCQUNILENBQUM7c0RBRUUwQixLQUFLOUIsRUFBRSxHQUFHSSw0QkFBYyw4REFBQ1Qsc0ZBQUtBO2dEQUFDMEIsV0FBVTs7Ozs7dURBQWVTLEtBQUs5QixFQUFFOzs7Ozs7c0RBRWxFLDhEQUFDZ0M7NENBQ0NYLFdBQVcsQ0FBQzs7a0JBRVosRUFBRVMsS0FBSzlCLEVBQUUsSUFBSUksY0FBYyxrQkFBa0IsZ0JBQWdCO2dCQUMvRCxDQUFDO3NEQUVFMEIsS0FBSzdCLElBQUk7Ozs7Ozs7Ozs7OztnQ0FJYjhCLFFBQVFoQyxNQUFNMEIsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQ0g7b0NBQ0NELFdBQVcsQ0FBQzs7a0JBRVosRUFBRVMsS0FBSzlCLEVBQUUsR0FBR0ksY0FBYyxpQkFBaUIsY0FBYztnQkFDM0QsQ0FBQzs7Ozs7OzsyQkEvQkswQixLQUFLOUIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBd0M3QiIsInNvdXJjZXMiOlsiRDpcXGJsb2ctZ2VuLWFpXFxmcm9udGVuZFxcY29tcG9uZW50c1xcc3RlcHBlci1oZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiXG5pbXBvcnQgeyBDaGVjaywgU2F2ZSB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiXG5cbmludGVyZmFjZSBTdGVwcGVySGVhZGVyUHJvcHMge1xuICBjdXJyZW50U3RlcDogbnVtYmVyXG4gIGRyYWZ0SWQ6IHN0cmluZ1xuICBjb21wYW55TmFtZT86IHN0cmluZ1xufVxuXG5jb25zdCBzdGVwcyA9IFtcbiAgeyBpZDogMSwgbmFtZTogXCJDb21wYW55ICYgS2V5d29yZHNcIiwgcGF0aDogXCIva2V5d29yZHNcIiB9LFxuICB7IGlkOiAyLCBuYW1lOiBcIkgxICYgTWV0YVwiLCBwYXRoOiBcIi9tZXRhXCIgfSxcbiAgeyBpZDogMywgbmFtZTogXCJDb250ZW50ICYgRGVwbG95XCIsIHBhdGg6IFwiL2VkaXRvclwiIH0sXG5dXG5cbmV4cG9ydCBmdW5jdGlvbiBTdGVwcGVySGVhZGVyKHsgY3VycmVudFN0ZXAsIGRyYWZ0SWQsIGNvbXBhbnlOYW1lIH06IFN0ZXBwZXJIZWFkZXJQcm9wcykge1xuICBjb25zdCBbc2F2aW5nLCBzZXRTYXZpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICBjb25zdCBoYW5kbGVTYXZlRXhpdCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRTYXZpbmcodHJ1ZSlcbiAgICB0cnkge1xuICAgICAgLy8gU2F2ZSBjdXJyZW50IHByb2dyZXNzXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSlcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiUHJvZ3Jlc3Mgc2F2ZWRcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiWW91ciB3b3JrIGhhcyBiZWVuIHNhdmVkIHN1Y2Nlc3NmdWxseS5cIixcbiAgICAgIH0pXG4gICAgICByb3V0ZXIucHVzaChcIi9cIilcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJTYXZlIGZhaWxlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gc2F2ZSBwcm9ncmVzcy4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0U2F2aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcHgtNiBweS00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LVsjMDA2NmNjXVwiPkFydGljbGVTY3JpYmU8L2gxPlxuICAgICAgICAgICAge2NvbXBhbnlOYW1lICYmIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPntjb21wYW55TmFtZX08L3A+fVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCI+XG4gICAgICAgICAgICAgIFN0ZXAge2N1cnJlbnRTdGVwfSBvZiB7c3RlcHMubGVuZ3RofVxuICAgICAgICAgICAgPC9CYWRnZT5cblxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTYXZlRXhpdH0gZGlzYWJsZWQ9e3NhdmluZ30gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAge3NhdmluZyA/IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICAgICAgICAgICAgU2F2aW5nLi4uXG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICBTYXZlICYgRXhpdFxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTdGVwcGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIHtzdGVwcy5tYXAoKHN0ZXAsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17c3RlcC5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy04IGgtOCByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bVxuICAgICAgICAgICAgICAgICAgJHtcbiAgICAgICAgICAgICAgICAgICAgc3RlcC5pZCA8IGN1cnJlbnRTdGVwXG4gICAgICAgICAgICAgICAgICAgICAgPyBcImJnLVsjMDBhYTY2XSB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICA6IHN0ZXAuaWQgPT09IGN1cnJlbnRTdGVwXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctWyMwMDY2Y2NdIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLWdyYXktMjAwIHRleHQtZ3JheS01MDBcIlxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge3N0ZXAuaWQgPCBjdXJyZW50U3RlcCA/IDxDaGVjayBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz4gOiBzdGVwLmlkfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgIG1sLTIgdGV4dC1zbSBmb250LW1lZGl1bVxuICAgICAgICAgICAgICAgICAgJHtzdGVwLmlkIDw9IGN1cnJlbnRTdGVwID8gXCJ0ZXh0LWdyYXktOTAwXCIgOiBcInRleHQtZ3JheS01MDBcIn1cbiAgICAgICAgICAgICAgICBgfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtzdGVwLm5hbWV9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7aW5kZXggPCBzdGVwcy5sZW5ndGggLSAxICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgIHctMTIgaC0wLjUgbXgtNFxuICAgICAgICAgICAgICAgICAgJHtzdGVwLmlkIDwgY3VycmVudFN0ZXAgPyBcImJnLVsjMDBhYTY2XVwiIDogXCJiZy1ncmF5LTIwMFwifVxuICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvaGVhZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJCdXR0b24iLCJCYWRnZSIsIkNoZWNrIiwiU2F2ZSIsInVzZVJvdXRlciIsInVzZVRvYXN0Iiwic3RlcHMiLCJpZCIsIm5hbWUiLCJwYXRoIiwiU3RlcHBlckhlYWRlciIsImN1cnJlbnRTdGVwIiwiZHJhZnRJZCIsImNvbXBhbnlOYW1lIiwic2F2aW5nIiwic2V0U2F2aW5nIiwicm91dGVyIiwidG9hc3QiLCJoYW5kbGVTYXZlRXhpdCIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJwdXNoIiwiZXJyb3IiLCJ2YXJpYW50IiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDEiLCJwIiwibGVuZ3RoIiwib25DbGljayIsImRpc2FibGVkIiwic2l6ZSIsIm1hcCIsInN0ZXAiLCJpbmRleCIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/stepper-header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2JhZGdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEI7QUFDbUM7QUFFakM7QUFFaEMsTUFBTUcsZ0JBQWdCRiw2REFBR0EsQ0FDdkIsMEtBQ0E7SUFDRUcsVUFBVTtRQUNSQyxTQUFTO1lBQ1BDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQVM7UUFDWDtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmTCxTQUFTO0lBQ1g7QUFDRjtBQU9GLFNBQVNNLE1BQU0sRUFBRUMsU0FBUyxFQUFFUCxPQUFPLEVBQUUsR0FBR1EsT0FBbUI7SUFDekQscUJBQ0UsOERBQUNDO1FBQUlGLFdBQVdWLDhDQUFFQSxDQUFDQyxjQUFjO1lBQUVFO1FBQVEsSUFBSU87UUFBYSxHQUFHQyxLQUFLOzs7Ozs7QUFFeEU7QUFFK0IiLCJzb3VyY2VzIjpbIkQ6XFxibG9nLWdlbi1haVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHVpXFxiYWRnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBiYWRnZVZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLWZ1bGwgYm9yZGVyIHB4LTIuNSBweS0wLjUgdGV4dC14cyBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1yaW5nIGZvY3VzOnJpbmctb2Zmc2V0LTJcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1wcmltYXJ5LzgwXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJvcmRlci10cmFuc3BhcmVudCBiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1zZWNvbmRhcnkvODBcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIGhvdmVyOmJnLWRlc3RydWN0aXZlLzgwXCIsXG4gICAgICAgIG91dGxpbmU6IFwidGV4dC1mb3JlZ3JvdW5kXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQmFkZ2VQcm9wc1xuICBleHRlbmRzIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJhZGdlVmFyaWFudHM+IHt9XG5cbmZ1bmN0aW9uIEJhZGdlKHsgY2xhc3NOYW1lLCB2YXJpYW50LCAuLi5wcm9wcyB9OiBCYWRnZVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NuKGJhZGdlVmFyaWFudHMoeyB2YXJpYW50IH0pLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4gIClcbn1cblxuZXhwb3J0IHsgQmFkZ2UsIGJhZGdlVmFyaWFudHMgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3ZhIiwiY24iLCJiYWRnZVZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsInNlY29uZGFyeSIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsImRlZmF1bHRWYXJpYW50cyIsIkJhZGdlIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtYmFzZSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBmaWxlOnRleHQtZm9yZWdyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/skeleton.tsx":
/*!************************************!*\
  !*** ./components/ui/skeleton.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NrZWxldG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnQztBQUVoQyxTQUFTQyxTQUFTLEVBQ2hCQyxTQUFTLEVBQ1QsR0FBR0MsT0FDa0M7SUFDckMscUJBQ0UsOERBQUNDO1FBQ0NGLFdBQVdGLDhDQUFFQSxDQUFDLHFDQUFxQ0U7UUFDbEQsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFbUIiLCJzb3VyY2VzIjpbIkQ6XFxibG9nLWdlbi1haVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBTa2VsZXRvbih7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50Pikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJhbmltYXRlLXB1bHNlIHJvdW5kZWQtbWQgYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IFNrZWxldG9uIH1cbiJdLCJuYW1lcyI6WyJjbiIsIlNrZWxldG9uIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\blog-gen-ai\\\\frontend\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUcvQixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCxxVEFDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUNBSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiRDpcXGJsb2ctZ2VuLWFpXFxmcm9udGVuZFxcY29tcG9uZW50c1xcdWlcXHRleHRhcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFRleHRBcmVhRWxlbWVudCxcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJ0ZXh0YXJlYVwiPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICByZXR1cm4gKFxuICAgIDx0ZXh0YXJlYVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmbGV4IG1pbi1oLVs4MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtYmFzZSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICBjbGFzc05hbWVcbiAgICAgICl9XG4gICAgICByZWY9e3JlZn1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59KVxuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSBcIlRleHRhcmVhXCJcblxuZXhwb3J0IHsgVGV4dGFyZWEgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:5000/api\";\nclass ApiClient {\n    async request(endpoint, options) {\n        const url = `${API_BASE_URL}${endpoint}`;\n        const headers = {};\n        // Only add Content-Type for non-FormData requests\n        if (!(options?.body instanceof FormData)) {\n            headers[\"Content-Type\"] = \"application/json\";\n        }\n        const response = await fetch(url, {\n            headers: {\n                ...headers,\n                ...options?.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(`API request failed: ${response.statusText}`);\n        }\n        return response.json();\n    }\n    // Companies\n    async getCompanies() {\n        return this.request(\"/company\");\n    }\n    // Blog workflow\n    async startBlog(companyId, userId) {\n        return this.request(\"/blog/start\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                companyId,\n                userId\n            })\n        });\n    }\n    async selectKeywordAnalyze(draftId, selectedKeyword, alternativeKeywords) {\n        return this.request(\"/blog/select-keyword-analyze\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                selectedKeyword,\n                alternativeKeywords\n            })\n        });\n    }\n    async generateMetaScores(draftId) {\n        return this.request(\"/blog/generate-meta-scores\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async selectMeta(draftId, selectedMetaIndex) {\n        return this.request(\"/blog/select-meta\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                selectedMetaIndex\n            })\n        });\n    }\n    async regenerateMeta(draftId, optionIndex) {\n        return this.request(\"/blog/regenerate-meta\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                optionIndex\n            })\n        });\n    }\n    async generateStructuredContent(draftId) {\n        return this.request(\"/blog/generate-structured-content\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async regenerateBlock(draftId, blockId, regenerationType, customPrompt, newContent) {\n        return this.request(\"/blog/regenerate-block\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                blockId,\n                regenerationType,\n                customPrompt,\n                newContent\n            })\n        });\n    }\n    async generateImage(draftId, blockId, description) {\n        return this.request(\"/blog/generate-image\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                blockId,\n                description\n            })\n        });\n    }\n    async generateAIImage(prompt, keyword) {\n        return this.request(\"/blog/generate-ai-image\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                prompt,\n                keyword\n            })\n        });\n    }\n    async uploadImage(draftId, blockId, file) {\n        const formData = new FormData();\n        formData.append(\"draftId\", draftId);\n        formData.append(\"blockId\", blockId);\n        formData.append(\"file\", file);\n        return this.request(\"/blog/upload-image\", {\n            method: \"POST\",\n            body: formData,\n            headers: {}\n        });\n    }\n    async generateLinks(draftId) {\n        return this.request(\"/blog/generate-links\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    async deployWordPress(draftId, wordpressConfig) {\n        return this.request(\"/blog/deploy-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                wordpressConfig\n            })\n        });\n    }\n    // Draft management\n    async getDraft(draftId) {\n        return this.request(`/blog/draft/${draftId}`);\n    }\n    async listDrafts(userId) {\n        const params = userId ? `?userId=${userId}` : \"\";\n        return this.request(`/blog/drafts${params}`);\n    }\n    // WordPress\n    async testWordPress() {\n        return this.request(\"/blog/test-wordpress\", {\n            method: \"POST\"\n        });\n    }\n    async previewWordPress(draftId) {\n        return this.request(\"/blog/preview-wordpress\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId\n            })\n        });\n    }\n    // Draft management - additional methods\n    async deleteDraft(draftId) {\n        return this.request(`/blog/draft/${draftId}`, {\n            method: \"DELETE\"\n        });\n    }\n    async saveDraft(draftId, updates) {\n        return this.request(\"/blog/save-draft\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                updates\n            })\n        });\n    }\n    // WordPress deployment\n    async deployToWordPress(draftId, options) {\n        return this.request(`/blog/deploy-wordpress`, {\n            method: \"POST\",\n            body: JSON.stringify({\n                draftId,\n                postStatus: options.postStatus\n            })\n        });\n    }\n}\nconst api = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cblog%5C%5C%5BdraftId%5D%5C%5Ceditor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cblog%5C%5C%5BdraftId%5D%5C%5Ceditor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/blog/[draftId]/editor/page.tsx */ \"(ssr)/./app/blog/[draftId]/editor/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNibG9nLWdlbi1haSU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDYmxvZyU1QyU1QyU1QmRyYWZ0SWQlNUQlNUMlNUNlZGl0b3IlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxibG9nLWdlbi1haVxcXFxmcm9udGVuZFxcXFxhcHBcXFxcYmxvZ1xcXFxbZHJhZnRJZF1cXFxcZWRpdG9yXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cblog%5C%5C%5BdraftId%5D%5C%5Ceditor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cblog-gen-ai%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&page=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&appPaths=%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage&pagePath=private-next-app-dir%2Fblog%2F%5BdraftId%5D%2Feditor%2Fpage.tsx&appDir=D%3A%5Cblog-gen-ai%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cblog-gen-ai%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();