const { GoogleGenerativeAI } = require('@google/generative-ai');

// Use GOOGLE_API_KEY since it has all Google APIs activated
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY);

// Configure high-end models with optimal settings
const modelConfig = {
  generationConfig: {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 8192,
  }
};

// Use the working model: gemini-1.5-flash
const model = genAI.getGenerativeModel({
  model: "gemini-1.5-flash",
  ...modelConfig
});
console.log('✅ Using Gemini 1.5 Flash model');

// Vision model for image generation
const visionModel = genAI.getGenerativeModel({
  model: "gemini-1.5-pro-vision",
  generationConfig: {
    temperature: 0.8,
    topK: 32,
    topP: 0.95,
    maxOutputTokens: 4096,
  }
});

module.exports = { model, visionModel, genAI };