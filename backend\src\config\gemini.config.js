const { GoogleGenerativeAI } = require('@google/generative-ai');

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Configure high-end models with optimal settings
const modelConfig = {
  generationConfig: {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 8192,
  }
};

// Try multiple model options (highest quality first)
let model;
try {
  model = genAI.getGenerativeModel({
    model: "gemini-1.5-pro",
    ...modelConfig
  });
  console.log('✅ Using Gemini 1.5 Pro model');
} catch (error) {
  try {
    model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash",
      ...modelConfig
    });
    console.log('✅ Using Gemini 2.0 Flash model');
  } catch (error2) {
    try {
      model = genAI.getGenerativeModel({
        model: "gemini-2.0-pro",
        ...modelConfig
      });
      console.log('✅ Using Gemini 2.0 Pro model');
    } catch (error3) {
      model = genAI.getGenerativeModel({
        model: "gemini-pro",
        ...modelConfig
      });
      console.log('✅ Using Gemini Pro model (fallback)');
    }
  }
}

// Vision model for image generation
const visionModel = genAI.getGenerativeModel({
  model: "gemini-1.5-pro-vision",
  generationConfig: {
    temperature: 0.8,
    topK: 32,
    topP: 0.95,
    maxOutputTokens: 4096,
  }
});

module.exports = { model, visionModel, genAI };