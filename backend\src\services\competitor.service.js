const axios = require('axios');
const { model } = require('../config/gemini.config');
const newsService = require('./news.service');

class CompetitorService {
  constructor() {
    // Add your competitor list here or fetch from sheets
    this.competitors = [
      'sunrun.com',
      'tesla.com/solar',
      'enphase.com',
      'solaredge.com',
      'aurorasolar.com'
    ];
  }

  async analyzeCompetitors(focusKeyword) {
    try {
      console.log(`Starting competitor analysis for keyword: ${focusKeyword}`);

      // Fetch real-time competitor news and data
      const competitorNews = await newsService.fetchCompetitorNews(this.competitors, focusKeyword);
      const industryInsights = await newsService.getRecentIndustryInsights(focusKeyword);

      console.log(`Fetched ${competitorNews.length} competitor news articles`);
      console.log(`Fetched ${industryInsights.articles.length} industry articles`);

      // Prepare real-time data for Gemini analysis
      const recentNewsContext = competitorNews.slice(0, 10).map(article => ({
        competitor: article.competitor,
        title: article.title,
        description: article.description,
        publishedAt: article.publishedAt,
        source: article.source?.name
      }));

      const industryTrends = industryInsights.insights.slice(0, 5).map(insight => ({
        type: insight.type,
        insight: insight.insight,
        title: insight.title
      }));

      const prompt = `
        Analyze competitor content for keyword: "${focusKeyword}" using REAL-TIME DATA from ${new Date().getFullYear()}

        Top solar industry competitors: ${this.competitors.join(', ')}

        RECENT COMPETITOR NEWS (Last 30 days):
        ${recentNewsContext.map(news => `- ${news.competitor}: "${news.title}" (${news.source})`).join('\n')}

        CURRENT INDUSTRY TRENDS:
        ${industryTrends.map(trend => `- ${trend.type}: ${trend.insight}`).join('\n')}

        Based on this REAL-TIME data, provide analysis in this JSON format:
        {
          "topRankingPages": [
            {
              "competitor": "company name",
              "title": "recent content title from news",
              "wordCount": "estimated word count",
              "keyPoints": ["current market point", "recent trend point"],
              "contentGaps": ["gap based on recent news", "opportunity from trends"],
              "recentActivity": "summary of recent competitor activity"
            }
          ],
          "commonH2Topics": ["topic based on recent trends", "topic from competitor news"],
          "averageWordCount": "number",
          "contentOpportunities": ["opportunity from real-time data", "gap in current market"],
          "realTimeInsights": ["insight from recent news", "trend-based insight"]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Competitor analysis parsing error:', parseError);
      }

      // Enhanced fallback analysis with real-time context
      const fallbackInsights = industryInsights.insights.slice(0, 3).map(insight => insight.insight);

      return {
        topRankingPages: [
          {
            competitor: "Industry Leader",
            title: `Complete Guide to ${focusKeyword} - ${new Date().getFullYear()} Edition`,
            wordCount: "2500",
            keyPoints: ["Comprehensive coverage", "Expert insights", ...fallbackInsights.slice(0, 2)],
            contentGaps: ["Local market specifics", "2025 updates", "Real-time market data"],
            recentActivity: "Active in recent industry discussions"
          }
        ],
        commonH2Topics: [
          `What is ${focusKeyword}`,
          `Benefits of ${focusKeyword}`,
          "Step-by-Step Guide",
          "Common Mistakes",
          "Cost Analysis",
          `${focusKeyword} Trends ${new Date().getFullYear()}`
        ],
        averageWordCount: "2000",
        contentOpportunities: [
          "More detailed implementation guide",
          "Local market analysis",
          "ROI calculator integration",
          "Real-time industry insights",
          "Current regulatory updates"
        ],
        realTimeInsights: fallbackInsights.length > 0 ? fallbackInsights : [
          "Market showing continued growth",
          "New regulations affecting industry",
          "Technology improvements driving adoption"
        ]
      };
    } catch (error) {
      console.error('Error analyzing competitors:', error);
      throw error;
    }
  }

  async generateKeywordCluster(focusKeyword, competitorAnalysis) {
    try {
      const prompt = `
        Generate a keyword cluster for main keyword: "${focusKeyword}"
        Based on competitor topics: ${competitorAnalysis.commonH2Topics.join(', ')}
        
        Create a comprehensive keyword cluster with search volume estimates.
        Include LSI keywords and related terms.
        
        Return in this JSON format:
        {
          "primaryKeyword": "${focusKeyword}",
          "secondaryKeywords": [
            {"keyword": "term", "searchVolume": "estimate", "difficulty": "low/medium/high"}
          ],
          "lsiKeywords": ["term1", "term2"],
          "h2Keywords": [
            {"h2Title": "title", "targetKeyword": "keyword", "searchIntent": "informational/transactional"}
          ]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Keyword cluster parsing error:', parseError);
      }

      // Fallback cluster
      return {
        primaryKeyword: focusKeyword,
        secondaryKeywords: [
          {keyword: `${focusKeyword} guide`, searchVolume: "1000", difficulty: "medium"},
          {keyword: `${focusKeyword} cost`, searchVolume: "800", difficulty: "low"},
          {keyword: `best ${focusKeyword}`, searchVolume: "600", difficulty: "high"}
        ],
        lsiKeywords: ["solar", "installation", "permits", "efficiency", "ROI"],
        h2Keywords: competitorAnalysis.commonH2Topics.map(topic => ({
          h2Title: topic,
          targetKeyword: topic.toLowerCase().replace(/[^a-z0-9\s]/g, ''),
          searchIntent: "informational"
        }))
      };
    } catch (error) {
      console.error('Error generating keyword cluster:', error);
      throw error;
    }
  }

  async analyzeTrends(focusKeyword) {
    try {
      console.log(`Starting trends analysis for keyword: ${focusKeyword}`);

      const currentYear = new Date().getFullYear();

      // Fetch real-time trending data
      const trendingNews = await newsService.fetchTrendingTopics(focusKeyword);
      const industryInsights = await newsService.getRecentIndustryInsights(focusKeyword);

      console.log(`Fetched ${trendingNews.length} trending articles`);

      // Prepare real-time trends context
      const recentTrends = trendingNews.slice(0, 8).map(article => ({
        title: article.title,
        category: article.category,
        publishedAt: article.publishedAt,
        source: article.source?.name
      }));

      const marketInsights = industryInsights.insights
        .filter(insight => insight.type === 'growth_trend' || insight.type === 'market_trends')
        .slice(0, 5);

      const regulatoryInsights = industryInsights.insights
        .filter(insight => insight.type === 'regulatory')
        .slice(0, 3);

      const techInsights = industryInsights.insights
        .filter(insight => insight.type === 'technology')
        .slice(0, 3);

      const prompt = `
        Analyze current trends for: "${focusKeyword}" in ${currentYear} using REAL-TIME DATA

        RECENT TRENDING NEWS (Last 30 days):
        ${recentTrends.map(trend => `- ${trend.category}: "${trend.title}" (${trend.source})`).join('\n')}

        MARKET INSIGHTS FROM NEWS:
        ${marketInsights.map(insight => `- ${insight.insight}: ${insight.title}`).join('\n')}

        REGULATORY UPDATES:
        ${regulatoryInsights.map(insight => `- ${insight.title}`).join('\n')}

        TECHNOLOGY DEVELOPMENTS:
        ${techInsights.map(insight => `- ${insight.title}`).join('\n')}

        Based on this REAL-TIME data, return in JSON format:
        {
          "currentTrends": ["trend from recent news", "trend from market data"],
          "emergingTopics": ["topic from tech news", "topic from industry insights"],
          "seasonalFactors": "description based on current date and news",
          "regulatoryUpdates": ["update from real news", "policy from recent articles"],
          "marketInsights": ["insight from trending news", "growth pattern from data"],
          "realTimeData": {
            "lastUpdated": "${new Date().toISOString()}",
            "sourcesAnalyzed": ${trendingNews.length + industryInsights.articles.length},
            "trendingKeywords": ["keyword from news analysis"]
          }
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Trends analysis parsing error:', parseError);
      }

      // Enhanced fallback with real-time context
      const fallbackTrends = recentTrends.slice(0, 3).map(trend =>
        trend.title.length > 50 ? trend.title.substring(0, 50) + '...' : trend.title
      );

      const fallbackRegulatory = regulatoryInsights.length > 0
        ? regulatoryInsights.map(insight => insight.title.substring(0, 60) + '...')
        : ["New tax incentives available", "Updated interconnection standards"];

      const fallbackMarket = marketInsights.length > 0
        ? marketInsights.map(insight => insight.insight)
        : ["Supply chain improvements", "Cost reductions in panel technology"];

      return {
        currentTrends: fallbackTrends.length > 0 ? fallbackTrends : [
          "Increased focus on energy independence",
          "Growing adoption of battery storage"
        ],
        emergingTopics: [
          "Virtual power plants",
          "AI-powered energy management",
          `${focusKeyword} automation trends`
        ],
        seasonalFactors: `Peak installation season approaching - ${new Date().toLocaleDateString()}`,
        regulatoryUpdates: fallbackRegulatory.slice(0, 3),
        marketInsights: fallbackMarket.slice(0, 3),
        realTimeData: {
          lastUpdated: new Date().toISOString(),
          sourcesAnalyzed: trendingNews.length + industryInsights.articles.length,
          trendingKeywords: industryInsights.insights
            .filter(insight => insight.keywords)
            .flatMap(insight => insight.keywords.slice(0, 3))
            .map(kw => kw.keyword || kw)
            .slice(0, 5)
        }
      };
    } catch (error) {
      console.error('Error analyzing trends:', error);
      throw error;
    }
  }
}

module.exports = new CompetitorService();