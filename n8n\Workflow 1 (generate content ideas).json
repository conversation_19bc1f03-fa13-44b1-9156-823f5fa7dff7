{"name": "Workflow 1 (generate content ideas)", "nodes": [{"parameters": {"documentId": {"__rl": true, "value": "1F6afV2T3QxBQHrLfrbwqmR0a8Aag3YqCDZkvwwQf1R4", "mode": "list", "cachedResultName": "Company KT Sheet", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1F6afV2T3QxBQHrLfrbwqmR0a8Aag3YqCDZkvwwQf1R4/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Wattmonk KT", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1F6afV2T3QxBQHrLfrbwqmR0a8Aag3YqCDZkvwwQf1R4/edit#gid=0"}, "combineFilters": "OR", "options": {}}, "id": "1843c64d-df7f-433c-86b0-267fb181aec9", "name": "Read Company KT Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [200, 0], "credentials": {"googleSheetsOAuth2Api": {"id": "TXBfVPVDwrhp999t", "name": "Google Sheets account"}}}, {"parameters": {"url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "authentication": "predefinedCredentialType", "nodeCredentialType": "googlePalmApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"You are a professional solar industry content strategist. Generate a unique, detailed blog idea based on the following requirements:\\n\\nCompany: {{ $json.companyName }}\\nServices: {{ $json.services }}\\nFocus Keyword: {{ $json.focusKeyword }}\\nArticle Format: {{ $json.articleFormat }}\\nTarget Audience: {{ $json.targetAudience }}\\nObjective: {{ $json.objective }}\\nWord Count: {{ $json.wordCount }}\\nUnique Angle: {{ $json.uniqueAngle }}\\nExecution ID: {{ $json.executionId }}\\n\\nGenerate a comprehensive blog idea that includes:\\n1. A compelling title incorporating the focus keyword\\n2. A detailed 7-point outline\\n3. 3-5 key takeaways\\n4. 2-3 practical examples\\n5. A strong call-to-action\\n\\nEnsure the content is specifically tailored to the target audience and incorporates the unique angle. The blog should be actionable, informative, and demonstrate expertise in solar technology.\\n\\nReturn the response in this exact JSON format:\\n\\n{\\n  \\\"focusKeyword\\\": \\\"{{ $json.focusKeyword }}\\\",\\n  \\\"title\\\": \\\"[Create compelling title with focus keyword]\\\",\\n  \\\"articleFormat\\\": \\\"{{ $json.articleFormat }}\\\",\\n  \\\"wordCount\\\": {{ $json.wordCount }},\\n  \\\"targetAudience\\\": \\\"{{ $json.targetAudience }}\\\",\\n  \\\"objective\\\": \\\"{{ $json.objective }}\\\",\\n  \\\"uniqueAngle\\\": \\\"{{ $json.uniqueAngle }}\\\",\\n  \\\"outline\\\": [\\n    \\\"1. Introduction and [specific topic]\\\",\\n    \\\"2. [Detailed point 2]\\\",\\n    \\\"3. [Detailed point 3]\\\",\\n    \\\"4. [Detailed point 4]\\\",\\n    \\\"5. [Detailed point 5]\\\",\\n    \\\"6. [Detailed point 6]\\\",\\n    \\\"7. Conclusion and Next Steps\\\"\\n  ],\\n  \\\"keyTakeaways\\\": [\\n    \\\"[Specific takeaway 1]\\\",\\n    \\\"[Specific takeaway 2]\\\",\\n    \\\"[Specific takeaway 3]\\\",\\n    \\\"[Specific takeaway 4]\\\",\\n    \\\"[Specific takeaway 5]\\\"\\n  ],\\n  \\\"examples\\\": [\\n    \\\"[Practical example 1]\\\",\\n    \\\"[Practical example 2]\\\",\\n    \\\"[Practical example 3]\\\"\\n  ],\\n  \\\"callToAction\\\": \\\"[Specific CTA related to {{ $json.companyName }} services]\\\"\\n}\\n\\nMake sure each blog idea is completely unique and incorporates current solar industry trends and technologies. Add randomization elements like specific years, locations, or case study details to ensure uniqueness across multiple generations.\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 0.9,\n    \"topK\": 40,\n    \"topP\": 0.95,\n    \"maxOutputTokens\": 2048\n  }\n}", "options": {"response": {"response": {"neverError": true}}}}, "id": "52bf17e0-0528-470c-81ad-ae2dcbbed5ae", "name": "Generate Individual Ideas", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 0], "credentials": {"googlePalmApi": {"id": "mfmYziwwRaDXEvbP", "name": "Google Gemini(PaLM) Api account 2"}}}, {"parameters": {"jsCode": "// Generate 2 unique blog idea requests with clean formatting\nconst items = $input.all();\nconst uniqueRequests = [];\n\n// Comprehensive keyword categories for solar industry\nconst keywordCategories = [\n  // Technical Keywords\n  'solar panel efficiency optimization',\n  'photovoltaic cell technology advancement',\n  'inverter performance monitoring',\n  'battery storage system integration',\n  'grid-tie solar installation methods',\n  'microinverter vs power optimizer comparison',\n  'solar tracking system benefits',\n  'bifacial solar panel advantages',\n  'string inverter troubleshooting guide',\n  'DC optimizer installation techniques',\n  \n  // Commercial Keywords\n  'commercial solar ROI calculation',\n  'industrial solar maintenance protocols',\n  'warehouse solar installation planning',\n  'office building solar solutions',\n  'retail store solar energy systems',\n  'manufacturing facility solar design',\n  'hospital solar power implementation',\n  'school solar installation programs',\n  'apartment complex solar solutions',\n  'shopping mall solar energy integration',\n  \n  // Residential Keywords\n  'home solar panel sizing guide',\n  'residential solar financing options',\n  'rooftop solar installation process',\n  'solar panel maintenance schedule',\n  'home battery backup systems',\n  'residential solar permit requirements',\n  'solar panel warranty comparison',\n  'home energy audit for solar',\n  'solar panel snow removal methods',\n  'residential solar monitoring apps',\n  \n  // Financial Keywords\n  'solar investment tax credit guide',\n  'solar lease vs purchase analysis',\n  'solar PPA agreement terms',\n  'solar loan financing rates',\n  'solar panel depreciation schedules',\n  'renewable energy certificate trading',\n  'solar insurance coverage options',\n  'solar system appraisal methods',\n  'solar financing for bad credit',\n  'solar cost per watt analysis',\n  \n  // Regional/Seasonal Keywords\n  'winter solar panel performance',\n  'summer solar energy production',\n  'desert solar installation challenges',\n  'coastal solar corrosion protection',\n  'mountain solar installation altitude',\n  'urban solar shading solutions',\n  'rural solar grid connection',\n  'tropical climate solar considerations',\n  'northern latitude solar angles'\n];\n\n// Article formats for variety\nconst articleFormats = [\n  'Technical Deep Dive',\n  'Complete Guide',\n  'Step-by-Step Tutorial',\n  'Comparison Analysis',\n  'Case Study',\n  'Best Practices Guide',\n  'Troubleshooting Manual',\n  'Cost Benefit Analysis',\n  'Implementation Roadmap',\n  'Performance Review',\n  'Market Analysis',\n  'Future Trends Report',\n  'Expert Interview',\n  'Success Story',\n  'Research Summary'\n];\n\n// Target audiences\nconst audiences = [\n  'Solar Engineers',\n  'Homeowners',\n  'Business Owners',\n  'Solar Installers',\n  'Energy Consultants',\n  'Facility Managers',\n  'Property Developers',\n  'Contractors',\n  'Electricians',\n  'Energy Auditors',\n  'Financial Analysts',\n  'Policy Makers',\n  'Environmental Scientists',\n  'Project Managers',\n  'Maintenance Technicians'\n];\n\n// Objectives\nconst objectives = [\n  'technical education',\n  'cost analysis',\n  'implementation guidance',\n  'performance optimization',\n  'troubleshooting support',\n  'decision making support',\n  'compliance guidance',\n  'maintenance instruction',\n  'financial planning',\n  'technology comparison',\n  'market intelligence',\n  'risk assessment',\n  'efficiency improvement',\n  'safety compliance',\n  'environmental impact'\n];\n\n// Clean word count options (round figures)\nconst wordCountOptions = [\n  800, 900, 1000, 1200, 1400, 1500, 1600, 1800, 2000, 2200, 2400, 2500\n];\n\n// Add randomization factors\nconst timestamp = Date.now();\nconst randomSeed = Math.floor(Math.random() * 1000000);\n\n// Generate only 2 unique requests\nfor (let i = 0; i < 2; i++) {\n  const item = items[0]; // Use first item from Google Sheet\n  const companyData = item.json;\n  \n  // Create randomized indices using timestamp and random seed\n  const keywordIndex = (timestamp + randomSeed + i * 17) % keywordCategories.length;\n  const formatIndex = (timestamp + randomSeed + i * 23) % articleFormats.length;\n  const audienceIndex = (timestamp + randomSeed + i * 31) % audiences.length;\n  const objectiveIndex = (timestamp + randomSeed + i * 37) % objectives.length;\n  const wordCountIndex = (timestamp + randomSeed + i * 41) % wordCountOptions.length;\n  \n  // Add uniqueness factors\n  const randomId = Math.random().toString(36).substring(2, 8);\n  \n  // Use clean focus keyword without date/time stamps\n  const focusKeyword = keywordCategories[keywordIndex];\n  \n  // Create unique angle without date references\n  const uniqueAngle = `Advanced technical perspective on ${focusKeyword}`;\n  \n  const uniqueRequest = {\n    itemIndex: i,\n    companyName: companyData['Company Name'] || 'WattMonk',\n    services: companyData['Services Offered '] || 'Solar Installation',\n    overview: companyData['Service Overview'] || 'Solar Solutions',\n    about: companyData['About The Company'] || 'Solar Energy Company',\n    \n    // Clean parameters\n    focusKeyword: focusKeyword,\n    articleFormat: articleFormats[formatIndex],\n    targetAudience: audiences[audienceIndex],\n    objective: objectives[objectiveIndex],\n    wordCount: wordCountOptions[wordCountIndex], // Clean round numbers\n    uniqueAngle: uniqueAngle,\n    \n    // Internal tracking (not visible in output)\n    executionId: `exec_${timestamp}_${i}_${randomId}`,\n    timestamp: new Date().toISOString(),\n    randomSeed: randomSeed,\n    uniqueIdentifier: `${focusKeyword.replace(/\\s+/g, '_')}_${i}_${timestamp}_${randomId}_${randomSeed}`\n  };\n  \n  uniqueRequests.push(uniqueRequest);\n}\n\nconsole.log(`Generated ${uniqueRequests.length} unique blog idea requests`);\nconsole.log('Sample keywords:', uniqueRequests.map(r => r.focusKeyword));\nconsole.log('Word counts:', uniqueRequests.map(r => r.wordCount));\n\nreturn uniqueRequests;"}, "id": "fc280569-66c7-4891-8f7f-835d6c4c141c", "name": "Generate Requests", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 0]}, {"parameters": {"jsCode": "// FIXED: Process individual Gemini responses to ensure 49 unique outputs\nconst items = $input.all();\nconst finalUniqueIdeas = [];\n\nconsole.log(`Processing ${items.length} Gemini responses`);\n\nfor (let i = 0; i < items.length; i++) {\n  const item = items[i];\n  \n  try {\n    // Get the original request data with better error handling\n    let requestData = {};\n    \n    // Try multiple methods to get request data\n    try {\n      // Method 1: Direct node reference\n      requestData = $('Generate Requests').item(i).json;\n    } catch (error1) {\n      try {\n        // Method 2: Get from previous execution data\n        const allRequests = $('Generate Requests').all();\n        if (allRequests && allRequests[i]) {\n          requestData = allRequests[i].json;\n        } else {\n          throw new Error('No request data found');\n        }\n      } catch (error2) {\n        // Method 3: Create fallback request data\n        console.warn(`Creating fallback request data for item ${i + 1}`);\n        requestData = {\n          itemIndex: i,\n          companyName: 'WattMonk',\n          services: 'Solar Installation Services',\n          focusKeyword: `unique solar analysis ${i + 1} ${Date.now()}`,\n          articleFormat: 'Technical Guide',\n          targetAudience: 'Solar Engineers',\n          objective: 'technical education',\n          wordCount: 1500 + (i * 20),\n          uniqueAngle: `Advanced technical perspective ${i + 1}`,\n          executionId: `fallback_${i}_${Date.now()}`,\n          uniqueIdentifier: `fallback_${i}_${Date.now()}_${Math.random().toString(36).substring(2, 6)}`,\n          quarterYear: `Q${Math.floor(new Date().getMonth() / 3) + 1}_${new Date().getFullYear()}`\n        };\n      }\n    }\n    \n    const geminiResponse = item.json;\n    let blogIdea = null;\n    \n    // Enhanced Gemini response parsing\n    if (geminiResponse?.candidates?.[0]?.content?.parts?.[0]?.text) {\n      let responseText = geminiResponse.candidates[0].content.parts[0].text;\n      \n      console.log(`Processing Gemini response ${i + 1}, length: ${responseText.length}`);\n      \n      // Clean response text more thoroughly\n      responseText = responseText\n        .replace(/```json\\s*/gi, '')\n        .replace(/```\\s*/g, '')\n        .replace(/^[^{]*/, '') // Remove text before first {\n        .replace(/[^}]*$/, '}') // Ensure ends with }\n        .trim();\n      \n      // Multiple JSON extraction methods\n      let jsonText = '';\n      \n      // Method 1: Direct parse if starts with {\n      if (responseText.startsWith('{')) {\n        jsonText = responseText;\n      } else {\n        // Method 2: Find JSON object boundaries\n        const jsonStart = responseText.indexOf('{');\n        const jsonEnd = responseText.lastIndexOf('}') + 1;\n        \n        if (jsonStart !== -1 && jsonEnd > jsonStart) {\n          jsonText = responseText.substring(jsonStart, jsonEnd);\n        }\n      }\n      \n      // Try to parse JSON\n      if (jsonText) {\n        try {\n          blogIdea = JSON.parse(jsonText);\n          console.log(`✓ Successfully parsed idea ${i + 1}: ${blogIdea.focusKeyword || 'no keyword'}`);\n          \n          // Validate required fields\n          if (!blogIdea.focusKeyword || !blogIdea.articleFormat) {\n            console.warn(`Incomplete blog idea ${i + 1}, using fallback`);\n            blogIdea = null;\n          }\n        } catch (parseError) {\n          console.error(`JSON parsing failed for item ${i + 1}:`, parseError.message);\n          console.log('JSON text snippet:', jsonText.substring(0, 200) + '...');\n          \n          // Try to extract key fields manually\n          blogIdea = extractFieldsManually(responseText, requestData);\n        }\n      }\n    } else {\n      console.warn(`No valid Gemini response for item ${i + 1}`);\n    }\n    \n    // If parsing failed completely, create guaranteed unique idea from request data\n    if (!blogIdea) {\n      console.log(`Creating structured fallback idea for item ${i + 1}`);\n      blogIdea = createStructuredFallback(requestData, i);\n    }\n    \n    // Ensure all required fields exist with fallbacks\n    const uniqueBlogEntry = {\n      focusKeyword: blogIdea.focusKeyword || requestData.focusKeyword || `unique solar topic ${i + 1}`,\n      title: blogIdea.title || `${requestData.articleFormat || 'Complete Guide'}: ${requestData.focusKeyword || 'Solar Analysis'}`,\n      articleFormat: blogIdea.articleFormat || requestData.articleFormat || 'Technical Guide',\n      wordCount: validateWordCount(blogIdea.wordCount) || requestData.wordCount || (1500 + i * 20),\n      targetAudience: blogIdea.targetAudience || requestData.targetAudience || 'Solar Engineers',\n      objective: blogIdea.objective || requestData.objective || 'technical education',\n      uniqueAngle: blogIdea.uniqueAngle || requestData.uniqueAngle || `Unique perspective ${i + 1}`,\n      outline: ensureArray(blogIdea.outline) || [\n        'Introduction and Market Overview',\n        'Technical Specifications',\n        'Implementation Strategy',\n        'Best Practices and Guidelines',\n        'Cost-Benefit Analysis',\n        'Case Studies and Examples',\n        'Future Trends and Conclusions'\n      ],\n      keyTakeaways: ensureArray(blogIdea.keyTakeaways) || [\n        `Key insights into ${requestData.focusKeyword || 'solar technology'}`,\n        'Implementation best practices',\n        'Cost-benefit considerations'\n      ],\n      examples: ensureArray(blogIdea.examples) || [\n        'Real-world implementation case study',\n        'Performance metrics analysis'\n      ],\n      callToAction: blogIdea.callToAction || `Contact ${requestData.companyName || 'our team'} for professional consultation`,\n      \n      // Metadata for tracking and uniqueness\n      companyName: requestData.companyName || 'WattMonk',\n      itemNumber: i + 1,\n      executionId: requestData.executionId || `exec_${Date.now()}_${i}`,\n      uniqueIdentifier: requestData.uniqueIdentifier || `unique_${i}_${Date.now()}`,\n      generatedAt: new Date().toISOString(),\n      quarterYear: requestData.quarterYear || `Q${Math.floor(new Date().getMonth() / 3) + 1}_${new Date().getFullYear()}`,\n      originalIndex: i,\n      processingMethod: blogIdea ? 'gemini_parsed' : 'fallback_generated'\n    };\n    \n    finalUniqueIdeas.push(uniqueBlogEntry);\n    \n  } catch (error) {\n    console.error(`Critical error processing item ${i + 1}:`, error);\n    \n    // Emergency unique fallback with guaranteed uniqueness\n    const emergencyId = `emergency_${i}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;\n    \n    const emergencyEntry = {\n      focusKeyword: `emergency solar analysis ${i + 1} ${emergencyId}`,\n      title: `Emergency Solar Analysis ${i + 1}`,\n      articleFormat: 'Emergency Technical Guide',\n      wordCount: 1500,\n      targetAudience: 'Solar Engineers',\n      objective: 'emergency technical education',\n      uniqueAngle: 'Emergency generated comprehensive analysis',\n      outline: [\n        'Emergency Technical Overview',\n        'Critical Analysis Points',\n        'Implementation Guidance',\n        'Next Steps and Recommendations'\n      ],\n      keyTakeaways: [\n        'Critical technical insights',\n        'Emergency best practices'\n      ],\n      examples: [\n        'Emergency case study example'\n      ],\n      callToAction: 'Contact for emergency technical consultation',\n      companyName: 'WattMonk',\n      itemNumber: i + 1,\n      executionId: emergencyId,\n      uniqueIdentifier: emergencyId,\n      generatedAt: new Date().toISOString(),\n      quarterYear: `Q${Math.floor(new Date().getMonth() / 3) + 1}_${new Date().getFullYear()}`,\n      originalIndex: i,\n      isEmergency: true,\n      processingMethod: 'emergency_fallback',\n      error: error.message\n    };\n    \n    finalUniqueIdeas.push(emergencyEntry);\n  }\n}\n\n// Helper functions\nfunction extractFieldsManually(text, requestData) {\n  try {\n    // Try to extract key information manually from the text\n    const focusKeywordMatch = text.match(/\"focusKeyword\":\\s*\"([^\"]+)\"/);\n    const titleMatch = text.match(/\"title\":\\s*\"([^\"]+)\"/);\n    const formatMatch = text.match(/\"articleFormat\":\\s*\"([^\"]+)\"/);\n    \n    return {\n      focusKeyword: focusKeywordMatch ? focusKeywordMatch[1] : requestData.focusKeyword,\n      title: titleMatch ? titleMatch[1] : `Guide to ${requestData.focusKeyword}`,\n      articleFormat: formatMatch ? formatMatch[1] : requestData.articleFormat,\n      wordCount: requestData.wordCount,\n      targetAudience: requestData.targetAudience,\n      objective: requestData.objective,\n      uniqueAngle: requestData.uniqueAngle\n    };\n  } catch (error) {\n    console.error('Manual extraction failed:', error);\n    return null;\n  }\n}\n\nfunction createStructuredFallback(requestData, index) {\n  return {\n    focusKeyword: requestData.focusKeyword || `advanced solar analysis ${index + 1}`,\n    title: `Complete ${requestData.articleFormat || 'Guide'}: ${requestData.focusKeyword || 'Solar Technology'}`,\n    articleFormat: requestData.articleFormat || 'Technical Guide',\n    wordCount: requestData.wordCount || (1500 + index * 20),\n    targetAudience: requestData.targetAudience || 'Solar Engineers',\n    objective: requestData.objective || 'technical education',\n    uniqueAngle: requestData.uniqueAngle || `Advanced technical analysis perspective`,\n    outline: [\n      'Introduction and Technology Overview',\n      'Technical Specifications and Requirements',\n      'Implementation and Installation Process',\n      'Performance Optimization Strategies',\n      'Cost Analysis and ROI Calculations',\n      'Case Studies and Real-World Applications',\n      'Future Developments and Conclusions'\n    ],\n    keyTakeaways: [\n      `Comprehensive understanding of ${requestData.focusKeyword || 'solar technology'}`,\n      'Implementation best practices and guidelines',\n      'Cost-benefit analysis and ROI insights'\n    ],\n    examples: [\n      'Detailed implementation case study',\n      'Performance metrics and analysis'\n    ],\n    callToAction: `Contact ${requestData.companyName || 'our experts'} for professional consultation and implementation`\n  };\n}\n\nfunction validateWordCount(wordCount) {\n  if (typeof wordCount === 'number' && wordCount >= 1000 && wordCount <= 5000) {\n    return wordCount;\n  }\n  if (typeof wordCount === 'string') {\n    const parsed = parseInt(wordCount.replace(/[^\\d]/g, ''), 10);\n    if (!isNaN(parsed) && parsed >= 1000 && parsed <= 5000) {\n      return parsed;\n    }\n  }\n  return null;\n}\n\nfunction ensureArray(value) {\n  if (Array.isArray(value) && value.length > 0) {\n    return value;\n  }\n  return null;\n}\n\n// Final validation and logging\nconsole.log(`✓ Generated ${finalUniqueIdeas.length} final unique blog ideas`);\nif (finalUniqueIdeas.length > 0) {\n  console.log('Sample keywords:', finalUniqueIdeas.slice(0, 3).map(idea => idea.focusKeyword));\n}\n\n// Verify uniqueness\nconst keywords = finalUniqueIdeas.map(idea => idea.focusKeyword);\nconst uniqueKeywords = new Set(keywords);\nconsole.log(`Uniqueness check: ${uniqueKeywords.size} unique keywords out of ${keywords.length} total`);\n\nif (uniqueKeywords.size !== keywords.length) {\n  console.warn('⚠️ Duplicate keywords detected! Adding additional uniqueness...');\n  \n  // Add additional uniqueness to any duplicates\n  const seenKeywords = new Set();\n  finalUniqueIdeas.forEach((idea, index) => {\n    let keyword = idea.focusKeyword;\n    let counter = 1;\n    \n    while (seenKeywords.has(keyword.toLowerCase())) {\n      keyword = `${idea.focusKeyword} variant ${counter}`;\n      counter++;\n    }\n    \n    seenKeywords.add(keyword.toLowerCase());\n    idea.focusKeyword = keyword;\n  });\n  \n  console.log('✓ Uniqueness enforced for all entries');\n}\n\n// Return the processed items\nreturn finalUniqueIdeas.map(idea => ({ json: idea }));"}, "id": "77f1decf-e59a-4bd1-ac45-5f55bd1d4d2d", "name": "Process Responses", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 0]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1Apb72AR5glFViGd3O9DIa29ICMzrk35K-Io_czPSQSA", "mode": "list", "cachedResultName": "WattMonk Blog Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Apb72AR5glFViGd3O9DIa29ICMzrk35K-Io_czPSQSA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 1346435451, "mode": "list", "cachedResultName": "Automated Keywords", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Apb72AR5glFViGd3O9DIa29ICMzrk35K-Io_czPSQSA/edit#gid=1346435451"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "Focus Keyword", "displayName": "Focus Keyword", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Article Format", "displayName": "Article Format", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Word Count", "displayName": "Word Count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Target Audience", "displayName": "Target Audience", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Objective", "displayName": "Objective", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "fd789434-d63b-47dd-b4dd-11a1107e9b92", "name": "Save Ideas", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [1000, 0], "credentials": {"googleSheetsOAuth2Api": {"id": "TXBfVPVDwrhp999t", "name": "Google Sheets account"}}}, {"parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [1], "triggerAtHour": 9}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "d9bcf3d7-cb2c-458b-8dea-5587787d09ec", "name": "Schedule Trigger"}], "pinData": {}, "connections": {"Read Company KT Sheet": {"main": [[{"node": "Generate Requests", "type": "main", "index": 0}]]}, "Generate Individual Ideas": {"main": [[{"node": "Process Responses", "type": "main", "index": 0}]]}, "Generate Requests": {"main": [[{"node": "Generate Individual Ideas", "type": "main", "index": 0}]]}, "Process Responses": {"main": [[{"node": "Save Ideas", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Read Company KT Sheet", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "b4aaaf41-2097-4b7e-abdf-49e8eadb306d", "meta": {"instanceId": "bd13a791ef83e2ae6563714d8025574c31f1bfadf3ea50d267483e93fe42f56c"}, "id": "Y3b6Voq0I1srXxk6", "tags": []}