export interface Company {
  id: string
  companyName: string
  servicesOffered: string
  serviceOverview: string
  aboutTheCompany: string
}

export interface Draft {
  id: string
  userId: string
  companyData: Company
  selectedKeyword?: string
  keywordSuggestions?: {
    manual: Keyword[]
    auto: Keyword[]
    total: Keyword[]
  }
  competitorAnalysis?: CompetitorAnalysis[]
  keywordCluster?: KeywordCluster[]
  trends?: TrendAnalysis[]
  metaOptions?: MetaOption[]
  finalMeta?: MetaOption
  structuredContent?: BlogBlock[]
  status: "keyword_selection" | "meta_generation" | "content_generation" | "link_generation" | "review" | "published"
  createdAt: string
  updatedAt: string
}

export interface Keyword {
  id?: string
  focusKeyword: string
  articleFormat: string
  wordCount: string
  targetAudience: string
  objective: string
  source?: "manual" | "ai"
}

export interface CompetitorAnalysis {
  domain: string
  title: string
  domainAuthority: number
  wordCount: number
  seoScore: number
}

export interface KeywordCluster {
  keyword: string
  searchVolume: number
  difficulty: number
  relevanceScore: number
}

export interface TrendAnalysis {
  topic: string
  description: string
  direction: "up" | "down"
  confidence: number
}

export interface MetaOption {
  h1Title: string
  metaTitle: string
  metaDescription: string
  scores: {
    keywordScore: number
    lengthScore: number
    readabilityScore: number
    trendScore: number
    totalScore: number
  }
  keywordsIncluded: string[]
}

export interface BlogBlock {
  id: string
  type: "introduction" | "section" | "image" | "conclusion" | "references"
  content?: string
  h2?: string
  imageType?: "feature" | "in-blog"
  alt?: string
  editable: boolean
  wordCount?: number
}

export interface InternalLink {
  anchorText: string
  targetUrl: string
  context: string
  relevance: number
}

export interface ExternalLink {
  anchorText: string
  targetDomain: string
  context: string
  relevance: number
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface StartBlogResponse {
  success: boolean
  draftId: string
  companyData: Company
  keywordSuggestions: {
    manual: Keyword[]
    auto: Keyword[]
    total: Keyword[]
  }
  message: string
}

export interface KeywordAnalysisResponse {
  success: boolean
  competitorAnalysis: CompetitorAnalysis[]
  keywordCluster: KeywordCluster[]
  trends: TrendAnalysis[]
  message: string
}

export interface MetaGenerationResponse {
  success: boolean
  metaOptions: MetaOption[]
  message: string
}

export interface ContentGenerationResponse {
  success: boolean
  structuredContent: BlogBlock[]
  message: string
}
