const { model } = require('../config/gemini.config');

class GeminiService {
  async generateKeywordSuggestions(companyData, existingKeywords = []) {
    try {
      const currentYear = new Date().getFullYear(); // Dynamic year
      const prompt = `
        Current Year: ${currentYear}
        Company: ${companyData.companyName}
        Services: ${companyData.servicesOffered}
        About: ${companyData.aboutTheCompany}
        
        Existing keywords: ${existingKeywords.map(k => k.focusKeyword).join(', ')}
        
        Generate 2 new blog keyword suggestions for this solar company that are:
        1. SEO-friendly and relevant to their services in ${currentYear}
        2. Different from existing keywords
        3. Focused on their target audience (solar installers, contractors)
        4. Include current year ${currentYear} where relevant
        
        Return ONLY a valid JSON array with exactly 2 objects. Each object must have these fields:
        - focusKeyword: the keyword phrase
        - articleFormat: "How To" or "Guide" or "List"
        - wordCount: suggested word count (between 1200-2000)
        - targetAudience: specific audience segment
        - objective: main goal of the article
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      console.log('Gemini raw response:', text);
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/$$[\s\S]*$$/);
        
        if (jsonMatch) {
          const keywords = JSON.parse(jsonMatch[0]);
          return keywords.slice(0, 2);
        }
      } catch (parseError) {
        console.error('JSON parsing error:', parseError);
      }
      
      // Updated fallback keywords with current year
      return [
        {
          focusKeyword: `solar installation best practices ${currentYear}`,
          articleFormat: "Guide",
          wordCount: "1500",
          targetAudience: "Solar Installers",
          objective: "Educate on latest installation techniques"
        },
        {
          focusKeyword: "solar permit approval tips",
          articleFormat: "How To",
          wordCount: "1800",
          targetAudience: "Solar Contractors",
          objective: "Speed up permit approval process"
        }
      ];
    } catch (error) {
      console.error('Error generating keywords:', error);
      const currentYear = new Date().getFullYear();
      return [
        {
          focusKeyword: "solar panel maintenance guide",
          articleFormat: "How To",
          wordCount: "1200",
          targetAudience: "Solar Technicians",
          objective: "Improve maintenance procedures"
        },
        {
          focusKeyword: `commercial solar project planning ${currentYear}`,
          articleFormat: "Guide",
          wordCount: "2000",
          targetAudience: "Project Managers",
          objective: "Streamline project execution"
        }
      ];
    }
  }

  async generateMetaTags(keyword, companyData) {
    try {
      const currentYear = new Date().getFullYear();
      const prompt = `
        Current Year: ${currentYear}
        Company: ${companyData.companyName}
        Services: ${companyData.servicesOffered}
        Keyword: ${keyword.focusKeyword}
        Article Format: ${keyword.articleFormat}
        
        Generate 3 different sets of SEO-optimized meta tags for a blog post in ${currentYear}.
        
        Requirements:
        - H1 Title: 50-60 characters, include keyword naturally
        - Meta Title: 50-60 characters, can be same as H1
        - Meta Description: 150-160 characters, compelling and includes keyword
        - Make it current and relevant for ${currentYear}
        
        Return ONLY a valid JSON array with exactly 3 objects.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      console.log('Gemini meta response:', text);
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/$$[\s\S]*$$/);
        
        if (jsonMatch) {
          const metaTags = JSON.parse(jsonMatch[0]);
          return metaTags.slice(0, 3);
        }
      } catch (parseError) {
        console.error('Meta JSON parsing error:', parseError);
      }
      
      const fallbackTitle = `${keyword.focusKeyword} - Expert Guide`;
      return [
        {
          h1Title: fallbackTitle.substring(0, 60),
          metaTitle: fallbackTitle.substring(0, 60),
          metaDescription: `Learn everything about ${keyword.focusKeyword} in ${currentYear}. Expert insights from ${companyData.companyName} to help solar professionals succeed.`.substring(0, 160)
        },
        {
          h1Title: `Complete ${keyword.focusKeyword} Guide ${currentYear}`,
          metaTitle: `Complete ${keyword.focusKeyword} Guide ${currentYear}`,
          metaDescription: `Master ${keyword.focusKeyword} with proven strategies. ${companyData.companyName} shares industry best practices and insider tips.`.substring(0, 160)
        },
        {
          h1Title: `${keyword.focusKeyword}: Professional Tips`,
          metaTitle: `${keyword.focusKeyword}: Professional Tips`,
          metaDescription: `Professional guide to ${keyword.focusKeyword}. Trusted advice from ${companyData.companyName} for solar industry professionals.`.substring(0, 160)
        }
      ];
    } catch (error) {
      console.error('Error generating meta tags:', error);
      throw error;
    }
  }

  async generateBlogContent(metaData, keyword, companyData) {
    try {
      const currentYear = new Date().getFullYear();
      const prompt = `
        Current Year: ${currentYear}
        Create a structured blog post with these details:
        Title: ${metaData.h1Title}
        Keyword: ${keyword.focusKeyword}
        Word Count Target: ${keyword.wordCount}
        Company: ${companyData.companyName}
        Target Audience: ${keyword.targetAudience}
        
        Generate a comprehensive blog post with:
        1. Introduction paragraph (100-150 words) that includes the keyword naturally
        2. 4-5 H2 sections with descriptive titles
        3. Content for each section (200-300 words each)
        4. Include placeholder for a feature image after introduction
        5. Include placeholders for 2 in-content images (one after 2nd section, one after 4th section)
        6. Conclusion (100-150 words) with a call to action
        
        Make it informative, professional, current for ${currentYear}, and valuable for ${keyword.targetAudience}.
        
        Return ONLY valid JSON in this exact format:
        {
          "introduction": "Introduction text here...",
          "featureImage": {
            "alt": "Descriptive alt text for main image",
            "caption": "Image caption",
            "description": "What the image should show"
          },
          "sections": [
            {
              "h2": "Section Title Here",
              "content": "Section content here...",
              "image": {
                "alt": "Alt text for section image",
                "caption": "Image caption",
                "description": "What the image should show",
                "afterSection": true
              }
            }
          ],
          "conclusion": "Conclusion text here..."
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      console.log('Gemini content response length:', text.length);
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Content JSON parsing error:', parseError);
      }
      
      // Fallback content structure with images
      return {
        introduction: `Welcome to our comprehensive guide on ${keyword.focusKeyword}. In today's rapidly evolving solar industry, understanding ${keyword.focusKeyword} is crucial for success in ${currentYear}. This guide will provide you with actionable insights and proven strategies to excel in your solar projects.`,
        featureImage: {
          alt: `${keyword.focusKeyword} overview`,
          caption: `Professional guide to ${keyword.focusKeyword}`,
          description: `Modern solar installation showing ${keyword.focusKeyword} in action`
        },
        sections: [
          {
            h2: `Understanding ${keyword.focusKeyword} in ${currentYear}`,
            content: `${keyword.focusKeyword} plays a vital role in the solar industry. Whether you're a seasoned professional or new to the field, mastering these concepts will significantly improve your project outcomes. Let's explore the fundamental aspects and best practices that leading professionals use.`
          },
          {
            h2: "Key Benefits and Advantages",
            content: `Implementing proper ${keyword.focusKeyword} strategies offers numerous benefits. From increased efficiency to cost savings, understanding these advantages helps you make informed decisions. Our experts at ${companyData.companyName} have identified the most impactful benefits through years of industry experience.`,
            image: {
              alt: `Benefits of ${keyword.focusKeyword}`,
              caption: "Key advantages illustrated",
              description: "Infographic showing benefits and ROI",
              afterSection: true
            }
          },
          {
            h2: "Step-by-Step Implementation Guide",
            content: `Success with ${keyword.focusKeyword} requires a systematic approach. Follow these proven steps to ensure optimal results. Each step has been refined through real-world application and feedback from industry professionals.`
          },
          {
            h2: "Common Challenges and Solutions",
            content: `Every solar project faces unique challenges. Understanding common issues related to ${keyword.focusKeyword} and their solutions prepares you for success. Learn from the experiences of others to avoid costly mistakes and delays.`,
                        image: {
              alt: `${keyword.focusKeyword} best practices`,
              caption: "Professional implementation example",
              description: "Real-world example of successful implementation",
              afterSection: true
            }
          }
        ],
        conclusion: `Mastering ${keyword.focusKeyword} is essential for success in today's solar industry. By following the strategies outlined in this guide, you'll be well-equipped to handle any project in ${currentYear}. For professional assistance and expert guidance, contact ${companyData.companyName} today.`
      };
    } catch (error) {
      console.error('Error generating blog content:', error);
      throw error;
    }
  }

  async generateImagePrompt(imageData, keyword, companyName) {
    try {
      const prompt = `
        Create a detailed image generation prompt for: ${imageData.description}
        Context: Blog about ${keyword}
        Company: ${companyName}

        Make it specific, professional, and relevant to solar industry.
        Return only the image prompt text, no extra formatting.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('Error generating image prompt:', error);
      return `Professional solar installation showing ${imageData.description}`;
    }
  }

  async generateKeywordCluster(keyword) {
    try {
      const prompt = `
        Generate a keyword cluster for the primary keyword: "${keyword}"

        Return a JSON object with:
        - secondaryKeywords: array of 5-7 related keywords with relevance scores
        - lsiKeywords: array of 8-10 LSI (Latent Semantic Indexing) keywords
        - longTailKeywords: array of 3-5 long-tail variations

        Focus on solar industry context.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);

        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Keyword cluster parsing error:', parseError);
      }

      // Fallback keyword cluster
      return {
        secondaryKeywords: [
          { keyword: 'solar installation', relevance: 0.9 },
          { keyword: 'solar design', relevance: 0.8 },
          { keyword: 'solar permits', relevance: 0.7 },
          { keyword: 'solar maintenance', relevance: 0.6 },
          { keyword: 'solar efficiency', relevance: 0.7 }
        ],
        lsiKeywords: [
          'solar panels', 'renewable energy', 'solar system', 'photovoltaic',
          'solar power', 'clean energy', 'solar technology', 'energy savings',
          'solar solutions', 'sustainable energy'
        ],
        longTailKeywords: [
          `how to ${keyword}`,
          `best practices for ${keyword}`,
          `${keyword} cost analysis`,
          `${keyword} installation guide`,
          `professional ${keyword} services`
        ]
      };
    } catch (error) {
      console.error('Error generating keyword cluster:', error);
      throw error;
    }
  }

  async getCurrentTrends(keyword) {
    try {
      const currentYear = new Date().getFullYear();
      const prompt = `
        Identify current trends in ${currentYear} related to: "${keyword}" in the solar industry.

        Return a JSON object with:
        - currentTrends: array of 5-7 trending topics
        - emergingTechnologies: array of 3-5 new technologies
        - marketTrends: array of 3-5 market developments
        - regulatoryTrends: array of 2-3 regulatory changes

        Focus on what's happening in ${currentYear}.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);

        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Trends parsing error:', parseError);
      }

      // Fallback trends
      return {
        currentTrends: [
          'AI-powered solar design',
          'Battery storage integration',
          'Smart grid connectivity',
          'Sustainability focus',
          'Cost reduction strategies',
          'Remote monitoring systems'
        ],
        emergingTechnologies: [
          'Perovskite solar cells',
          'Bifacial solar panels',
          'Solar tracking systems',
          'Microinverters',
          'Energy management systems'
        ],
        marketTrends: [
          'Residential solar growth',
          'Commercial solar adoption',
          'Utility-scale projects',
          'Energy storage demand',
          'Grid modernization'
        ],
        regulatoryTrends: [
          'Net metering policies',
          'Renewable energy standards',
          'Tax incentive changes'
        ]
      };
    } catch (error) {
      console.error('Error getting current trends:', error);
      throw error;
    }
  }
}

module.exports = new GeminiService();